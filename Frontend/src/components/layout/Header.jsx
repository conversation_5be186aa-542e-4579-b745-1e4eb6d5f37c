import React, { useState } from 'react';
import { Menu, User, LogOut, Settings, Wallet, X } from 'lucide-react';
import { Link } from 'react-router-dom';
import useAuthStore from '../../store/authStore';
import ThemeToggle from './ThemeToggle';
import LanguageToggle from './LanguageToggle';
import NotificationDropdown from '../notifications/NotificationDropdown';

const Header = ({ onMenuClick, isSidebarOpen }) => {
  const [showUserMenu, setShowUserMenu] = useState(false);
  const { user, logout } = useAuthStore();

  const handleLogout = async () => {
    await logout();
  };

  return (
    <header className="fixed top-0 left-0 right-0 h-16 flex items-center px-6 bg-gray-100 dark:bg-[#1E1E2D] shadow-sm border-b border-gray-200 dark:border-[#1E3A8A] z-50">
      {/* Left: menu + logo */}
      <div className="flex items-center space-x-4">
        <button
          onClick={onMenuClick}
          className="text-[#1E3A8A] dark:text-[#8B5CF6] hover:text-[#8B5CF6] transition-colors"
        >
          {isSidebarOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
        </button>

        <div className="flex items-center">
          <div className="flex items-center justify-center w-9 h-9 bg-[#1E3A8A] dark:bg-[#8B5CF6] rounded-lg shadow">
            <Wallet className="w-5 h-5 text-white" />
          </div>
          <span className="ml-2 text-lg font-semibold text-[#1E3A8A] dark:text-white">Finwise</span>
        </div>
      </div>

      {/* Right: toggles + notifications + user */}
      <div className="flex items-center ml-auto space-x-4">
        <LanguageToggle />
        <ThemeToggle />

        {/* Notifications */}
        <NotificationDropdown />

        {/* User Menu */}
        <div className="relative">
          <button
            onClick={() => setShowUserMenu(!showUserMenu)}
            className="flex items-center space-x-2 text-[#1E3A8A] dark:text-white hover:text-[#8B5CF6] transition-colors"
          >
            <div className="h-9 w-9 rounded-full dark:bg-[#8B5CF6] bg-[#1E3A8A] flex items-center justify-center">
              <User className="h-5 w-5 text-white" />
            </div>
            <span className="hidden md:block text-sm font-medium">{user?.name || 'User'}</span>
          </button>

          {showUserMenu && (
            <div className="absolute right-0 mt-2 w-52 bg-white dark:bg-[#1E1E2D] rounded-xl shadow-lg border border-gray-200 dark:border-[#1E3A8A] overflow-hidden z-50">
              <Link
                to="/profile"
                className="flex items-center px-4 py-3 text-sm text-[#1E3A8A] dark:text-white hover:bg-[#8B5CF6] hover:text-white transition-colors"
                onClick={() => setShowUserMenu(false)}
              >
                <User className="h-4 w-4 mr-3" />
                Profile
              </Link>
              <Link
                to="/settings"
                className="flex items-center px-4 py-3 text-sm text-[#1E3A8A] dark:text-white hover:bg-[#8B5CF6] hover:text-white transition-colors"
                onClick={() => setShowUserMenu(false)}
              >
                <Settings className="h-4 w-4 mr-3" />
                Settings
              </Link>
              <hr className="border-gray-200 dark:border-[#636160]" />
              <button
                onClick={handleLogout}
                className="flex items-center w-full px-4 py-3 text-sm text-[#EF4444] hover:bg-[#FEE2E2] dark:hover:bg-[#EF4444] dark:hover:text-white transition-colors"
              >
                <LogOut className="h-4 w-4 mr-3" />
                Sign out
              </button>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
