import { create } from 'zustand';
import { ocrAPI } from '../services/api';
import toast from 'react-hot-toast';

const useOCRStore = create((set, get) => ({
  // State
  isScanning: false,
  scanResult: null,
  error: null,
  serviceStatus: null,

  // Actions
  scanReceipt: async (imageFile, options = {}) => {
    set({ isScanning: true, error: null, scanResult: null });
    
    try {
      const response = await ocrAPI.scanReceipt(imageFile);
      const result = response.data.data;

      set({
        isScanning: false,
        scanResult: result,
        error: null
      });

      toast.success('Receipt scanned successfully!');
      return { success: true, data: result };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to scan receipt';
      
      set({
        isScanning: false,
        error: errorMessage,
        scanResult: null
      });

      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  checkServiceStatus: async () => {
    try {
      const response = await ocrAPI.getStatus();
      const status = response.data.data;

      set({ serviceStatus: status });
      return { success: true, data: status };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to check OCR service status';
      
      set({ serviceStatus: null });
      return { success: false, error: errorMessage };
    }
  },

  clearScanResult: () => {
    set({ scanResult: null, error: null });
  },

  clearError: () => {
    set({ error: null });
  },

  setLoading: (loading) => {
    set({ isScanning: loading });
  }
}));

export default useOCRStore;
