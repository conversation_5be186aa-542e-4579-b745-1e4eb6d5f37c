const { Category } = require('../models');
const { Op } = require('sequelize');

// @desc    Get all categories
// @route   GET /api/categories
// @access  Private
const getCategories = async (req, res, next) => {
  try {
    const { type, active = 'true', search } = req.query;

    // Build where clause
    const whereClause = {};

    // Filter by type (income/expense)
    if (type && ['income', 'expense'].includes(type)) {
      whereClause.type = type;
    }

    // Filter by active status
    if (active === 'true') {
      whereClause.is_active = true;
    }

    // Search in name
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { name_km: { [Op.like]: `%${search}%` } }
      ];
    }

    const categories = await Category.findAll({
      where: whereClause,
      order: [['sort_order', 'ASC'], ['name', 'ASC']]
    });

    res.json({
      success: true,
      data: categories
    });

  } catch (error) {
    next(error);
  }
};

// @desc    Get single category
// @route   GET /api/categories/:id
// @access  Private
const getCategory = async (req, res, next) => {
  try {
    const { id } = req.params;

    const category = await Category.findByPk(id);

    if (!category) {
      return res.status(404).json({
        success: false,
        error: 'Category not found'
      });
    }

    res.json({
      success: true,
      data: category
    });

  } catch (error) {
    next(error);
  }
};

// @desc    Create new category
// @route   POST /api/categories
// @access  Private (Admin only in future)
const createCategory = async (req, res, next) => {
  try {
    const {
      name,
      name_km,
      type,
      icon,
      color,
      description,
      description_km,
      sort_order = 0
    } = req.body;

    // Check if category with same name already exists
    const existingCategory = await Category.findOne({
      where: {
        [Op.or]: [
          { name: name.trim() },
          ...(name_km ? [{ name_km: name_km.trim() }] : [])
        ]
      }
    });

    if (existingCategory) {
      return res.status(409).json({
        success: false,
        error: 'Category with this name already exists'
      });
    }

    const category = await Category.create({
      name: name.trim(),
      name_km: name_km?.trim(),
      type,
      icon,
      color,
      description: description?.trim(),
      description_km: description_km?.trim(),
      sort_order,
      is_default: false,
      is_active: true
    });

    res.status(201).json({
      success: true,
      message: 'Category created successfully',
      data: category
    });

  } catch (error) {
    next(error);
  }
};

// @desc    Update category
// @route   PUT /api/categories/:id
// @access  Private (Admin only in future)
const updateCategory = async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      name,
      name_km,
      type,
      icon,
      color,
      description,
      description_km,
      is_active,
      sort_order
    } = req.body;

    const category = await Category.findByPk(id);

    if (!category) {
      return res.status(404).json({
        success: false,
        error: 'Category not found'
      });
    }

    // Check if updating name conflicts with existing category
    if (name && name.trim() !== category.name) {
      const existingCategory = await Category.findOne({
        where: {
          name: name.trim(),
          id: { [Op.ne]: id }
        }
      });

      if (existingCategory) {
        return res.status(409).json({
          success: false,
          error: 'Category with this name already exists'
        });
      }
    }

    // Update category
    await category.update({
      ...(name && { name: name.trim() }),
      ...(name_km !== undefined && { name_km: name_km?.trim() }),
      ...(type && { type }),
      ...(icon !== undefined && { icon }),
      ...(color !== undefined && { color }),
      ...(description !== undefined && { description: description?.trim() }),
      ...(description_km !== undefined && { description_km: description_km?.trim() }),
      ...(is_active !== undefined && { is_active }),
      ...(sort_order !== undefined && { sort_order })
    });

    res.json({
      success: true,
      message: 'Category updated successfully',
      data: category
    });

  } catch (error) {
    next(error);
  }
};

// @desc    Delete category
// @route   DELETE /api/categories/:id
// @access  Private (Admin only in future)
const deleteCategory = async (req, res, next) => {
  try {
    const { id } = req.params;

    const category = await Category.findByPk(id);

    if (!category) {
      return res.status(404).json({
        success: false,
        error: 'Category not found'
      });
    }

    // Check if category is default (cannot be deleted)
    if (category.is_default) {
      return res.status(400).json({
        success: false,
        error: 'Default categories cannot be deleted'
      });
    }

    // Check if category has associated transactions
    const { Transaction } = require('../models');
    const transactionCount = await Transaction.count({
      where: { category_id: id }
    });

    if (transactionCount > 0) {
      return res.status(400).json({
        success: false,
        error: `Cannot delete category. It has ${transactionCount} associated transactions.`,
        suggestion: 'Consider deactivating the category instead.'
      });
    }

    await category.destroy();

    res.json({
      success: true,
      message: 'Category deleted successfully'
    });

  } catch (error) {
    next(error);
  }
};

// @desc    Get category statistics
// @route   GET /api/categories/:id/stats
// @access  Private
const getCategoryStats = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const category = await Category.findByPk(id);

    if (!category) {
      return res.status(404).json({
        success: false,
        error: 'Category not found'
      });
    }

    const { Transaction } = require('../models');

    // Get transaction statistics for this category
    const stats = await Transaction.findAll({
      where: {
        category_id: id,
        user_id: userId
      },
      attributes: [
        [Transaction.sequelize.fn('COUNT', Transaction.sequelize.col('id')), 'transactionCount'],
        [Transaction.sequelize.fn('SUM', Transaction.sequelize.col('money_in')), 'totalIncome'],
        [Transaction.sequelize.fn('SUM', Transaction.sequelize.col('money_out')), 'totalExpense'],
        [Transaction.sequelize.fn('MAX', Transaction.sequelize.col('date')), 'lastTransactionDate'],
        [Transaction.sequelize.fn('MIN', Transaction.sequelize.col('date')), 'firstTransactionDate']
      ],
      raw: true
    });

    const result = stats[0];

    res.json({
      success: true,
      data: {
        category,
        statistics: {
          transactionCount: parseInt(result.transactionCount) || 0,
          totalIncome: parseFloat(result.totalIncome) || 0,
          totalExpense: parseFloat(result.totalExpense) || 0,
          netAmount: (parseFloat(result.totalIncome) || 0) - (parseFloat(result.totalExpense) || 0),
          lastTransactionDate: result.lastTransactionDate,
          firstTransactionDate: result.firstTransactionDate
        }
      }
    });

  } catch (error) {
    next(error);
  }
};

module.exports = {
  getCategories,
  getCategory,
  createCategory,
  updateCategory,
  deleteCategory,
  getCategoryStats
};
