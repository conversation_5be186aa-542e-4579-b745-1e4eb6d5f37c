/**
 * Telegram Bot Service for Finwise
 */

const { Telegraf, session, Markup } = require('telegraf');
const { logger, logBotEvent, logUserInteraction, logBotStartup, logBotShutdown } = require('../utils/logger');
const { userRateLimiter } = require('../utils/rateLimiter');
const config = require('../config/config');
const BackendAPI = require('./BackendAPI');

// Import controllers
const authController = require('../controllers/authController');
const transactionController = require('../controllers/transactionController');
const dashboardController = require('../controllers/dashboardController');

class TelegramBotService {
  constructor() {
    this.bot = null;
    this.isRunning = false;
  }

  // Initialize the bot
  async initialize() {
    try {
      if (!config.telegram.botToken) {
        throw new Error('TELEGRAM_BOT_TOKEN is required');
      }

      this.bot = new Telegraf(config.telegram.botToken);

      // Set up middleware
      this.setupMiddleware();

      // Set up commands
      this.setupCommands();

      // Set up message handlers
      this.setupMessageHandlers();

      // Set up callback handlers
      this.setupCallbackHandlers();

      // Set up error handling
      this.setupErrorHandling();

      logBotEvent('Bot initialized successfully');
      return this.bot;
    } catch (error) {
      logger.error('Failed to initialize Telegram bot', { error: error.message });
      throw error;
    }
  }

  // Set up middleware
  setupMiddleware() {
    // Session middleware
    this.bot.use(session());

    // Rate limiting middleware
    this.bot.use(userRateLimiter.telegramMiddleware(20, 60000));

    // Logging middleware
    this.bot.use((ctx, next) => {
      const userId = ctx.from?.id;
      const username = ctx.from?.username;
      const command = ctx.message?.text || ctx.callbackQuery?.data || 'unknown';

      logUserInteraction(userId, username, command);
      return next();
    });

    // Command preprocessing
    this.bot.use((ctx, next) => {
      // Add timestamp to context
      ctx.timestamp = new Date();
      return next();
    });
  }

  // Set up bot commands
  setupCommands() {
    // Authentication commands
    this.bot.start(authController.handleStart.bind(authController));
    this.bot.command('link', authController.handleLink.bind(authController));
    this.bot.command('unlink', authController.handleUnlink.bind(authController));

    // Dashboard commands (require authentication)
    this.bot.command('balance', authController.requireAuth.bind(authController), dashboardController.handleBalance.bind(dashboardController));
    this.bot.command('stats', authController.requireAuth.bind(authController), dashboardController.handleDetailedStats.bind(dashboardController));
    this.bot.command('report', authController.requireAuth.bind(authController), dashboardController.handleMonthlyReport.bind(dashboardController));
    this.bot.command('goals', authController.requireAuth.bind(authController), dashboardController.handleGoalsBudgets.bind(dashboardController));
    this.bot.command('budgets', authController.requireAuth.bind(authController), dashboardController.handleGoalsBudgets.bind(dashboardController));

    // Transaction commands (require authentication)
    this.bot.command('add', authController.requireAuth.bind(authController), transactionController.handleAddTransaction.bind(transactionController));
    this.bot.command('recent', authController.requireAuth.bind(authController), transactionController.handleRecentTransactions.bind(transactionController));

    // Help command
    this.bot.help(dashboardController.handleHelp.bind(dashboardController));
  }

  // Set up message handlers
  setupMessageHandlers() {
    // Handle text messages
    this.bot.on('text', async (ctx) => {
      const text = ctx.message.text;
      const telegramId = ctx.from.id;

      // Skip if it's a command
      if (text.startsWith('/')) {
        return;
      }

      try {
        // Check if user is in linking process
        if (ctx.session?.awaitingLink) {
          await authController.handleLinkProcess(ctx, text);
          return;
        }

        // Check if user is adding a transaction step by step
        if (ctx.session?.addingTransaction) {
          const handled = await transactionController.handleTransactionStep(ctx, text);
          if (handled) return;
        }

        // Check if it's a quick transaction format
        const transactionRegex = /^(expense|income)\s+(\d+\.?\d*)\s+(.+?)(?:\s+(\w+))?$/i;
        if (transactionRegex.test(text)) {
          // Require authentication for transactions
          const user = await BackendAPI.getUserByTelegramId(telegramId);
          if (user) {
            ctx.user = user;
            await transactionController.handleQuickTransaction(ctx, text);
          } else {
            await authController.requireAuth(ctx, () => {});
          }
          return;
        }

        // Handle keyboard button presses
        await this.handleKeyboardButtons(ctx, text);

      } catch (error) {
        logger.error('Error handling text message', { error: error.message, telegramId });
        await ctx.reply('❌ Sorry, something went wrong. Please try again later.');
      }
    });
  }

  // Set up callback query handlers
  setupCallbackHandlers() {
    // Authentication callbacks
    this.bot.action('confirm_unlink', authController.handleUnlinkConfirmation.bind(authController));
    this.bot.action('cancel_unlink', (ctx) => ctx.editMessageText('❌ Unlink cancelled.'));

    // Transaction callbacks
    this.bot.action('add_expense', transactionController.handleAddExpense.bind(transactionController));
    this.bot.action('add_income', transactionController.handleAddIncome.bind(transactionController));
    this.bot.action('cancel_transaction', transactionController.handleCancelTransaction.bind(transactionController));

    // Category selection callbacks
    this.bot.action(/^cat_(.+)$/, (ctx) => {
      const category = ctx.match[1];
      return transactionController.handleCategorySelection(ctx, category);
    });

    // Dashboard callbacks
    this.bot.action('detailed_stats', dashboardController.handleDetailedStats.bind(dashboardController));
    this.bot.action('monthly_report', dashboardController.handleMonthlyReport.bind(dashboardController));
    this.bot.action('goals_budgets', dashboardController.handleGoalsBudgets.bind(dashboardController));
    this.bot.action('recent_transactions', transactionController.handleRecentTransactions.bind(transactionController));
    this.bot.action('back_to_balance', dashboardController.handleBalance.bind(dashboardController));
  }

  // Handle keyboard button presses
  async handleKeyboardButtons(ctx, text) {
    const telegramId = ctx.from.id;

    // Check if user is authenticated for protected actions
    const user = await BackendAPI.getUserByTelegramId(telegramId);

    switch (text) {
      case '🔗 Link Account':
        await authController.handleLink(ctx);
        break;
      case '❓ Help':
        await dashboardController.handleHelp(ctx);
        break;
      case '💰 Balance':
        if (user) {
          ctx.user = user;
          await dashboardController.handleBalance(ctx);
        } else {
          await authController.requireAuth(ctx, () => {});
        }
        break;
      case '📊 Stats':
        if (user) {
          ctx.user = user;
          await dashboardController.handleDetailedStats(ctx);
        } else {
          await authController.requireAuth(ctx, () => {});
        }
        break;
      case '➕ Add Transaction':
        if (user) {
          ctx.user = user;
          await transactionController.handleAddTransaction(ctx);
        } else {
          await authController.requireAuth(ctx, () => {});
        }
        break;
      case '📱 Recent':
      case '📱 Recent Transactions':
        if (user) {
          ctx.user = user;
          await transactionController.handleRecentTransactions(ctx);
        } else {
          await authController.requireAuth(ctx, () => {});
        }
        break;
      case '🎯 Goals':
      case '📋 Budgets':
        if (user) {
          ctx.user = user;
          await dashboardController.handleGoalsBudgets(ctx);
        } else {
          await authController.requireAuth(ctx, () => {});
        }
        break;
      case '⚙️ Settings':
        await ctx.reply('⚙️ Settings feature coming soon!');
        break;
      default:
        // Unknown button or message
        await ctx.reply(
          `🤔 I didn't understand that. Use /help to see available commands or try one of the buttons below.`,
          Markup.keyboard([
            ['💰 Balance', '📊 Stats'],
            ['➕ Add Transaction', '📱 Recent'],
            ['🎯 Goals', '📋 Budgets'],
            ['❓ Help', '⚙️ Settings']
          ]).resize()
        );
        break;
    }
  }

  // Set up error handling
  setupErrorHandling() {
    this.bot.catch((err, ctx) => {
      logger.error('Telegram bot error', {
        error: err.message,
        stack: err.stack,
        userId: ctx?.from?.id,
        username: ctx?.from?.username
      });

      // Try to send error message to user
      if (ctx && ctx.reply) {
        ctx.reply('❌ Sorry, something went wrong. Please try again later.')
          .catch(replyErr => {
            logger.error('Failed to send error message to user', { error: replyErr.message });
          });
      }
    });
  }

  // Start the bot
  async start() {
    try {
      if (!this.bot) {
        throw new Error('Bot not initialized');
      }

      logBotStartup();

      if (config.server.environment === 'production' && config.telegram.webhookUrl) {
        // Use webhooks in production
        await this.bot.telegram.setWebhook(config.telegram.webhookUrl);
        logger.info('Bot started with webhook', { url: config.telegram.webhookUrl });
      } else {
        // Use polling in development
        await this.bot.launch();
        logger.info('Bot started with polling');
      }

      this.isRunning = true;
      logBotEvent('Bot started successfully');
    } catch (error) {
      logger.error('Failed to start bot', { error: error.message });
      throw error;
    }
  }

  // Stop the bot
  async stop() {
    try {
      if (this.bot && this.isRunning) {
        await this.bot.stop();
        this.isRunning = false;
        logBotShutdown();
        logBotEvent('Bot stopped successfully');
      }
    } catch (error) {
      logger.error('Failed to stop bot', { error: error.message });
      throw error;
    }
  }

  // Get bot status
  getStatus() {
    return {
      isRunning: this.isRunning,
      botInfo: this.bot ? 'initialized' : 'not initialized'
    };
  }
}

module.exports = TelegramBotService;
