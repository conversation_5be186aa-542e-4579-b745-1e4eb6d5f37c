'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('payments', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      subscription_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'subscriptions',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      amount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        validate: {
          min: 0
        },
        comment: 'Payment amount'
      },
      currency: {
        type: Sequelize.STRING(3),
        defaultValue: 'USD',
        allowNull: false,
        comment: 'Payment currency'
      },
      status: {
        type: Sequelize.ENUM('pending', 'completed', 'failed', 'cancelled', 'refunded'),
        defaultValue: 'pending',
        allowNull: false,
        comment: 'Payment status'
      },
      payment_method: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: 'Payment method (e.g., Bakong)'
      },
      payment_reference: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: 'External payment reference'
      },
      transaction_id: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: 'Payment provider transaction ID'
      },
      qr_code: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'QR code string for payment'
      },
      qr_md5_hash: {
        type: Sequelize.STRING(32),
        allowNull: true,
        comment: 'MD5 hash of QR code for tracking'
      },
      bill_number: {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: 'Bill number for payment tracking'
      },
      paid_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'When payment was completed'
      },
      failed_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'When payment failed'
      },
      failure_reason: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Reason for payment failure'
      },
      metadata: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Additional payment metadata'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // Add indexes
    await queryInterface.addIndex('payments', ['user_id']);
    await queryInterface.addIndex('payments', ['subscription_id']);
    await queryInterface.addIndex('payments', ['status']);
    await queryInterface.addIndex('payments', ['payment_reference']);
    await queryInterface.addIndex('payments', ['qr_md5_hash']);
    await queryInterface.addIndex('payments', ['bill_number']);
    await queryInterface.addIndex('payments', ['paid_at']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('payments');
  }
};
