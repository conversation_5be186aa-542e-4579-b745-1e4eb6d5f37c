# 🤖 Finwise Telegram Bot - Usage Guide

## 🚀 Getting Started

### 1. Start the Bot
Send `/start` to the bot to begin. You'll see a welcome message with options to link your account.

### 2. Link Your Account
Use `/link` and then send one of the following:
- Your Finwise account email address
- Your User ID from Finwise dashboard
- A link code generated from your Finwise account

### 3. Start Managing Your Finances!
Once linked, you can use all the bot features.

## 📱 Available Commands

### 🔐 Account Management
- `/start` - Welcome message and setup
- `/link` - Link your Finwise account
- `/unlink` - Unlink your account
- `/help` - Show help message

### 💰 Financial Overview
- `/balance` - View balance and monthly overview
- `/stats` - Detailed financial statistics
- `/report` - Monthly financial report

### 📊 Transactions
- `/add` - Add new transaction (guided)
- `/recent` - View recent transactions

**Quick Transaction Format:**
```
expense 25.50 Coffee food
income 1500 Salary work
```

### 🎯 Goals & Budgets
- `/goals` - View and manage financial goals
- `/budgets` - View and manage budgets

## 🎮 Interactive Features

### 📱 Keyboard Buttons
The bot provides interactive keyboards with buttons for:
- 💰 Balance
- 📊 Stats  
- ➕ Add Transaction
- 📱 Recent Transactions
- 🎯 Goals
- 📋 Budgets
- ❓ Help
- ⚙️ Settings

### 🔄 Step-by-Step Guidance
When adding transactions, the bot guides you through:
1. **Type** (Income/Expense)
2. **Amount** (e.g., 25.50)
3. **Description** (e.g., Coffee at Starbucks)
4. **Category** (Food, Transport, etc.)

### 📊 Category Selection
Choose from predefined categories:
- 🍔 Food
- 🚗 Transport
- 🏠 Housing
- 🛍️ Shopping
- 💊 Health
- 🎯 Entertainment
- 💼 Work
- 📚 Education
- 🔧 Other

## 💡 Pro Tips

### ⚡ Quick Entry
For fast transaction entry, use the format:
```
expense [amount] [description] [category]
income [amount] [description] [category]
```

Examples:
- `expense 25.50 Coffee food`
- `income 1500 Freelance work`
- `expense 45.67 Groceries food`

### 📊 Smart Analytics
- Check `/stats` for detailed spending analysis
- Use `/report` for monthly summaries
- Monitor `/goals` for progress tracking

### 🔔 Stay Updated
- The bot syncs in real-time with your Finwise account
- All transactions appear immediately in both bot and web app
- Goals and budgets are updated automatically

## 🆘 Troubleshooting

### Account Linking Issues
- Make sure you have a Finwise account first
- Use the exact email address from your account
- Check that your account isn't already linked to another Telegram

### Transaction Problems
- Ensure you're using the correct format
- Check that amounts are numbers only
- Categories are optional but helpful for organization

### General Issues
- Try `/help` for command reference
- Restart with `/start` if needed
- Contact support if problems persist

## 🔒 Security & Privacy

- All data is encrypted in transit
- Your Telegram account is securely linked to Finwise
- No financial data is stored on Telegram servers
- You can unlink your account anytime with `/unlink`

## 🌟 Advanced Features

### 📈 Analytics
- Monthly spending trends
- Category-wise breakdowns
- Goal progress tracking
- Budget utilization reports

### 🎯 Goal Management
- Set savings targets
- Track progress automatically
- Get milestone notifications
- Adjust goals as needed

### 📋 Budget Control
- Set spending limits by category
- Real-time budget tracking
- Overspend alerts
- Monthly budget reports

---

**Need Help?** Use `/help` in the bot or contact <NAME_EMAIL>
