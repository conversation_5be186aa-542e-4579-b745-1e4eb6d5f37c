/**
 * Authentication Controller for Telegram Bot
 */

const { Markup } = require('telegraf');
const BackendAPI = require('../services/BackendAPI');
const { logger, logUserInteraction } = require('../utils/logger');

class AuthController {
  // Start command - welcome new users
  async handleStart(ctx) {
    try {
      const telegramId = ctx.from.id;
      const username = ctx.from.username;
      const firstName = ctx.from.first_name;

      logUserInteraction(telegramId, 'start_command', { username, firstName });

      // Check if user is already linked
      const user = await BackendAPI.getUserByTelegramId(telegramId);
      
      if (user) {
        await ctx.reply(
          `🎉 Welcome back, ${user.name || firstName}!\n\n` +
          `Your Finwise account is already linked. Use /help to see available commands.`,
          Markup.keyboard([
            ['💰 Balance', '📊 Stats'],
            ['➕ Add Transaction', '📱 Recent'],
            ['🎯 Goals', '📋 Budgets'],
            ['❓ Help', '⚙️ Settings']
          ]).resize()
        );
      } else {
        await ctx.reply(
          `👋 Welcome to Finwise, ${firstName}!\n\n` +
          `🤖 I'm your personal finance assistant. I can help you:\n` +
          `• Track expenses and income\n` +
          `• Monitor your budget\n` +
          `• Set and track financial goals\n` +
          `• Generate reports and insights\n\n` +
          `To get started, you need to link your Finwise account.\n` +
          `Use /link to connect your account.`,
          Markup.keyboard([
            ['🔗 Link Account'],
            ['❓ Help', '📱 Demo']
          ]).resize()
        );
      }
    } catch (error) {
      logger.error('Error in start command', { error: error.message, telegramId: ctx.from.id });
      await ctx.reply('❌ Sorry, something went wrong. Please try again later.');
    }
  }

  // Link account command
  async handleLink(ctx) {
    try {
      const telegramId = ctx.from.id;
      const username = ctx.from.username;

      logUserInteraction(telegramId, 'link_command', { username });

      // Check if already linked
      const existingUser = await BackendAPI.getUserByTelegramId(telegramId);
      
      if (existingUser) {
        await ctx.reply(
          `✅ Your account is already linked!\n\n` +
          `Account: ${existingUser.name || existingUser.email}\n` +
          `Use /help to see available commands.`
        );
        return;
      }

      await ctx.reply(
        `🔗 Link Your Finwise Account\n\n` +
        `To link your account, you can:\n\n` +
        `1️⃣ **Send your email address**\n` +
        `   Just type your Finwise account email\n\n` +
        `2️⃣ **Send your User ID**\n` +
        `   Find it in your Finwise profile settings\n\n` +
        `3️⃣ **Use a link code**\n` +
        `   Generate one from your Finwise dashboard\n\n` +
        `📝 Just send me any of these and I'll link your account!`,
        { parse_mode: 'Markdown' }
      );

      // Set user state to expect linking info
      ctx.session = ctx.session || {};
      ctx.session.awaitingLink = true;

    } catch (error) {
      logger.error('Error in link command', { error: error.message, telegramId: ctx.from.id });
      await ctx.reply('❌ Sorry, something went wrong. Please try again later.');
    }
  }

  // Handle linking process
  async handleLinkProcess(ctx, linkData) {
    try {
      const telegramId = ctx.from.id;
      const username = ctx.from.username;
      const firstName = ctx.from.first_name;

      logUserInteraction(telegramId, 'link_process', { linkData: linkData.substring(0, 10) + '...' });

      // Try to link the account
      const result = await BackendAPI.linkUserAccount(telegramId, linkData);

      if (result.success) {
        await ctx.reply(
          `✅ Account linked successfully!\n\n` +
          `Welcome to Finwise, ${result.user.name || firstName}! 🎉\n\n` +
          `You can now use all bot features. Type /help to get started.`,
          Markup.keyboard([
            ['💰 Balance', '📊 Stats'],
            ['➕ Add Transaction', '📱 Recent'],
            ['🎯 Goals', '📋 Budgets'],
            ['❓ Help', '⚙️ Settings']
          ]).resize()
        );

        // Clear session state
        if (ctx.session) {
          ctx.session.awaitingLink = false;
        }

        logUserInteraction(telegramId, 'link_success', { userId: result.user.id });
      } else {
        await ctx.reply(
          `❌ Unable to link account.\n\n` +
          `Please check:\n` +
          `• Email address is correct\n` +
          `• You have a Finwise account\n` +
          `• Account isn't already linked\n\n` +
          `Try again with /link`
        );
      }
    } catch (error) {
      logger.error('Error in link process', { error: error.message, telegramId: ctx.from.id });
      
      if (error.response?.status === 404) {
        await ctx.reply(
          `❌ Account not found.\n\n` +
          `Please make sure:\n` +
          `• You have a Finwise account\n` +
          `• Email address is correct\n` +
          `• Account isn't already linked\n\n` +
          `Create an account at finwise.app first, then try /link again.`
        );
      } else if (error.response?.status === 409) {
        await ctx.reply(
          `❌ This account is already linked to another Telegram user.\n\n` +
          `If this is your account, please contact support.`
        );
      } else {
        await ctx.reply(
          `❌ Sorry, something went wrong during linking.\n\n` +
          `Please try again later or contact support if the problem persists.`
        );
      }
    }
  }

  // Unlink account command
  async handleUnlink(ctx) {
    try {
      const telegramId = ctx.from.id;

      logUserInteraction(telegramId, 'unlink_command');

      const user = await BackendAPI.getUserByTelegramId(telegramId);
      
      if (!user) {
        await ctx.reply('❌ No linked account found.');
        return;
      }

      await ctx.reply(
        `⚠️ Unlink Account\n\n` +
        `Are you sure you want to unlink your Finwise account?\n` +
        `Account: ${user.name || user.email}\n\n` +
        `This will disable all bot features until you link again.`,
        Markup.inlineKeyboard([
          [Markup.button.callback('✅ Yes, Unlink', 'confirm_unlink')],
          [Markup.button.callback('❌ Cancel', 'cancel_unlink')]
        ])
      );
    } catch (error) {
      logger.error('Error in unlink command', { error: error.message, telegramId: ctx.from.id });
      await ctx.reply('❌ Sorry, something went wrong. Please try again later.');
    }
  }

  // Handle unlink confirmation
  async handleUnlinkConfirmation(ctx) {
    try {
      const telegramId = ctx.from.id;

      // This would require a backend API endpoint to unlink
      // For now, we'll just show a message
      await ctx.editMessageText(
        `✅ Account unlinked successfully.\n\n` +
        `Use /link to connect your account again.`
      );

      logUserInteraction(telegramId, 'unlink_success');
    } catch (error) {
      logger.error('Error in unlink confirmation', { error: error.message, telegramId: ctx.from.id });
      await ctx.reply('❌ Sorry, something went wrong. Please try again later.');
    }
  }

  // Check if user is authenticated
  async requireAuth(ctx, next) {
    try {
      const telegramId = ctx.from.id;
      const user = await BackendAPI.getUserByTelegramId(telegramId);
      
      if (!user) {
        await ctx.reply(
          `🔒 Authentication Required\n\n` +
          `You need to link your Finwise account first.\n` +
          `Use /link to get started.`,
          Markup.keyboard([
            ['🔗 Link Account'],
            ['❓ Help']
          ]).resize()
        );
        return;
      }

      // Add user to context for use in other handlers
      ctx.user = user;
      return next();
    } catch (error) {
      logger.error('Error in auth check', { error: error.message, telegramId: ctx.from.id });
      await ctx.reply('❌ Sorry, something went wrong. Please try again later.');
    }
  }
}

module.exports = new AuthController();
