/**
 * Transaction Controller for Telegram Bot
 */

const { Markup } = require('telegraf');
const BackendAPI = require('../services/BackendAPI');
const { logger, logUserInteraction } = require('../utils/logger');

class TransactionController {
  // Add transaction command
  async handleAddTransaction(ctx) {
    try {
      const telegramId = ctx.from.id;
      logUserInteraction(telegramId, 'add_transaction_command');

      await ctx.reply(
        `➕ Add New Transaction\n\n` +
        `You can add a transaction in several ways:\n\n` +
        `📝 **Quick Format:**\n` +
        `\`expense 25.50 Coffee food\`\n` +
        `\`income 1500 Salary work\`\n\n` +
        `📋 **Step by Step:**\n` +
        `Use the buttons below to guide you through the process.`,
        {
          parse_mode: 'Markdown',
          ...Markup.inlineKeyboard([
            [Markup.button.callback('💸 Add Expense', 'add_expense')],
            [Markup.button.callback('💰 Add Income', 'add_income')],
            [Markup.button.callback('❌ Cancel', 'cancel_transaction')]
          ])
        }
      );
    } catch (error) {
      logger.error('Error in add transaction command', { error: error.message, telegramId: ctx.from.id });
      await ctx.reply('❌ Sorry, something went wrong. Please try again later.');
    }
  }

  // Handle quick transaction input
  async handleQuickTransaction(ctx, text) {
    try {
      const telegramId = ctx.from.id;
      
      // Parse transaction format: "type amount description category"
      const transactionRegex = /^(expense|income)\s+(\d+\.?\d*)\s+(.+?)(?:\s+(\w+))?$/i;
      const match = text.match(transactionRegex);

      if (!match) {
        await ctx.reply(
          `❌ Invalid format. Please use:\n\n` +
          `\`expense 25.50 Coffee food\`\n` +
          `\`income 1500 Salary work\`\n\n` +
          `Or use /add for step-by-step guidance.`,
          { parse_mode: 'Markdown' }
        );
        return;
      }

      const [, type, amount, description, category] = match;
      const isIncome = type.toLowerCase() === 'income';

      const transactionData = {
        details: description.trim(),
        money_in: isIncome ? parseFloat(amount) : 0,
        money_out: isIncome ? 0 : parseFloat(amount),
        currency: 'USD', // Default currency
        date: new Date().toISOString().split('T')[0],
        tags: category ? category.toLowerCase() : 'other'
      };

      const result = await BackendAPI.createTransaction(ctx.user.id, transactionData);

      if (result.success) {
        await ctx.reply(
          `✅ Transaction Added Successfully!\n\n` +
          `💰 Amount: $${amount}\n` +
          `📝 Description: ${description}\n` +
          `🏷️ Category: ${category || 'other'}\n` +
          `📊 Type: ${type}\n` +
          `📅 Date: ${new Date().toLocaleDateString()}\n\n` +
          `Use /recent to see your latest transactions.`
        );

        logUserInteraction(telegramId, 'transaction_added', {
          type,
          amount: parseFloat(amount),
          category: category || 'other'
        });
      } else {
        await ctx.reply('❌ Failed to add transaction. Please try again.');
      }
    } catch (error) {
      logger.error('Error in quick transaction', { error: error.message, telegramId: ctx.from.id });
      await ctx.reply('❌ Sorry, something went wrong. Please try again later.');
    }
  }

  // Handle guided expense addition
  async handleAddExpense(ctx) {
    try {
      await ctx.editMessageText(
        `💸 Add Expense\n\n` +
        `Please enter the expense amount (numbers only):\n` +
        `Example: 25.50`,
        Markup.inlineKeyboard([
          [Markup.button.callback('❌ Cancel', 'cancel_transaction')]
        ])
      );

      // Set session state
      ctx.session = ctx.session || {};
      ctx.session.addingTransaction = {
        type: 'expense',
        step: 'amount'
      };
    } catch (error) {
      logger.error('Error in add expense', { error: error.message, telegramId: ctx.from.id });
      await ctx.reply('❌ Sorry, something went wrong. Please try again later.');
    }
  }

  // Handle guided income addition
  async handleAddIncome(ctx) {
    try {
      await ctx.editMessageText(
        `💰 Add Income\n\n` +
        `Please enter the income amount (numbers only):\n` +
        `Example: 1500`,
        Markup.inlineKeyboard([
          [Markup.button.callback('❌ Cancel', 'cancel_transaction')]
        ])
      );

      // Set session state
      ctx.session = ctx.session || {};
      ctx.session.addingTransaction = {
        type: 'income',
        step: 'amount'
      };
    } catch (error) {
      logger.error('Error in add income', { error: error.message, telegramId: ctx.from.id });
      await ctx.reply('❌ Sorry, something went wrong. Please try again later.');
    }
  }

  // Handle transaction step process
  async handleTransactionStep(ctx, text) {
    try {
      const session = ctx.session?.addingTransaction;
      if (!session) return false;

      const telegramId = ctx.from.id;

      switch (session.step) {
        case 'amount':
          const amount = parseFloat(text);
          if (isNaN(amount) || amount <= 0) {
            await ctx.reply('❌ Please enter a valid amount (numbers only).');
            return true;
          }

          session.amount = amount;
          session.step = 'description';

          await ctx.reply(
            `✅ Amount: $${amount}\n\n` +
            `📝 Now enter a description for this ${session.type}:\n` +
            `Example: Coffee at Starbucks`
          );
          return true;

        case 'description':
          session.description = text.trim();
          session.step = 'category';

          await ctx.reply(
            `✅ Description: ${text}\n\n` +
            `🏷️ Choose a category:`,
            Markup.inlineKeyboard([
              [Markup.button.callback('🍔 Food', 'cat_food'), Markup.button.callback('🚗 Transport', 'cat_transport')],
              [Markup.button.callback('🏠 Housing', 'cat_housing'), Markup.button.callback('🛍️ Shopping', 'cat_shopping')],
              [Markup.button.callback('💊 Health', 'cat_health'), Markup.button.callback('🎯 Entertainment', 'cat_entertainment')],
              [Markup.button.callback('💼 Work', 'cat_work'), Markup.button.callback('📚 Education', 'cat_education')],
              [Markup.button.callback('🔧 Other', 'cat_other')]
            ])
          );
          return true;
      }

      return false;
    } catch (error) {
      logger.error('Error in transaction step', { error: error.message, telegramId: ctx.from.id });
      await ctx.reply('❌ Sorry, something went wrong. Please try again later.');
      return true;
    }
  }

  // Handle category selection
  async handleCategorySelection(ctx, category) {
    try {
      const session = ctx.session?.addingTransaction;
      if (!session) return;

      const telegramId = ctx.from.id;
      session.category = category;

      // Create the transaction
      const transactionData = {
        details: session.description,
        money_in: session.type === 'income' ? session.amount : 0,
        money_out: session.type === 'expense' ? session.amount : 0,
        currency: 'USD',
        date: new Date().toISOString().split('T')[0],
        tags: category
      };

      const result = await BackendAPI.createTransaction(ctx.user.id, transactionData);

      if (result.success) {
        await ctx.editMessageText(
          `✅ ${session.type === 'income' ? 'Income' : 'Expense'} Added Successfully!\n\n` +
          `💰 Amount: $${session.amount}\n` +
          `📝 Description: ${session.description}\n` +
          `🏷️ Category: ${category}\n` +
          `📅 Date: ${new Date().toLocaleDateString()}\n\n` +
          `Use /recent to see your latest transactions.`
        );

        logUserInteraction(telegramId, 'transaction_added_guided', {
          type: session.type,
          amount: session.amount,
          category
        });
      } else {
        await ctx.editMessageText('❌ Failed to add transaction. Please try again.');
      }

      // Clear session
      delete ctx.session.addingTransaction;
    } catch (error) {
      logger.error('Error in category selection', { error: error.message, telegramId: ctx.from.id });
      await ctx.reply('❌ Sorry, something went wrong. Please try again later.');
    }
  }

  // Show recent transactions
  async handleRecentTransactions(ctx) {
    try {
      const telegramId = ctx.from.id;
      logUserInteraction(telegramId, 'recent_transactions_command');

      const transactions = await BackendAPI.getUserTransactions(ctx.user.id, { limit: 10 });

      if (!transactions.success || !transactions.data || transactions.data.length === 0) {
        await ctx.reply(
          `📱 Recent Transactions\n\n` +
          `No transactions found.\n\n` +
          `Use /add to add your first transaction!`
        );
        return;
      }

      let message = `📱 Recent Transactions\n\n`;
      
      transactions.data.forEach((tx, index) => {
        const amount = tx.money_in > 0 ? `+$${tx.money_in}` : `-$${tx.money_out}`;
        const emoji = tx.money_in > 0 ? '💰' : '💸';
        const date = new Date(tx.date).toLocaleDateString();
        
        message += `${emoji} ${amount}\n`;
        message += `📝 ${tx.details}\n`;
        message += `🏷️ ${tx.tags || 'other'} • 📅 ${date}\n\n`;
      });

      message += `Use /add to add a new transaction.`;

      await ctx.reply(message);
    } catch (error) {
      logger.error('Error in recent transactions', { error: error.message, telegramId: ctx.from.id });
      await ctx.reply('❌ Sorry, something went wrong. Please try again later.');
    }
  }

  // Cancel transaction process
  async handleCancelTransaction(ctx) {
    try {
      if (ctx.session?.addingTransaction) {
        delete ctx.session.addingTransaction;
      }

      await ctx.editMessageText('❌ Transaction cancelled.');
    } catch (error) {
      logger.error('Error in cancel transaction', { error: error.message, telegramId: ctx.from.id });
      await ctx.reply('❌ Sorry, something went wrong. Please try again later.');
    }
  }
}

module.exports = new TransactionController();
