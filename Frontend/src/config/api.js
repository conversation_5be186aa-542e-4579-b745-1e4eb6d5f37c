// API Configuration
// Centralized configuration for all API endpoints

// For development with Vite proxy, use relative URLs
// For production, use full URLs from environment variables
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '';
const API_URL = import.meta.env.VITE_API_URL || '/api';

// API Endpoints
export const API_ENDPOINTS = {
  // Base URLs
  BASE: API_BASE_URL,
  API: API_URL,
  
  // Authentication endpoints
  AUTH: {
    LOGIN: `${API_URL}/auth/login`,
    REGISTER: `${API_URL}/auth/register`,
    LOGOUT: `${API_URL}/auth/logout`,
    REFRESH: `${API_URL}/auth/refresh`,
    FORGOT_PASSWORD: `${API_URL}/auth/forgot-password`,
    VERIFY_PASSWORD_RESET_OTP: `${API_URL}/auth/verify-password-reset-otp`,
    RESET_PASSWORD: `${API_URL}/auth/reset-password`,
    GOOGLE: `${API_URL}/auth/google`,
    FACEBOOK: `${API_URL}/auth/facebook`,
    PROFILE: `${API_URL}/users/profile`,
  },
  
  // OTP endpoints
  OTP: {
    SEND_PASSWORD_RESET: `${API_URL}/otp/send-password-reset`,
    VERIFY_PASSWORD_RESET: `${API_URL}/otp/verify-password-reset`,
    RESEND: `${API_URL}/otp/resend`,
  },
  
  // User endpoints
  USERS: {
    PROFILE: `${API_URL}/users/profile`,
    UPDATE: `${API_URL}/users/profile`,
    DELETE: `${API_URL}/users/profile`,
  },
  
  // Transaction endpoints
  TRANSACTIONS: {
    LIST: `${API_URL}/transactions`,
    CREATE: `${API_URL}/transactions`,
    UPDATE: (id) => `${API_URL}/transactions/${id}`,
    DELETE: (id) => `${API_URL}/transactions/${id}`,
    EXPORT: `${API_URL}/export/transactions`,
    IMPORT: `${API_URL}/import/transactions`,
  },
  
  // Budget endpoints
  BUDGETS: {
    LIST: `${API_URL}/budgets`,
    CREATE: `${API_URL}/budgets`,
    UPDATE: (id) => `${API_URL}/budgets/${id}`,
    DELETE: (id) => `${API_URL}/budgets/${id}`,
  },
  
  // Goal endpoints
  GOALS: {
    LIST: `${API_URL}/goals`,
    CREATE: `${API_URL}/goals`,
    UPDATE: (id) => `${API_URL}/goals/${id}`,
    DELETE: (id) => `${API_URL}/goals/${id}`,
  },
  
  // Category endpoints
  CATEGORIES: {
    LIST: `${API_URL}/categories`,
    CREATE: `${API_URL}/categories`,
    UPDATE: (id) => `${API_URL}/categories/${id}`,
    DELETE: (id) => `${API_URL}/categories/${id}`,
  },
  
  // Dashboard endpoints
  DASHBOARD: {
    SUMMARY: `${API_URL}/dashboard/summary`,
    CHART_DATA: `${API_URL}/dashboard/charts`,
  },
  
  // OCR endpoints
  OCR: {
    UPLOAD: `${API_URL}/ocr/upload`,
    PROCESS: `${API_URL}/ocr/process`,
  },
};

// Default configuration
export const API_CONFIG = {
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
};

// Helper function to get authenticated headers
export const getAuthHeaders = () => {
  const token = localStorage.getItem('accessToken');
  return {
    ...API_CONFIG.headers,
    ...(token && { Authorization: `Bearer ${token}` }),
  };
};

// Helper function to build query strings
export const buildQueryString = (params) => {
  const searchParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      searchParams.append(key, value);
    }
  });
  return searchParams.toString();
};

export default API_ENDPOINTS;
