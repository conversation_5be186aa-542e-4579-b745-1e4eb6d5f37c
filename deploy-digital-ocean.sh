#!/bin/bash

# Digital Ocean Production Deployment Script
# Run this script on your Digital Ocean droplet

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🚀 Finwise Backend - Digital Ocean Production Deployment"
echo "=================================================="

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    print_warning "Running as root. Consider using a non-root user with sudo privileges."
fi

# Check if docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Installing Docker..."
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    sudo usermod -aG docker $USER
    print_success "Docker installed. Please logout and login again, then re-run this script."
    exit 0
fi

# Check if git is installed
if ! command -v git &> /dev/null; then
    print_status "Installing git..."
    sudo apt update
    sudo apt install -y git
fi

# Clone or update repository
REPO_URL="https://github.com/your-username/finwise.git"  # Update this with your actual repo URL
APP_DIR="/opt/finwise"

if [ -d "$APP_DIR" ]; then
    print_status "Updating existing repository..."
    cd $APP_DIR
    git pull origin main
else
    print_status "Cloning repository..."
    sudo mkdir -p /opt
    sudo git clone $REPO_URL $APP_DIR
    sudo chown -R $USER:$USER $APP_DIR
    cd $APP_DIR
fi

# Check if .env file exists
if [ ! -f "Backend/.env" ]; then
    if [ -f "Backend/.env.production" ]; then
        print_status "Copying production environment file..."
        cp Backend/.env.production Backend/.env
    else
        print_error "No environment file found. Please create Backend/.env"
        print_status "You can copy from Backend/.env.production and modify as needed"
        exit 1
    fi
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p Backend/uploads
mkdir -p logs

# Stop existing container if running
if docker ps | grep -q finwise-backend; then
    print_warning "Stopping existing container..."
    docker stop finwise-backend
fi

# Remove existing container if exists
if docker ps -a | grep -q finwise-backend; then
    print_warning "Removing existing container..."
    docker rm finwise-backend
fi

# Build new image
print_status "Building Docker image..."
docker build -t finwise-backend:latest .

# Run the container
print_status "Starting Finwise backend container..."
docker run -d \
    --name finwise-backend \
    -p 5003:5003 \
    --env-file Backend/.env \
    -v "$(pwd)/Backend/uploads:/app/uploads" \
    -v "$(pwd)/logs:/app/logs" \
    --restart unless-stopped \
    --memory="1g" \
    --cpus="1.0" \
    finwise-backend:latest

# Wait for container to start
print_status "Waiting for container to start..."
sleep 5

# Check if container is running
if docker ps | grep -q finwise-backend; then
    print_success "Container started successfully!"
    
    # Test health endpoint
    print_status "Testing health endpoint..."
    sleep 3
    if curl -f http://localhost:5003/api/health > /dev/null 2>&1; then
        print_success "Health check passed!"
    else
        print_warning "Health check failed. Check logs with: docker logs finwise-backend"
    fi
    
    echo ""
    echo "📊 Container Information:"
    docker ps | grep finwise-backend
    echo ""
    echo "📋 Useful Commands:"
    echo "  View logs: docker logs finwise-backend"
    echo "  Follow logs: docker logs -f finwise-backend"
    echo "  Stop container: docker stop finwise-backend"
    echo "  Restart container: docker restart finwise-backend"
    echo ""
    echo "🌐 Your backend is now running at:"
    echo "  Local: http://localhost:5003"
    echo "  Health: http://localhost:5003/api/health"
    
    # Get server IP
    SERVER_IP=$(curl -s ifconfig.me || echo "Unable to detect")
    if [ "$SERVER_IP" != "Unable to detect" ]; then
        echo "  External: http://$SERVER_IP:5003"
        echo "  Health: http://$SERVER_IP:5003/api/health"
    fi
    
else
    print_error "Container failed to start!"
    print_status "Checking logs..."
    docker logs finwise-backend
    exit 1
fi

# Setup firewall if ufw is available
if command -v ufw &> /dev/null; then
    print_status "Configuring firewall..."
    sudo ufw allow 5003/tcp
    sudo ufw allow ssh
    print_success "Firewall configured to allow port 5003"
fi

# Setup log rotation
print_status "Setting up log rotation..."
sudo tee /etc/logrotate.d/finwise > /dev/null <<EOF
$(pwd)/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    copytruncate
}
EOF

print_success "Deployment completed successfully!"
echo ""
echo "🔧 Next Steps:"
echo "1. Update your Netlify frontend environment variables with this server's URL"
echo "2. Configure a reverse proxy (nginx) for SSL and domain mapping"
echo "3. Set up monitoring and backups"
echo "4. Test all functionality from your frontend"
echo ""
echo "📞 Support: Check logs if you encounter any issues"
