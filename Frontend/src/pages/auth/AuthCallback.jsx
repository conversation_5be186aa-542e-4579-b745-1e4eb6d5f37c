import React, { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import useAuthStore from "../../store/authStore";

const AuthCallback = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { oauthLogin, isAuthenticated, user } = useAuthStore();
  const [hasProcessed, setHasProcessed] = useState(false);

  useEffect(() => {
    if (hasProcessed) return;

    const token = searchParams.get("token");
    const refresh = searchParams.get("refresh");
    const error = searchParams.get("error");

    console.log("AuthCallback - URL params:", { token: token?.substring(0, 20) + "...", refresh: refresh?.substring(0, 20) + "...", error });

    if (error) {
      console.error("OAuth error:", error);
      navigate("/login?error=" + error);
      return;
    }

    if (token && refresh) {
      console.log("AuthCallback - Calling oauthLogin...");
      setHasProcessed(true);
      
      // Use the OAuth login method
      oauthLogin(token, refresh).then((result) => {
        console.log("AuthCallback - oauthLogin result:", result);
        if (result.success) {
          console.log("AuthCallback - Login successful, waiting for state update...");
          // Don't navigate immediately, let the effect below handle it
        } else {
          console.error("AuthCallback - Login failed:", result.error);
          navigate("/login?error=" + (result.error || "oauth_failed"));
        }
      }).catch((err) => {
        console.error("AuthCallback - oauthLogin promise rejected:", err);
        navigate("/login?error=oauth_failed");
      });
    } else {
      console.error("Missing tokens in OAuth callback");
      navigate("/login?error=missing_tokens");
    }
  }, [searchParams, navigate, oauthLogin, hasProcessed]);

  // Separate effect to handle navigation after authentication state is updated
  useEffect(() => {
    if (hasProcessed && isAuthenticated && user) {
      console.log("AuthCallback - User is authenticated, redirecting to dashboard");
      // Small delay to ensure state is fully persisted
      setTimeout(() => {
        navigate("/dashboard", { replace: true });
      }, 100);
    }
  }, [isAuthenticated, user, hasProcessed, navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
        <p className="mt-4 text-gray-600">Completing authentication...</p>
        {hasProcessed && (
          <p className="mt-2 text-sm text-gray-500">Processing login...</p>
        )}
      </div>
    </div>
  );
};

export default AuthCallback;
