import React, { useState, useEffect } from 'react';
import { X, Target, DollarSign, Calendar, FileText } from 'lucide-react';
import useGoalStore from '../../store/goalStore';

const GoalForm = ({ goal, onClose, onSuccess }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    target_amount: '',
    current_amount: '0',
    target_date: '',
    category: 'savings'
  });

  const [errors, setErrors] = useState({});
  const { createGoal, updateGoal, isLoading } = useGoalStore();

  useEffect(() => {
    if (goal) {
      setFormData({
        name: goal.name || '',
        description: goal.description || '',
        target_amount: goal.target_amount?.toString() || '',
        current_amount: goal.current_amount?.toString() || '0',
        target_date: goal.target_date ? goal.target_date.split('T')[0] : '',
        category: goal.category || 'savings'
      });
    }
  }, [goal]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Goal name is required';
    }

    if (!formData.target_amount || parseFloat(formData.target_amount) <= 0) {
      newErrors.target_amount = 'Target amount must be greater than 0';
    }

    if (parseFloat(formData.current_amount) < 0) {
      newErrors.current_amount = 'Current amount cannot be negative';
    }

    if (!formData.target_date) {
      newErrors.target_date = 'Target date is required';
    } else {
      const targetDate = new Date(formData.target_date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (targetDate <= today) {
        newErrors.target_date = 'Target date must be in the future';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const goalData = {
      ...formData,
      target_amount: parseFloat(formData.target_amount),
      current_amount: parseFloat(formData.current_amount)
    };

    try {
      let result;
      if (goal) {
        result = await updateGoal(goal.id, goalData);
      } else {
        result = await createGoal(goalData);
      }

      if (result.success) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error saving goal:', error);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const categories = [
    { value: 'savings', label: 'Savings' },
    { value: 'emergency', label: 'Emergency Fund' },
    { value: 'vacation', label: 'Vacation' },
    { value: 'house', label: 'House/Property' },
    { value: 'car', label: 'Car/Vehicle' },
    { value: 'education', label: 'Education' },
    { value: 'retirement', label: 'Retirement' },
    { value: 'debt', label: 'Debt Payoff' },
    { value: 'other', label: 'Other' }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-[#e2e8ec] rounded-2xl shadow-xl w-full max-w-lg max-h-[90vh] overflow-y-auto border border-[#c6d2dd]">
        {/* Header */}
        <div className="flex items-center justify-between p-5 border-b border-[#aabbcd] bg-[#c6d2dd]/30 rounded-t-2xl">
          <h2 className="text-xl font-semibold text-gray-900">
            {goal ? 'Edit Goal' : 'Create New Goal'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-5">
          {/* Goal Name */}
          <div>
            <label className="block text-sm font-medium text-[#636160] mb-1">
              <Target className="w-4 h-4 inline mr-1" />
              Goal Name
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className={`w-full rounded-lg border px-3 py-2 text-sm focus:ring-2 focus:ring-[#8da5bd] ${errors.name ? 'border-red-500' : "border-[#aabbcd]"}`}
              placeholder="e.g., Emergency Fund, Vacation, New Car"
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <FileText className="w-4 h-4 inline mr-1" />
              Description (Optional)
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows={3}
              className="w-full rounded-lg border px-3 py-2 text-sm focus:ring-2 focus:ring-[#8da5bd] 'border-red-500' : border-[#aabbcd]"
              placeholder="Describe your goal and why it's important to you..."
            />
          </div>

          {/* Category */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category
            </label>
            <select
              name="category"
              value={formData.category}
              onChange={handleChange}
              className="w-full rounded-lg border px-3 py-2 text-sm focus:ring-2 focus:ring-[#8da5bd] border-[#aabbcd]"
            >
              {categories.map(category => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>
          </div>

          {/* Target Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <DollarSign className="w-4 h-4 inline mr-1" />
              Target Amount
            </label>
            <input
              type="number"
              name="target_amount"
              value={formData.target_amount}
              onChange={handleChange}
              min="0"
              step="0.01"
              className={`w-full rounded-lg border px-3 py-2 text-sm focus:ring-2 focus:ring-[#8da5bd] ${errors.target_amount ? 'border-red-500' : 'border-[#aabbcd]'}`}
              placeholder="0.00"
            />
            {errors.target_amount && (
              <p className="mt-1 text-sm text-red-600">{errors.target_amount}</p>
            )}
          </div>

          {/* Current Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Current Amount
            </label>
            <input
              type="number"
              name="current_amount"
              value={formData.current_amount}
              onChange={handleChange}
              min="0"
              step="0.01"
              className={`w-full rounded-lg border px-3 py-2 text-sm focus:ring-2 focus:ring-[#8da5bd] ${errors.current_amount ? 'border-red-500' : 'border-[#aabbcd]'}`}
              placeholder="0.00"
            />
            {errors.current_amount && (
              <p className="mt-1 text-sm text-red-600">{errors.current_amount}</p>
            )}
          </div>

          {/* Target Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Calendar className="w-4 h-4 inline mr-1" />
              Target Date
            </label>
            <input
              type="date"
              name="target_date"
              value={formData.target_date}
              onChange={handleChange}
              min={new Date().toISOString().split('T')[0]}
              className={`w-full rounded-lg border px-3 py-2 text-sm focus:ring-2 focus:ring-[#8da5bd] ${errors.target_date ? 'border-red-500' : 'border-[#aabbcd]'}`}
            />
            {errors.target_date && (
              <p className="mt-1 text-sm text-red-600">{errors.target_date}</p>
            )}
          </div>

          {/* Progress Preview */}
          {formData.target_amount && formData.current_amount && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Progress Preview</h4>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">
                  ${parseFloat(formData.current_amount || 0).toFixed(2)} of ${parseFloat(formData.target_amount || 0).toFixed(2)}
                </span>
                <span className="text-sm font-medium">
                  {((parseFloat(formData.current_amount || 0) / parseFloat(formData.target_amount || 1)) * 100).toFixed(1)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-primary-600 h-2 rounded-full"
                  style={{
                    width: `${Math.min(
                      (parseFloat(formData.current_amount || 0) / parseFloat(formData.target_amount || 1)) * 100,
                      100
                    )}%`
                  }}
                ></div>
              </div>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 rounded-lg border border-[#aabbcd] text-[#636160] hover:bg-[#c6d2dd]/40 transition"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 rounded-lg bg-[#8da5bd] text-white hover:bg-[#7a90a8] transition"
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {goal ? 'Updating...' : 'Creating...'}
                </div>
              ) : (
                goal ? 'Update Goal' : 'Create Goal'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default GoalForm;
