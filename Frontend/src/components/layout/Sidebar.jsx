import React from "react";
import { NavLink } from "react-router-dom";
import {
  LayoutDashboard,
  CreditCard,
  PiggyBank,
  Target,
  BarChart3,
  Upload,
  X,
  Crown,
  Wallet
} from "lucide-react";

const Sidebar = ({ isOpen, onClose, isDesktop }) => {
  const navigation = [
    { name: t('navigation.dashboard'), href: "/dashboard", icon: LayoutDashboard },
    { name: t('navigation.transactions'), href: "/transactions", icon: CreditCard },
    { name: t('navigation.import'), href: "/import", icon: Upload },
    { name: t('navigation.budgets'), href: "/budgets", icon: PiggyBank },
    { name: t('navigation.goals'), href: "/goals", icon: Target },
    { name: t('navigation.reports'), href: "/reports", icon: BarChart3 },
    { name: t('billing.title'), href: "/billing", icon: Crown },
  ];

  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && !isDesktop && (
        <div className="fixed inset-0 z-40" onClick={onClose}>
          <div className="absolute inset-0 bg-black bg-opacity-50"></div>
        </div>
      )}

      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 transform shadow-xl transition-transform duration-300 ease-in-out
          ${isOpen ? "translate-x-0" : "-translate-x-full"}
          bg-gray-50 dark:bg-[#1E1E2D]`}
      >
        {/* Header */}
        <div className="flex items-center justify-between h-16 px-6 border-b border-[#3B4A8C] bg-[#3B4A8C] dark:bg-[#252736]">
          <div className="flex items-center">
            <div className="flex items-center justify-center w-9 h-9 bg-[#5C66B3] rounded-lg shadow">
              <Wallet className="w-5 h-5 text-white" />
            </div>
            <span className="ml-2 text-lg font-semibold text-white dark:text-gray-200">
              Finwise
            </span>
          </div>
          {!isDesktop && (
            <button
              onClick={onClose}
              className="text-white dark:text-gray-200 hover:text-[#8da5bd] transition"
            >
              <X className="w-6 h-6" />
            </button>
          )}
        </div>

        {/* Navigation */}
        <nav className="mt-6 overflow-y-auto">
          <div className="px-4 space-y-2">
            {navigation.map((item) => (
              <NavLink
                key={item.name}
                to={item.href}
                onClick={() => !isDesktop && onClose()}
                className={({ isActive }) =>
                  `flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200
                  ${
                    isActive
                      ? "bg-[#3B4A8C] text-white shadow-md dark:bg-[#7E57C2]"
                      : "text-[#3B4A8C] dark:text-gray-200 hover:bg-[#5C66B3] dark:hover:bg-[#3A3B4E] hover:text-white dark:hover:text-white"
                  }`
                }
              >
                <item.icon className="w-5 h-5 mr-3" />
                {item.name}
              </NavLink>
            ))}
          </div>
        </nav>

        {/* Bottom Help */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t">
          <div className="rounded-lg p-4 bg-[#3B4A8C] hover:bg-[#5C66B3] dark:bg-[#7E57C2] dark:hover:bg-[#9c6fd6] transition-colors">
            <h3 className="text-sm font-medium text-white">Need Help?</h3>
            <p className="text-xs text-[#e2e8ec] mt-1">
              Check out our documentation and tutorials.
            </p>
            <button className="mt-2 text-xs font-medium text-white hover:underline">
              Learn more →
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
