import React, { useState, useEffect } from "react";
import { importExportAPI, downloadFile } from "../services/api";
import { Helmet } from "react-helmet-async";
import {
  Plus,
  Filter,
  Download,
  Search,
  Edit,
  Trash2,
  Camera,
  DollarSign,
} from "lucide-react";
import useTransactionStore from "../store/transactionStore";
import TransactionForm from "../components/transactions/TransactionForm";
import ReceiptScanner from "../components/ocr/ReceiptScanner";
import { toast } from "react-hot-toast";
import CategoryDropdown from "../components/transactions/categoryDropdown";

const Transactions = () => {
  const [showForm, setShowForm] = useState(false);
  const [editingTransaction, setEditingTransaction] = useState(null);
  const [showFilters, setShowFilters] = useState(false);
  const [showExportDropdown, setShowExportDropdown] = useState(false);
  const [showOCRScanner, setShowOCRScanner] = useState(false);

  const [searchText, setSearchText] = useState("");
  const [filterCategory, setFilterCategory] = useState("");
  const [filterType, setFilterType] = useState("");
  const [filterDateFrom, setFilterDateFrom] = useState("");
  const [filterDateTo, setFilterDateTo] = useState("");
  const [editingCategoryId, setEditingCategoryId] = useState(null);
  const [tempCategoryId, setTempCategoryId] = useState(null);

  const {
    transactions,
    categories,
    isLoading,
    summary,
    fetchTransactions,
    fetchCategories,
    deleteTransaction,
    updateTransaction,
  } = useTransactionStore();

  useEffect(() => {
    fetchCategories();
    fetchTransactions();
  }, []);

  const filteredTransactions = transactions.filter((t) => {
    const matchesSearch =
      t.details.toLowerCase().includes(searchText.toLowerCase()) ||
      (t.category?.name || "").toLowerCase().includes(searchText.toLowerCase());
    const matchesCategory = filterCategory ? t.category_id === filterCategory : true;
    const matchesType =
      filterType === "income" ? t.money_in > 0 :
      filterType === "expense" ? t.money_out > 0 :
      true;
    const matchesDateFrom = filterDateFrom ? new Date(t.date) >= new Date(filterDateFrom) : true;
    const matchesDateTo = filterDateTo ? new Date(t.date) <= new Date(filterDateTo) : true;

    return matchesSearch && matchesCategory && matchesType && matchesDateFrom && matchesDateTo;
  });

  const formatAmount = (amount, currency = "USD") =>
    new Intl.NumberFormat("en-US", { style: "currency", currency }).format(amount);

  const formatDate = (dateString) =>
    new Date(dateString).toLocaleDateString("en-US", { year: "numeric", month: "short", day: "numeric" });

  const handleDelete = async (id) => {
    if (confirm("Are you sure you want to delete this transaction?")) {
      const result = await deleteTransaction(id);
      if (result.success) toast.success("Transaction deleted!");
    }
  };

  const handleExport = async (type) => {
    try {
      const params = {
        search: searchText,
        category_id: filterCategory,
        type: filterType,
        start_date: filterDateFrom,
        end_date: filterDateTo,
      };

      let response, filename;
      if (type === "csv") {
        response = await importExportAPI.exportCSV(params);
        filename = `transactions_${new Date().toISOString().split("T")[0]}.csv`;
      } else if (type === "excel") {
        response = await importExportAPI.exportExcel(params);
        filename = `transactions_${new Date().toISOString().split("T")[0]}.xlsx`;
      } else if (type === "pdf") {
        response = await importExportAPI.exportPDF(params);
        filename = `transactions_${new Date().toISOString().split("T")[0]}.pdf`;
      }

      downloadFile(response.data, filename);
      toast.success(`Transactions exported to ${type.toUpperCase()} successfully!`);
    } catch (error) {
      console.error("Export error:", error);
      toast.error(`Failed to export transactions to ${type.toUpperCase()}`);
    }
  };

  const handleCategoryChange = (transactionId, newCategoryId) => {
    const transaction = transactions.find(t => t.id === transactionId);
    if (!transaction) return;

    if (transaction.category_id === newCategoryId) {
      // No change, avoid redundant API call
      return;
    }

    const category = categories.find(c => c.id === newCategoryId);

    if (!category) {
      updateTransaction(transactionId, {
        category_id: null,
        money_in: 0,
        money_out: 0,
      });
    } else if (category.type === "income") {
      updateTransaction(transactionId, {
        category_id: newCategoryId,
        money_in: transaction.money_in || transaction.money_out || 0,
        money_out: 0,
      });
    } else {
      updateTransaction(transactionId, {
        category_id: newCategoryId,
        money_in: 0,
        money_out: transaction.money_out || transaction.money_in || 0,
      });
    }

    setEditingCategoryId(null);
  };

  return (
    <>
      <Helmet>
        <title>Transactions - Finwise</title>
        <meta name="description" content="Manage your financial transactions" />
      </Helmet>

      <div className="space-y-6 p-4 md:p-6 bg-gray-100 dark:bg-[#1E1E2D] rounded-xl">
        {/* Header */}
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-[#1E3A8A] dark:text-white">Transactions</h1>
            <p className="text-gray-500 dark:text-gray-400 mt-1">Track and manage your finances efficiently.</p>
          </div>

          <div className="flex flex-wrap gap-2">
            {/* Filter Button */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-4 py-2 border rounded-lg bg-white dark:bg-[#252736] dark:hover:bg-[#3A3B4E] text-[#1E3A8A] dark:text-white shadow hover:bg-[#8B5CF6] hover:text-white transition"
            >
              <Filter className="w-4 h-4" /> Filter
            </button>

            {/* Export */}
            <div className="relative">
              <button
                onClick={() => setShowExportDropdown(!showExportDropdown)}
                className="flex items-center gap-2 px-4 py-2 border rounded-lg bg-white dark:bg-[#252736] dark:hover:bg-[#3A3B4E] text-[#1E3A8A] dark:text-white shadow hover:bg-[#8B5CF6] hover:text-white transition"
              >
                <Download className="w-4 h-4" /> Export
              </button>

              {showExportDropdown && (
                <div className="absolute right-0 mt-2 w-44 bg-white dark:bg-[#252736] rounded-lg shadow-lg border border-gray-200 dark:border-[#3A3B4E] z-20 overflow-hidden">
                  {["excel", "csv", "pdf"].map((type) => (
                    <button
                      key={type}
                      onClick={() => { handleExport(type); setShowExportDropdown(false); }}
                      className="block w-full px-4 py-2 text-left text-sm text-[#1E3A8A] dark:text-white hover:bg-[#8B5CF6] hover:text-white transition"
                    >
                      Export to {type.toUpperCase()}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* OCR Scanner */}
            <button
              onClick={() => setShowOCRScanner(true)}
              className="flex items-center gap-2 px-4 py-2 border rounded-lg bg-white dark:bg-[#252736] dark:hover:bg-[#3A3B4E] text-[#1E3A8A] dark:text-white shadow hover:bg-[#8B5CF6] hover:text-white transition"
            >
              <Camera className="w-4 h-4" /> Scan Receipt
            </button>

            {/* Add Transaction */}
            <button
              onClick={() => { setEditingTransaction(null); setShowForm(true); }}
              className="flex items-center gap-2 px-4 py-2 rounded-lg bg-[#1E3A8A] dark:bg-[#8B5CF6] text-white hover:bg-[#162B5A] dark:hover:bg-[#6D28D9] transition shadow"
            >
              <Plus className="w-4 h-4" /> Add Transaction
            </button>
          </div>
        </div>

        {/* Summary cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
          {[
            {
              label: "Total Income",
              value: summary.totalIncome,
              color: "text-green-500",
              iconColor: "bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400",
            },
            {
              label: "Total Expenses",
              value: summary.totalExpense,
              color: "text-red-500",
              iconColor: "bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400",
            },
            {
              label: "Net Balance",
              value: summary.balance,
              color: summary.balance >= 0 ? "text-green-500" : "text-red-500",
              iconColor:
                summary.balance >= 0
                  ? "bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400"
                  : "bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400",
            },
            {
              label: "Transactions",
              value: summary.totalTransactions,
              color: "text-blue-500",
              iconColor: "bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400",
            },
          ].map((card, idx) => (
            <div
              key={idx}
              className="group relative flex flex-col justify-between gap-3 p-6 rounded-2xl border border-zinc-200 dark:border-zinc-700 bg-white/60 dark:bg-zinc-900/40 backdrop-blur-md shadow-md transition hover:shadow-lg hover:scale-[1.015]"
            >
              {/* Icon with ring */}
              <div className={`w-11 h-11 flex items-center justify-center rounded-full ${card.iconColor}`}>
                <DollarSign className="w-5 h-5" />
              </div>

              {/* Title and Value */}
              <div className="flex flex-col gap-1">
                <p className="text-sm font-medium text-zinc-500 dark:text-zinc-400">{card.label}</p>
                <h2 className={`text-2xl font-bold ${card.color}`}>
                  {card.label === "Transactions" ? card.value : formatAmount(card.value)}
                </h2>
              </div>

              {/* Fancy hover indicator line (optional) */}
              <div className="absolute bottom-0 left-6 w-0 h-[3px] bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full transition-all duration-300 group-hover:w-3/4"></div>
            </div>
          ))}
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="bg-white dark:bg-[#252736] p-4 rounded-xl shadow-md flex flex-wrap gap-4 items-center justify-between border border-gray-100 dark:border-[#3A3B4E]">
            {/* Search & Category */}
            <div className="flex flex-grow items-center gap-4">
              <div className="relative flex-1 min-w-[200px]">
                <input
                  type="text"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  placeholder="Search transactions..."
                  className="w-full pl-10 pr-3 py-2 rounded-lg border border-gray-300 dark:border-[#3A3B4E] bg-white dark:bg-[#1E1E2D] text-gray-800 dark:text-white focus:ring-1 focus:ring-[#8B5CF6] focus:border-[#8B5CF6] transition"
                />
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400 dark:text-gray-400" />
              </div>

              <div className="relative flex-1 w-[80px]">
                <select
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                  className="w-full py-2 px-3 rounded-lg border border-gray-300 dark:border-[#3A3B4E] bg-white dark:bg-[#1E1E2D] text-gray-800 dark:text-white focus:ring-1 focus:ring-[#8B5CF6] focus:border-[#8B5CF6] transition appearance-none"
                >
                  <option value="">All Category</option>
                  {categories.map((c) => (
                    <option key={c.id} value={c.id}>{c.name}</option>
                  ))}
                </select>
                <span className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none text-gray-400 dark:text-gray-400">▼</span>
              </div>

              {/* Type Pills */}
              <div className="flex gap-2">
                {["All", "Income", "Expense"].map((type) => {
                  const value = type.toLowerCase();
                  const isSelected = (value === "all" && !filterType) || filterType === value;
                  return (
                    <button
                      key={type}
                      onClick={() => setFilterType(value === "all" ? "" : value)}
                      className={`px-3 py-1.5 rounded-full font-medium text-sm transition ${
                        isSelected ? "bg-[#1E3A8A] dark:bg-[#8B5CF6] text-white shadow-sm" : "bg-gray-100 dark:bg-[#2A2B3D] text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-[#3A3B4E]"
                      }`}
                    >
                      {type}
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Date Range & Reset */}
            <div className="flex gap-4">
              <input
                type="date"
                value={filterDateFrom}
                onChange={(e) => setFilterDateFrom(e.target.value)}
                className="w-[150px] py-2 px-3 rounded-lg border border-gray-300 dark:border-[#3A3B4E] bg-white dark:bg-[#1E1E2D] text-gray-800 dark:text-white focus:ring-1 focus:ring-[#8B5CF6] focus:border-[#8B5CF6] transition"
              />
              <input
                type="date"
                value={filterDateTo}
                onChange={(e) => setFilterDateTo(e.target.value)}
                className="w-[150px] py-2 px-3 rounded-lg border border-gray-300 dark:border-[#3A3B4E] bg-white dark:bg-[#1E1E2D] text-gray-800 dark:text-white focus:ring-1 focus:ring-[#8B5CF6] focus:border-[#8B5CF6] transition"
              />
            </div>

            <div>
              <button
                onClick={() => {
                  setSearchText(""); setFilterCategory(""); setFilterType(""); setFilterDateFrom(""); setFilterDateTo("");
                }}
                className="px-3 py-2 rounded-lg bg-gray-200 dark:bg-[#2A2B3D] text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-[#3A3B4E] transition"
              >
                Reset
              </button>
            </div>
          </div>
        )}

        {/* Transactions Table */}
        <div className="bg-white dark:bg-[#252736] rounded-lg shadow overflow-x-auto mt-4">
          <table className="min-w-full table-fixed divide-y divide-gray-200 dark:divide-[#3A3B4E]">
            <thead className="bg-gray-50 dark:bg-[#2A2B3D] text-gray-600 dark:text-gray-300 text-sm font-medium">
              <tr>
                <th className="w-[20%] px-4 py-3 text-center">Date</th>
                <th className="w-[40%] px-4 py-3 text-center">Description</th>
                <th className="w-[20%] px-4 py-3 text-center">Category</th>
                <th className="w-[30%] px-4 py-3 text-center">Amount</th>
                <th className="w-[10%] px-4 py-3 text-center">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-[#252736] divide-y divide-gray-200 dark:divide-[#3A3B4E] text-center">
              {filteredTransactions.map((t) => (
                <tr key={t.id} className="hover:bg-gray-100 dark:hover:bg-[#2A2B3D] transition">
                  <td className="px-4 py-4 text-gray-800 dark:text-white">{formatDate(t.date)}</td>
                  <td className="px-4 py-4 text-gray-800 dark:text-white">{t.details}</td>
                  <td className="px-4 py-4 relative align-middle">
                    <div className="flex items-center justify-center">
                      {editingCategoryId === t.id ? (
                        <div className="relative w-full max-w-[180px]">
                          <CategoryDropdown
                            transaction={t}
                            categories={categories}
                            onChange={(newCategoryId) => {
                              setTempCategoryId(newCategoryId);
                              handleCategoryChange(t.id, newCategoryId);
                            }}
                            onClose={() => {
                              setEditingCategoryId(null);
                              setTempCategoryId(null);
                            }}
                          />
                        </div>
                      ) : (
                        <button
                          onClick={() => {
                            setEditingCategoryId(t.id);
                            setTempCategoryId(t.category?.id ?? '');
                          }}
                          className="flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium transition duration-150 hover:brightness-110 hover:underline cursor-pointer"
                          style={{
                            backgroundColor: t.category ? `${t.category.color}20` : '#e5e7eb',
                            color: t.category ? t.category.color : '#9ca3af',
                          }}
                          title="Click to change category"
                        >
                          {t.category ? t.category.name : 'Uncategorized'}
                          <Edit className="w-3 h-3" />
                        </button>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-4 font-medium">
                    {t.money_in > 0 ? (
                      <span className="text-green-600 dark:text-green-400">+{formatAmount(t.money_in, t.currency)}</span>
                    ) : (
                      <span className="text-red-600 dark:text-red-400">-{formatAmount(t.money_out, t.currency)}</span>
                    )}
                  </td>
                  <td className="px-4 py-4 flex justify-center items-center gap-2">
                    <button
                      className="text-blue-600 dark:text-[#8B5CF6] hover:text-blue-800 dark:hover:text-[#A78BFA] p-2 rounded hover:bg-blue-50 dark:hover:bg-[#3A3B4E] transition"
                      onClick={() => {
                        setEditingTransaction(t);
                        setShowForm(true);
                      }}
                    >
                      <Edit className="w-5 h-5" />
                    </button>
                    <button
                      className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-500 p-2 rounded hover:bg-red-50 dark:hover:bg-[#3A3B4E] transition"
                      onClick={() => handleDelete(t.id)}
                    >
                      <Trash2 className="w-5 h-5" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {filteredTransactions.length === 0 && (
            <p className="p-4 text-center text-gray-400 dark:text-gray-500">No transactions found.</p>
          )}
        </div>
      </div>

      {/* Modals */}
      <TransactionForm isOpen={showForm} transaction={editingTransaction} onClose={() => setShowForm(false)} />
      <ReceiptScanner isOpen={showOCRScanner} onClose={() => setShowOCRScanner(false)} />
    </>
  );
};

export default Transactions;
