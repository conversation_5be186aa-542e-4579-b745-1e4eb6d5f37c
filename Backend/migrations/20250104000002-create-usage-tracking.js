'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('usage_tracking', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      feature_type: {
        type: Sequelize.ENUM('ocr_scan', 'excel_import'),
        allowNull: false,
        comment: 'Type of feature being tracked'
      },
      usage_month: {
        type: Sequelize.INTEGER,
        allowNull: false,
        validate: {
          min: 1,
          max: 12
        },
        comment: 'Month of usage (1-12)'
      },
      usage_year: {
        type: Sequelize.INTEGER,
        allowNull: false,
        validate: {
          min: 2020
        },
        comment: 'Year of usage'
      },
      usage_count: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        allowNull: false,
        validate: {
          min: 0
        },
        comment: 'Number of times feature was used this month'
      },
      last_used_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Last time this feature was used'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // Add unique constraint
    await queryInterface.addConstraint('usage_tracking', {
      fields: ['user_id', 'feature_type', 'usage_month', 'usage_year'],
      type: 'unique',
      name: 'unique_user_feature_month_year'
    });

    // Add indexes
    await queryInterface.addIndex('usage_tracking', ['user_id']);
    await queryInterface.addIndex('usage_tracking', ['feature_type']);
    await queryInterface.addIndex('usage_tracking', ['usage_month', 'usage_year']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('usage_tracking');
  }
};
