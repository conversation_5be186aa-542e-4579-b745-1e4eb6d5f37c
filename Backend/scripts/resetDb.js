const { sequelize } = require("../models");

async function resetDatabase() {
  try {
    console.log("🔄 Starting database reset...");

    // Disable foreign key checks
    await sequelize.query("SET FOREIGN_KEY_CHECKS = 0;");
    console.log("✅ Foreign key checks disabled");

    // Drop all tables to avoid constraint issues
    await sequelize.drop({ cascade: true });
    console.log("✅ All tables dropped successfully");

    // Re-enable foreign key checks
    await sequelize.query("SET FOREIGN_KEY_CHECKS = 1;");
    console.log("✅ Foreign key checks re-enabled");

    // Recreate all tables with proper relationships
    await sequelize.sync({ force: true });
    console.log("✅ All tables recreated successfully");

    // Seed default categories
    const { Category } = sequelize.models;
    const defaultCategories = [
      {
        name: "Food & Dining",
        name_km: "អាហារ និង ភោជនីយដ្ឋាន",
        type: "expense",
        color: "#FF6B6B",
        icon: "🍽️",
      },
      {
        name: "Transportation",
        name_km: "ការដឹកជញ្ជូន",
        type: "expense",
        color: "#4ECDC4",
        icon: "🚗",
      },
      {
        name: "Shopping",
        name_km: "ការទិញទំនិញ",
        type: "expense",
        color: "#45B7D1",
        icon: "🛍️",
      },
      {
        name: "Entertainment",
        name_km: "កម្សាន្ត",
        type: "expense",
        color: "#96CEB4",
        icon: "🎬",
      },
      {
        name: "Bills & Utilities",
        name_km: "វិក្កយបត្រ និង សេវាកម្ម",
        type: "expense",
        color: "#FFEAA7",
        icon: "💡",
      },
      {
        name: "Healthcare",
        name_km: "សុខភាព",
        type: "expense",
        color: "#DDA0DD",
        icon: "🏥",
      },
      {
        name: "Education",
        name_km: "ការអប់រំ",
        type: "expense",
        color: "#98D8C8",
        icon: "📚",
      },
      {
        name: "Travel",
        name_km: "ការធ្វើដំណើរ",
        type: "expense",
        color: "#F7DC6F",
        icon: "✈️",
      },
      {
        name: "Personal Care",
        name_km: "ការថែទាំខ្លួន",
        type: "expense",
        color: "#BB8FCE",
        icon: "💄",
      },
      {
        name: "Other Expenses",
        name_km: "ការចំណាយផ្សេងៗ",
        type: "expense",
        color: "#85C1E9",
        icon: "📦",
      },
      {
        name: "Salary",
        name_km: "ប្រាក់ខែ",
        type: "income",
        color: "#58D68D",
        icon: "💰",
      },
      {
        name: "Other Income",
        name_km: "ចំណូលផ្សេងៗ",
        type: "income",
        color: "#52C41A",
        icon: "💵",
      },
    ];

    await Category.bulkCreate(defaultCategories);
    console.log("✅ Default categories seeded successfully");

    console.log("🎉 Database reset completed successfully!");
    process.exit(0);
  } catch (error) {
    console.error("❌ Database reset failed:", error);
    process.exit(1);
  }
}

resetDatabase();
