/**
 * QR File Manager - Handles QR code file lifecycle with automatic cleanup
 */

const fs = require('fs').promises;
const path = require('path');
const { logger } = require('../utils/logger');

class QRFileManager {
    constructor(tempDir = path.join(__dirname, '../temp')) {
        this.tempDir = path.join(__dirname, tempDir);
        this.activeQRFiles = new Map(); // Track QR files with expiry times
        this.cleanupInterval = null;
        
        // Initialize cleanup interval every 60 seconds (less frequent)
        this.startCleanupScheduler();
    }

    /**
     * Register a QR file with automatic cleanup after 30 minutes
     */
    async registerQRFile(billNumber, filePath, expirySeconds = 1800) {
        try {
            const expiryTime = Date.now() + (expirySeconds * 1000);
            
            this.activeQRFiles.set(billNumber, {
                filePath,
                expiryTime,
                createdAt: new Date().toISOString()
            });

            logger.info(`📄 QR file registered: ${billNumber}, expires in ${expirySeconds}s`, {
                filePath,
                expiryTime: new Date(expiryTime).toISOString()
            });

            return {
                billNumber,
                filePath,
                expiryTime,
                ttl: expirySeconds
            };

        } catch (error) {
            logger.error('Failed to register QR file', { billNumber, error: error.message });
            throw error;
        }
    }

    /**
     * Remove QR file immediately (called when payment is completed)
     */
    async removeQRFile(billNumber, reason = 'manual') {
        try {
            const qrFile = this.activeQRFiles.get(billNumber);
            
            if (!qrFile) {
                logger.warn(`QR file not found for removal: ${billNumber}`);
                return false;
            }

            // Delete the physical file
            try {
                await fs.access(qrFile.filePath);
                await fs.unlink(qrFile.filePath);
                logger.info(`🗑️ QR file deleted: ${billNumber} (${reason})`, {
                    filePath: qrFile.filePath
                });
            } catch (fileError) {
                if (fileError.code !== 'ENOENT') {
                    logger.warn(`Could not delete QR file: ${qrFile.filePath}`, {
                        error: fileError.message
                    });
                }
            }

            // Remove from tracking
            this.activeQRFiles.delete(billNumber);

            return true;

        } catch (error) {
            logger.error('Failed to remove QR file', { billNumber, error: error.message });
            throw error;
        }
    }

    /**
     * Start the cleanup scheduler - runs every 60 seconds
     */
    startCleanupScheduler() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }

        this.cleanupInterval = setInterval(() => {
            this.cleanupExpiredFiles();
        }, 60000); // 60 seconds

        logger.info('🕐 QR File cleanup scheduler started (60s interval)');
    }    /**
     * Stop the cleanup scheduler
     */
    stopCleanupScheduler() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
            logger.info('🛑 QR File cleanup scheduler stopped');
        }
    }

    /**
     * Clean up expired QR files
     */
    async cleanupExpiredFiles() {
        const now = Date.now();
        const expiredFiles = [];

        // Find expired files
        for (const [billNumber, qrFile] of this.activeQRFiles.entries()) {
            if (now > qrFile.expiryTime) {
                expiredFiles.push(billNumber);
            }
        }

        // Remove expired files
        for (const billNumber of expiredFiles) {
            await this.removeQRFile(billNumber, 'expired');
        }

        if (expiredFiles.length > 0) {
            logger.info(`🧹 Cleaned up ${expiredFiles.length} expired QR files`);
        }
    }

    /**
     * Get QR file info
     */
    getQRFileInfo(billNumber) {
        const qrFile = this.activeQRFiles.get(billNumber);
        if (!qrFile) {
            return null;
        }

        const now = Date.now();
        const remainingTime = Math.max(0, qrFile.expiryTime - now);

        return {
            billNumber,
            filePath: qrFile.filePath,
            createdAt: qrFile.createdAt,
            expiryTime: new Date(qrFile.expiryTime).toISOString(),
            remainingSeconds: Math.floor(remainingTime / 1000),
            isExpired: remainingTime === 0
        };
    }

    /**
     * List all active QR files
     */
    listActiveQRFiles() {
        const files = [];
        for (const [billNumber] of this.activeQRFiles.entries()) {
            files.push(this.getQRFileInfo(billNumber));
        }
        return files;
    }

    /**
     * Clean up all QR files and stop scheduler (for graceful shutdown)
     */
    async shutdown() {
        logger.info('🔄 Shutting down QR File Manager...');
        
        this.stopCleanupScheduler();
        
        // Clean up all remaining files
        const remainingFiles = Array.from(this.activeQRFiles.keys());
        for (const billNumber of remainingFiles) {
            await this.removeQRFile(billNumber, 'shutdown');
        }
        
        logger.info('✅ QR File Manager shutdown complete');
    }
}

module.exports = QRFileManager;
