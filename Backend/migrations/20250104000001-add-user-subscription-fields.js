'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Add role field to users table
    await queryInterface.addColumn('users', 'role', {
      type: Sequelize.ENUM('Freemium', 'Premium'),
      defaultValue: 'Freemium',
      allowNull: false,
      comment: 'User subscription role'
    });

    // Add subscription expiration field
    await queryInterface.addColumn('users', 'subscription_expires_at', {
      type: Sequelize.DATE,
      allowNull: true,
      comment: 'Premium subscription expiration date'
    });

    // Set all existing users to Freemium role
    await queryInterface.sequelize.query(
      "UPDATE users SET role = 'Freemium' WHERE role IS NULL"
    );
  },

  async down(queryInterface, Sequelize) {
    // Remove the columns
    await queryInterface.removeColumn('users', 'subscription_expires_at');
    await queryInterface.removeColumn('users', 'role');
  }
};
