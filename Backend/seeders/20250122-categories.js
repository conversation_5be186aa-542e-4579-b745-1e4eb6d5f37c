'use strict';

const { v4: uuidv4 } = require('uuid');

module.exports = {
  async up(queryInterface, Sequelize) {
    const categories = [
      // Income Categories
      {
        id: uuidv4(),
        name: 'Salary',
        name_km: 'ប្រាក់ខែ',
        type: 'income',
        icon: 'briefcase',
        color: '#22c55e',
        description: 'Monthly salary and wages',
        description_km: 'ប្រាក់ខែ និងប្រាក់ឈ្នួល',
        is_default: true,
        is_active: true,
        sort_order: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        name: 'Business',
        name_km: 'អាជីវកម្ម',
        type: 'income',
        icon: 'building',
        color: '#3b82f6',
        description: 'Business income and profits',
        description_km: 'ចំណូលពីអាជីវកម្ម',
        is_default: true,
        is_active: true,
        sort_order: 2,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        name: 'Investment',
        name_km: 'ការវិនិយោគ',
        type: 'income',
        icon: 'trending-up',
        color: '#10b981',
        description: 'Investment returns and dividends',
        description_km: 'ចំណូលពីការវិនិយោគ',
        is_default: true,
        is_active: true,
        sort_order: 3,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        name: 'Other Income',
        name_km: 'ចំណូលផ្សេងៗ',
        type: 'income',
        icon: 'plus-circle',
        color: '#8b5cf6',
        description: 'Other sources of income',
        description_km: 'ចំណូលពីប្រភពផ្សេងៗ',
        is_default: true,
        is_active: true,
        sort_order: 4,
        created_at: new Date(),
        updated_at: new Date()
      },

      // Expense Categories
      {
        id: uuidv4(),
        name: 'Food & Dining',
        name_km: 'អាហារ និងភោជនីយដ្ឋាន',
        type: 'expense',
        icon: 'utensils',
        color: '#ef4444',
        description: 'Groceries, restaurants, and food expenses',
        description_km: 'ការចំណាយលើអាហារ និងភោជនីយដ្ឋាន',
        is_default: true,
        is_active: true,
        sort_order: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        name: 'Transportation',
        name_km: 'ការដឹកជញ្ជូន',
        type: 'expense',
        icon: 'car',
        color: '#f59e0b',
        description: 'Gas, public transport, car maintenance',
        description_km: 'ប្រេងឥន្ធនៈ ការដឹកជញ្ជូនសាធារណៈ',
        is_default: true,
        is_active: true,
        sort_order: 2,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        name: 'Shopping',
        name_km: 'ការទិញទំនិញ',
        type: 'expense',
        icon: 'shopping-bag',
        color: '#ec4899',
        description: 'Clothing, electronics, and general shopping',
        description_km: 'សម្លៀកបំពាក់ គ្រឿងអេឡិចត្រូនិច',
        is_default: true,
        is_active: true,
        sort_order: 3,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        name: 'Entertainment',
        name_km: 'កម្សាន្ត',
        type: 'expense',
        icon: 'film',
        color: '#8b5cf6',
        description: 'Movies, games, and entertainment',
        description_km: 'ភាពយន្ត ហ្គេម និងកម្សាន្ត',
        is_default: true,
        is_active: true,
        sort_order: 4,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        name: 'Bills & Utilities',
        name_km: 'វិក័យប័ត្រ និងសេវាកម្ម',
        type: 'expense',
        icon: 'receipt',
        color: '#6b7280',
        description: 'Electricity, water, internet, phone bills',
        description_km: 'អគ្គិសនី ទឹក អ៊ីនធឺណិត ទូរស័ព្ទ',
        is_default: true,
        is_active: true,
        sort_order: 5,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        name: 'Healthcare',
        name_km: 'សុខភាព',
        type: 'expense',
        icon: 'heart',
        color: '#dc2626',
        description: 'Medical expenses, insurance, pharmacy',
        description_km: 'ការចំណាយលើសុខភាព ឱសថ',
        is_default: true,
        is_active: true,
        sort_order: 6,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        name: 'Education',
        name_km: 'អប់រំ',
        type: 'expense',
        icon: 'book',
        color: '#2563eb',
        description: 'School fees, books, courses',
        description_km: 'ថ្លៃសាលា សៀវភៅ វគ្គសិក្សា',
        is_default: true,
        is_active: true,
        sort_order: 7,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        name: 'Housing',
        name_km: 'លំនៅដ្ឋាន',
        type: 'expense',
        icon: 'home',
        color: '#059669',
        description: 'Rent, mortgage, home maintenance',
        description_km: 'ថ្លៃជួល ការថែទាំផ្ទះ',
        is_default: true,
        is_active: true,
        sort_order: 8,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    await queryInterface.bulkInsert('categories', categories);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('categories', { is_default: true });
  }
};
