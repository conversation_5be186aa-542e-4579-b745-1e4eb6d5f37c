/**
 * Rate limiter utility for Finwise Telegram Bot
 */

const rateLimit = require('express-rate-limit');
const { logger } = require('./logger');

// Create different rate limiters for different endpoints
const createRateLimiter = (options = {}) => {
  const defaultOptions = {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: {
      error: 'Too many requests',
      message: 'Too many requests from this IP, please try again later.',
      retryAfter: Math.ceil(options.windowMs / 1000) || 900
    },
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    handler: (req, res) => {
      logger.warn('Rate limit exceeded', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        path: req.path,
        method: req.method
      });
      
      res.status(429).json({
        error: 'Too many requests',
        message: 'Please wait before making more requests.',
        retryAfter: <PERSON>.ceil(options.windowMs / 1000) || 900
      });
    }
  };

  return rateLimit({
    ...defaultOptions,
    ...options
  });
};

// Telegram webhook rate limiter (more restrictive)
const telegramWebhookLimiter = createRateLimiter({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 30, // limit each IP to 30 requests per minute
  skipSuccessfulRequests: false,
  message: {
    error: 'Telegram webhook rate limit exceeded',
    message: 'Too many webhook requests, please slow down.'
  }
});

// API calls rate limiter (for bot making calls to backend)
const apiCallLimiter = createRateLimiter({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 60, // limit to 60 API calls per minute
  skipSuccessfulRequests: true, // Don't count successful requests
  message: {
    error: 'API rate limit exceeded',
    message: 'Too many API calls, please try again later.'
  }
});

// Health check rate limiter (very permissive)
const healthCheckLimiter = createRateLimiter({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 10, // limit to 10 health checks per minute
  message: {
    error: 'Health check rate limit exceeded',
    message: 'Too many health check requests.'
  }
});

// Per-user rate limiter for Telegram interactions
class UserRateLimiter {
  constructor() {
    this.users = new Map();
    this.cleanup();
  }

  // Check if user is rate limited
  isRateLimited(userId, maxRequests = 20, windowMs = 60000) {
    const now = Date.now();
    const userKey = userId.toString();
    
    if (!this.users.has(userKey)) {
      this.users.set(userKey, {
        requests: [],
        firstRequest: now
      });
    }

    const userData = this.users.get(userKey);
    
    // Remove old requests outside the window
    userData.requests = userData.requests.filter(
      timestamp => now - timestamp < windowMs
    );

    // Check if user exceeded the limit
    if (userData.requests.length >= maxRequests) {
      logger.warn('User rate limit exceeded', {
        userId,
        requestCount: userData.requests.length,
        maxRequests,
        windowMs
      });
      return true;
    }

    // Add current request
    userData.requests.push(now);
    return false;
  }

  // Get remaining requests for a user
  getRemainingRequests(userId, maxRequests = 20, windowMs = 60000) {
    const now = Date.now();
    const userKey = userId.toString();
    
    if (!this.users.has(userKey)) {
      return maxRequests;
    }

    const userData = this.users.get(userKey);
    const validRequests = userData.requests.filter(
      timestamp => now - timestamp < windowMs
    );

    return Math.max(0, maxRequests - validRequests.length);
  }

  // Cleanup old user data periodically
  cleanup() {
    setInterval(() => {
      const now = Date.now();
      const oneHour = 60 * 60 * 1000;

      for (const [userId, userData] of this.users.entries()) {
        // Remove users with no recent activity
        const hasRecentActivity = userData.requests.some(
          timestamp => now - timestamp < oneHour
        );

        if (!hasRecentActivity) {
          this.users.delete(userId);
        }
      }

      logger.info('User rate limiter cleanup completed', {
        activeUsers: this.users.size
      });
    }, 15 * 60 * 1000); // Cleanup every 15 minutes
  }

  // Middleware for Telegram bot commands
  telegramMiddleware(maxRequests = 20, windowMs = 60000) {
    return (ctx, next) => {
      const userId = ctx.from?.id;
      
      if (!userId) {
        return next();
      }

      if (this.isRateLimited(userId, maxRequests, windowMs)) {
        const remaining = this.getRemainingRequests(userId, maxRequests, windowMs);
        
        ctx.reply(`⚠️ Slow down! You've made too many requests. Please wait a moment before trying again.\n\nRequests remaining: ${remaining}`);
        return;
      }

      return next();
    };
  }
}

// Create singleton instance
const userRateLimiter = new UserRateLimiter();

module.exports = {
  createRateLimiter,
  telegramWebhookLimiter,
  apiCallLimiter,
  healthCheckLimiter,
  userRateLimiter
};
