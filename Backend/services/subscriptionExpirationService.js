const { User, Subscription } = require('../models');
const cron = require('node-cron');

class SubscriptionExpirationService {
  constructor() {
    this.isRunning = false;
    this.notificationIntervals = [7, 3, 1]; // Days before expiration to send notifications
    console.log('📅 Subscription Expiration Service initialized');
  }

  /**
   * Start the subscription monitoring service
   */
  start() {
    if (this.isRunning) return;

    this.isRunning = true;

    // Run daily at 9:00 AM
    this.dailyCheck = cron.schedule('0 9 * * *', async () => {
      await this.checkExpiringSubscriptions();
      await this.processExpiredSubscriptions();
    }, {
      scheduled: false
    });

    // Run hourly check for expired subscriptions
    this.hourlyCheck = cron.schedule('0 * * * *', async () => {
      await this.processExpiredSubscriptions();
    }, {
      scheduled: false
    });

    this.dailyCheck.start();
    this.hourlyCheck.start();

    console.log('📅 Subscription expiration monitoring started');
    console.log('⏰ Daily notifications check: 9:00 AM');
    console.log('⏰ Hourly expiration check: Every hour');
  }

  /**
   * Stop the subscription monitoring service
   */
  stop() {
    if (!this.isRunning) return;

    this.isRunning = false;

    if (this.dailyCheck) {
      this.dailyCheck.stop();
    }

    if (this.hourlyCheck) {
      this.hourlyCheck.stop();
    }

    console.log('📅 Subscription expiration monitoring stopped');
  }

  /**
   * Check for subscriptions expiring soon and send notifications
   */
  async checkExpiringSubscriptions() {
    try {
      console.log('🔍 Checking for expiring subscriptions...');

      for (const daysAhead of this.notificationIntervals) {
        const expiringSubscriptions = await Subscription.getExpiringSubscriptions(daysAhead);
        
        for (const subscription of expiringSubscriptions) {
          await this.sendExpirationNotification(subscription, daysAhead);
        }
      }

      console.log('✅ Expiring subscriptions check completed');

    } catch (error) {
      console.error('❌ Error checking expiring subscriptions:', error);
    }
  }

  /**
   * Process expired subscriptions and downgrade users
   */
  async processExpiredSubscriptions() {
    try {
      console.log('🔍 Processing expired subscriptions...');

      const expiredUsers = await User.findAll({
        where: {
          role: 'Premium',
          subscription_expires_at: {
            [User.sequelize.Op.lt]: new Date()
          }
        },
        include: [{
          model: Subscription,
          as: 'subscriptions',
          where: {
            status: 'active'
          },
          required: false
        }]
      });

      let processedCount = 0;

      for (const user of expiredUsers) {
        await this.processExpiredUser(user);
        processedCount++;
      }

      if (processedCount > 0) {
        console.log(`✅ Processed ${processedCount} expired subscription(s)`);
      }

    } catch (error) {
      console.error('❌ Error processing expired subscriptions:', error);
    }
  }

  /**
   * Process a single expired user
   */
  async processExpiredUser(user) {
    try {
      console.log(`⏰ Processing expired subscription for user: ${user.id}`);

      // Downgrade user to Freemium
      await user.downgradeToFreemium();

      // Mark active subscriptions as expired
      const activeSubscriptions = await Subscription.findAll({
        where: {
          user_id: user.id,
          status: 'active'
        }
      });

      for (const subscription of activeSubscriptions) {
        await subscription.update({
          status: 'expired'
        });
      }

      console.log(`✅ User ${user.id} downgraded to Freemium`);

      // Send expiration notification
      await this.sendSubscriptionExpiredNotification(user);

    } catch (error) {
      console.error(`❌ Error processing expired user ${user.id}:`, error);
    }
  }

  /**
   * Send expiration warning notification
   */
  async sendExpirationNotification(subscription, daysUntilExpiry) {
    try {
      const user = subscription.user;
      
      console.log(`📧 Sending ${daysUntilExpiry}-day expiration warning to user: ${user.id}`);

      // TODO: Implement actual notification sending (email, push notification, etc.)
      // For now, just log the notification
      const message = this.getExpirationMessage(daysUntilExpiry);
      
      console.log(`📧 Notification for ${user.email}: ${message}`);

      // You can implement email sending here
      // await this.sendEmail(user.email, 'Subscription Expiring Soon', message);

      // Or push notifications
      // await this.sendPushNotification(user.id, message);

    } catch (error) {
      console.error('❌ Error sending expiration notification:', error);
    }
  }

  /**
   * Send subscription expired notification
   */
  async sendSubscriptionExpiredNotification(user) {
    try {
      console.log(`📧 Sending subscription expired notification to user: ${user.id}`);

      const message = 'Your Premium subscription has expired. You have been downgraded to the Freemium plan. Upgrade again to continue enjoying unlimited access to all features.';
      
      console.log(`📧 Expiration notification for ${user.email}: ${message}`);

      // TODO: Implement actual notification sending
      // await this.sendEmail(user.email, 'Subscription Expired', message);

    } catch (error) {
      console.error('❌ Error sending expiration notification:', error);
    }
  }

  /**
   * Get expiration warning message based on days until expiry
   */
  getExpirationMessage(daysUntilExpiry) {
    switch (daysUntilExpiry) {
      case 7:
        return 'Your Premium subscription expires in 7 days. Renew now to continue enjoying unlimited access to all features.';
      case 3:
        return 'Your Premium subscription expires in 3 days. Don\'t lose access to unlimited features - renew today!';
      case 1:
        return 'Your Premium subscription expires tomorrow! Renew now to avoid interruption of service.';
      default:
        return `Your Premium subscription expires in ${daysUntilExpiry} days. Renew to continue unlimited access.`;
    }
  }

  /**
   * Manual check for testing purposes
   */
  async runManualCheck() {
    console.log('🔧 Running manual subscription check...');
    await this.checkExpiringSubscriptions();
    await this.processExpiredSubscriptions();
    console.log('✅ Manual subscription check completed');
  }

  /**
   * Get subscription statistics
   */
  async getSubscriptionStats() {
    try {
      const stats = {
        totalPremiumUsers: 0,
        activeSubscriptions: 0,
        expiredSubscriptions: 0,
        expiringIn7Days: 0,
        expiringIn3Days: 0,
        expiringIn1Day: 0
      };

      // Count premium users
      stats.totalPremiumUsers = await User.count({
        where: { role: 'Premium' }
      });

      // Count active subscriptions
      stats.activeSubscriptions = await Subscription.count({
        where: { status: 'active' }
      });

      // Count expired subscriptions
      stats.expiredSubscriptions = await Subscription.count({
        where: { status: 'expired' }
      });

      // Count expiring subscriptions
      for (const days of [7, 3, 1]) {
        const expiring = await Subscription.getExpiringSubscriptions(days);
        stats[`expiringIn${days}Day${days > 1 ? 's' : ''}`] = expiring.length;
      }

      return stats;

    } catch (error) {
      console.error('❌ Error getting subscription stats:', error);
      throw error;
    }
  }
}

module.exports = SubscriptionExpirationService;
