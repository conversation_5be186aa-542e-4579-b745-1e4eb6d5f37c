const { Sequelize } = require('sequelize');
require('dotenv').config();

class DatabaseService {
  constructor() {
    this.sequelize = null;
    this.isConnected = false;
  }

  async connect() {
    // Primary configuration (cloud database)
    const primaryConfig = {
      username: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      dialect: process.env.DB_DIALECT,
      dialectOptions: {
        ssl: {
          require: true,
          rejectUnauthorized: false
        },
        connectTimeout: 30000,
        acquireTimeout: 30000,
        timeout: 30000
      },
      pool: {
        max: 5,
        min: 0,
        acquire: 30000,
        idle: 10000
      },
      logging: false
    };

    // Fallback configuration (local database)
    const fallbackConfig = {
      username: 'root',
      password: '',
      database: 'finwise_local',
      host: '127.0.0.1',
      port: 3306,
      dialect: 'mysql',
      pool: {
        max: 5,
        min: 0,
        acquire: 30000,
        idle: 10000
      },
      logging: false
    };

    try {
      console.log('🔄 Attempting to connect to primary database...');
      this.sequelize = new Sequelize(
        primaryConfig.database,
        primaryConfig.username,
        primaryConfig.password,
        primaryConfig
      );

      await this.sequelize.authenticate();
      console.log('✅ Primary database connection established successfully.');
      this.isConnected = true;
      return this.sequelize;

    } catch (error) {
      console.log('⚠️ Primary database connection failed:', error.message);
      console.log('🔄 Attempting fallback to local database...');

      try {
        this.sequelize = new Sequelize(
          fallbackConfig.database,
          fallbackConfig.username,
          fallbackConfig.password,
          fallbackConfig
        );

        await this.sequelize.authenticate();
        console.log('✅ Fallback database connection established successfully.');
        this.isConnected = true;
        return this.sequelize;

      } catch (fallbackError) {
        console.log('❌ Fallback database connection also failed:', fallbackError.message);
        console.log('🔄 Using in-memory SQLite as last resort...');

        // Last resort: in-memory SQLite
        this.sequelize = new Sequelize('sqlite::memory:', {
          logging: false
        });

        await this.sequelize.authenticate();
        console.log('✅ In-memory SQLite database initialized.');
        this.isConnected = true;
        return this.sequelize;
      }
    }
  }

  getSequelize() {
    return this.sequelize;
  }

  isConnectionActive() {
    return this.isConnected;
  }

  async disconnect() {
    if (this.sequelize) {
      await this.sequelize.close();
      this.isConnected = false;
      console.log('🔌 Database connection closed.');
    }
  }
}

module.exports = new DatabaseService();
