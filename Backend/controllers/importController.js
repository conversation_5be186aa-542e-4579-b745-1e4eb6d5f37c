const excelImportService = require("../services/excelImportService");
const fileImportService = require("../services/fileImportService");
const { Transaction, ImportLog } = require("../models");
const multer = require("multer");
const path = require("path");
const fs = require("fs");

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow Excel, CSV, and PDF files
    const allowedExtensions = [".xlsx", ".xls", ".csv", ".pdf"];
    const allowedMimeTypes = [
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "application/vnd.ms-excel",
      "text/csv",
      "application/csv",
      "application/pdf",
    ];

    const fileExtension = path.extname(file.originalname).toLowerCase();

    if (
      allowedExtensions.includes(fileExtension) ||
      allowedMimeTypes.includes(file.mimetype)
    ) {
      cb(null, true);
    } else {
      cb(
        new Error(
          "Only Excel (.xlsx, .xls), CSV (.csv), and PDF (.pdf) files are allowed"
        ),
        false
      );
    }
  },
});

class ImportController {
  /**
   * Upload and preview Excel file before import
   */
  async previewImport(req, res) {
    try {
      const userId = req.user.id;
      const fileBuffer = req.file.buffer;
      const fileName = req.file.originalname;

      console.log(
        `📊 Preview import request from user ${userId} for file: ${fileName}`
      );

      // Validate file using new file import service
      const validation = fileImportService.validateImportFile(
        fileBuffer,
        fileName
      );
      if (!validation.valid) {
        return res.status(400).json({
          success: false,
          error: validation.error,
        });
      }

      // Process file for preview using new multi-format service
      const importResult = await fileImportService.processFile(
        fileBuffer,
        fileName,
        userId,
        {
          previewOnly: true,
        }
      );

      if (!importResult.success) {
        return res.status(400).json({
          success: false,
          error: importResult.error,
        });
      }

      // Return preview data (first 10 transactions + summary)
      const previewTransactions = importResult.transactions.slice(0, 10);

      res.json({
        success: true,
        preview: true,
        format: importResult.format,
        summary: {
          totalRows: importResult.totalRows,
          processedTransactions: importResult.processedTransactions,
          uniqueTransactions: importResult.uniqueTransactions,
          duplicates: importResult.duplicates,
          errors: importResult.errors,
        },
        sampleTransactions: previewTransactions,
        duplicateTransactions: importResult.duplicateTransactions.slice(0, 5),
        errorTransactions: importResult.errorTransactions.slice(0, 5),
        metadata: importResult.metadata,
      });
    } catch (error) {
      console.error("❌ Preview import error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to preview import file",
      });
    }
  }

  /**
   * Execute the actual import after preview confirmation
   */
  async executeImport(req, res) {
    try {
      const userId = req.user.id;

      // Validate file upload
      if (!req.file) {
        return res.status(400).json({
          success: false,
          error: "No file uploaded. Please select an Excel file.",
        });
      }

      const fileBuffer = req.file.buffer;
      const fileName = req.file.originalname;
      const { skipDuplicates = true, importMode = "safe" } = req.body;

      console.log(
        `📊 Execute import request from user ${userId} for file: ${fileName}`
      );

      // Process file using new multi-format service
      const importResult = await fileImportService.processFile(
        fileBuffer,
        fileName,
        userId,
        {
          skipDuplicates,
          importMode,
        }
      );

      if (!importResult.success) {
        return res.status(400).json({
          success: false,
          error: importResult.error,
        });
      }

      // Save transactions to database
      const savedTransactions = [];
      const failedTransactions = [];

      for (const transaction of importResult.transactions) {
        if (transaction.hasError) {
          failedTransactions.push(transaction);
          continue;
        }

        try {
          const savedTransaction = await Transaction.create({
            user_id: userId,
            date: transaction.date,
            details: transaction.details,
            money_in: transaction.money_in,
            money_out: transaction.money_out,
            currency: transaction.currency,
            imported_from: transaction.imported_from,
            category_id: null, // Will be categorized later
            balance: transaction.balance,
          });

          savedTransactions.push(savedTransaction);
        } catch (dbError) {
          console.error(
            `❌ Failed to save transaction from row ${transaction.rowNumber}:`,
            dbError
          );
          failedTransactions.push({
            ...transaction,
            hasError: true,
            error: `Database error: ${dbError.message}`,
          });
        }
      }

      // Log the import
      await ImportLog.create({
        user_id: userId,
        filename: fileName,
        file_type: "excel",
        row_count: importResult.totalRows,
        inserted_count: savedTransactions.length,
        duplicate_count: importResult.duplicates,
        error_count: failedTransactions.length,
        status: savedTransactions.length > 0 ? "completed" : "failed",
        column_mapping: { format: importResult.format },
        notes: JSON.stringify(importResult.metadata),
      });

      res.json({
        success: true,
        imported: true,
        format: importResult.format,
        summary: {
          totalRows: importResult.totalRows,
          processedTransactions: importResult.processedTransactions,
          importedTransactions: savedTransactions.length,
          duplicates: importResult.duplicates,
          errors: failedTransactions.length,
        },
        importedTransactions: savedTransactions.length,
        duplicateTransactions: importResult.duplicateTransactions,
        errorTransactions: failedTransactions,
        metadata: importResult.metadata,
      });
    } catch (error) {
      console.error("❌ Execute import error:", error);

      // Log failed import
      try {
        await ImportLog.create({
          user_id: req.user.id,
          filename: req.file?.originalname || "unknown",
          file_type: "excel",
          row_count: 0,
          inserted_count: 0,
          duplicate_count: 0,
          error_count: 0,
          status: "failed",
          error_details: { message: error.message },
        });
      } catch (logError) {
        console.error("Failed to log import error:", logError);
      }

      res.status(500).json({
        success: false,
        error: "Failed to execute import",
      });
    }
  }

  /**
   * Download sample template for ACLEDA or ABA format
   */
  async downloadTemplate(req, res) {
    try {
      const { format } = req.params;

      if (!["acleda", "aba"].includes(format.toLowerCase())) {
        return res.status(400).json({
          success: false,
          error: 'Invalid format. Use "acleda" or "aba"',
        });
      }

      const templateBuffer = excelImportService.createSampleTemplate(
        format.toUpperCase()
      );

      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${format.toUpperCase()}_Sample_Template.xlsx"`
      );

      res.send(templateBuffer);
    } catch (error) {
      console.error("❌ Download template error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to generate template",
      });
    }
  }

  /**
   * Get import history for user
   */
  async getImportHistory(req, res) {
    try {
      const userId = req.user.id;
      const { page = 1, limit = 10 } = req.query;

      const offset = (page - 1) * limit;

      const { count, rows: imports } = await ImportLog.findAndCountAll({
        where: { user_id: userId },
        order: [["created_at", "DESC"]],
        limit: parseInt(limit),
        offset: offset,
        attributes: [
          "id",
          "filename",
          "file_type",
          "row_count",
          "inserted_count",
          "duplicate_count",
          "error_count",
          "status",
          "error_details",
          "column_mapping",
          "created_at",
        ],
      });

      res.json({
        success: true,
        imports,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit),
        },
      });
    } catch (error) {
      console.error("❌ Get import history error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch import history",
      });
    }
  }

  /**
   * Get supported formats and their specifications
   */
  async getFormats(req, res) {
    try {
      res.json({
        success: true,
        formats: {
          ACLEDA: {
            name: "ACLEDA Bank Statement",
            description:
              "ACLEDA bank statement format with CASH IN/OUT columns",
            requiredColumns: [
              "DATE",
              "DESCRIPTIONS",
              "CASH OUT (Dr)",
              "CASH IN (Cr)",
            ],
            optionalColumns: ["BALANCE", "CCY"],
            sampleDownload: "/api/import/template/acleda",
          },
          ABA: {
            name: "ABA Bank Statement",
            description: "ABA bank statement format with Money In/Out columns",
            requiredColumns: [
              "Date",
              "Transaction Details",
              "Money In",
              "Money Out",
            ],
            optionalColumns: ["Balance", "Ccy"],
            sampleDownload: "/api/import/template/aba",
          },
        },
        supportedCurrencies: [
          "USD",
          "KHR",
          "VND",
          "EUR",
          "SEK",
          "NOK",
          "DKK",
          "MYR",
          "THB",
          "SGD",
        ],
        maxFileSize: "10MB",
        supportedExtensions: [".xlsx", ".xls"],
      });
    } catch (error) {
      console.error("❌ Get formats error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch format information",
      });
    }
  }
}

const importController = new ImportController();

module.exports = {
  ImportController: importController,
  upload,
  previewImport: importController.previewImport.bind(importController),
  executeImport: importController.executeImport.bind(importController),
  downloadTemplate: importController.downloadTemplate.bind(importController),
  getImportHistory: importController.getImportHistory.bind(importController),
  getFormats: importController.getFormats.bind(importController),
};
