const express = require('express');
const router = express.Router();
const {
  canProvideFeedback,
  submitFeedback,
  getFeedbackHistory,
  skipFeedback,
  getFeedbackStats
} = require('../controllers/feedbackController');
const { authenticateToken } = require('../middleware/auth');
const { body } = require('express-validator');

// Apply authentication to all routes
router.use(authenticateToken);

// Validation middleware for feedback submission
const validateFeedback = [
  body('content')
    .notEmpty()
    .withMessage('Feedback content is required')
    .isLength({ min: 1, max: 2000 })
    .withMessage('Feedback content must be between 1 and 2000 characters'),
  
  body('rating')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  
  body('category')
    .optional()
    .isIn(['general', 'feature_request', 'bug_report', 'improvement', 'other'])
    .withMessage('Invalid feedback category'),
  
  body('isAnonymous')
    .optional()
    .isBoolean()
    .withMessage('isAnonymous must be a boolean value')
];

/**
 * @route   GET /api/feedback/can-provide
 * @desc    Check if user can provide feedback this month
 * @access  Private
 */
router.get('/can-provide', canProvideFeedback);

/**
 * @route   POST /api/feedback/submit
 * @desc    Submit monthly feedback
 * @access  Private
 * @body    content: string (required)
 * @body    rating: number (optional, 1-5)
 * @body    category: string (optional)
 * @body    isAnonymous: boolean (optional)
 */
router.post('/submit', validateFeedback, submitFeedback);

/**
 * @route   GET /api/feedback/history
 * @desc    Get user's feedback history
 * @access  Private
 * @query   page: number (optional, default: 1)
 * @query   limit: number (optional, default: 20)
 */
router.get('/history', getFeedbackHistory);

/**
 * @route   POST /api/feedback/skip
 * @desc    Skip feedback for this month
 * @access  Private
 */
router.post('/skip', skipFeedback);

/**
 * @route   GET /api/feedback/stats
 * @desc    Get feedback statistics (admin only)
 * @access  Private
 * @query   month: number (optional)
 * @query   year: number (optional)
 */
router.get('/stats', getFeedbackStats);

module.exports = router;
