import React from 'react';
import { <PERSON>, <PERSON>, Monitor } from 'lucide-react';
import useThemeStore from '../../store/themeStore';

const ThemeToggle = ({ className = '' }) => {
  const { theme, setTheme } = useThemeStore();

  const themes = [
    { value: 'light', icon: Sun, label: 'Light' },
    { value: 'dark', icon: Moon, label: 'Dark' },
    { value: 'system', icon: Monitor, label: 'System' }
  ];

  return (
    <div className={`flex items-center space-x-1 p-1 bg-gray-100 dark:bg-gray-800 rounded-xl ${className}`}>
      {themes.map(({ value, icon: Icon, label }) => (
        <button
          key={value}
          onClick={() => setTheme(value)}
          className={`
            relative p-2 rounded-lg transition-all duration-300 transform hover:scale-110
            ${theme === value
              ? 'bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-lg'
              : 'text-gray-500 hover:text-gray-700 hover:bg-white/50 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700/50'
            }
          `}
          title={label}
        >
          <Icon className={`w-4 h-4 ${theme === value ? 'animate-fadeInUp' : ''}`} />

          {/* Active indicator */}
          {theme === value && (
            <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-blue-400/20 to-purple-400/20 animate-fadeInUp"></div>
          )}
        </button>
      ))}
    </div>
  );
};

export default ThemeToggle;
