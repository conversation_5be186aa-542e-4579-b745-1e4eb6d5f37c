import React from 'react';
import useAuthStore from '../../store/authStore';

const AuthDebug = () => {
  const { user, isAuthenticated, isLoading } = useAuthStore();

  return (
    // <div className="fixed top-4 right-4 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg border z-50 max-w-sm">
    //   <h3 className="font-bold text-sm mb-2">Auth Debug</h3>
    //   <div className="text-xs space-y-1">
    //     <div>
    //       <strong>isAuthenticated:</strong> {isAuthenticated ? 'true' : 'false'}
    //     </div>
    //     <div>
    //       <strong>isLoading:</strong> {isLoading ? 'true' : 'false'}
    //     </div>
    //     <div>
    //       <strong>user:</strong> {user ? user.name || user.email : 'null'}
    //     </div>
    //     <div>
    //       <strong>Current Path:</strong> {window.location.pathname}
    //     </div>
    //     <div>
    //       <strong>Token:</strong> {localStorage.getItem('accessToken') ? 'exists' : 'none'}
    //     </div>
    //   </div>
    // </div>
    <>
    
    </>
  );
};

export default AuthDebug;
