#!/bin/bash

# Digital Ocean Update Script - Enhanced with <PERSON><PERSON><PERSON>
# Run this script on your Digital Ocean droplet to update the deployment

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🔄 Finwise Backend - Digital Ocean Update"
echo "========================================"

# Backup current logs before stopping
print_status "Backing up current logs..."
mkdir -p backups
if docker ps | grep -q finwise-backend; then
    docker logs finwise-backend > "backups/backend-logs-$(date +%Y%m%d-%H%M%S).log" 2>&1
    print_success "Logs backed up"
fi

# Stop the current container gracefully
print_status "Stopping current container..."
if docker ps | grep -q finwise-backend; then
    docker stop finwise-backend
    print_success "Container stopped"
else
    print_warning "No running container found"
fi

# Remove the current container
print_status "Removing current container..."
if docker ps -a | grep -q finwise-backend; then
    docker rm finwise-backend
    print_success "Container removed"
fi

# Pull latest code if git repository exists
if [ -d ".git" ]; then
    print_status "Pulling latest code..."
    git pull origin main
    print_success "Code updated"
else
    print_warning "Not a git repository - skipping code update"
fi

# Check if environment file exists
if [ ! -f "Backend/.env" ]; then
    if [ -f "Backend/.env.production" ]; then
        print_status "Copying production environment file..."
        cp Backend/.env.production Backend/.env
    else
        print_error "No environment file found!"
        exit 1
    fi
fi

# Rebuild the image with latest code
print_status "Building Docker image..."
docker build -t finwise-backend:latest .
print_success "Image built successfully"

# Create uploads directory if it doesn't exist
mkdir -p Backend/uploads

# Run with environment file (more secure than inline variables)
print_status "Starting new container..."
docker run -d \
  --name finwise-backend \
  -p 5003:5003 \
  --env-file Backend/.env \
  --restart unless-stopped \
  -v "$(pwd)/Backend/uploads:/app/uploads" \
  --memory="1g" \
  --cpus="1.0" \
  finwise-backend:latest

# Wait for container to start
print_status "Waiting for container to start..."
sleep 5

# Check if container is running
if docker ps | grep -q finwise-backend; then
    print_success "Container is running!"

    # Test health endpoint
    print_status "Testing health endpoint..."
    sleep 3
    if curl -f http://localhost:5003/api/health > /dev/null 2>&1; then
        print_success "Health check passed!"
    else
        print_warning "Health check failed - check logs"
    fi

    # Show container status
    echo ""
    echo "📊 Container Status:"
    docker ps | grep finwise-backend

    # Show recent logs
    echo ""
    echo "📋 Recent Logs:"
    docker logs --tail 10 finwise-backend

else
    print_error "Container failed to start!"
    print_status "Checking logs..."
    docker logs finwise-backend
    exit 1
fi

print_success "Update completed successfully!"
echo ""
echo "🔧 Useful Commands:"
echo "  View logs: docker logs -f finwise-backend"
echo "  Stop container: docker stop finwise-backend"
echo "  Restart container: docker restart finwise-backend"
echo "  Monitor: ./scripts/monitor-deployment.sh"
