import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { Upload, FileSpreadsheet, Download, History, AlertCircle, CheckCircle } from 'lucide-react';
import ExcelUpload from '../components/import/ExcelUpload';
import ImportPreview from '../components/import/ImportPreview';
import ImportHistory from '../components/import/ImportHistory';
import TemplateDownload from '../components/import/TemplateDownload';

const Import = () => {
  const [activeTab, setActiveTab] = useState('upload');
  const [importData, setImportData] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const [originalFile, setOriginalFile] = useState(null);

  const tabs = [
    { id: 'upload', name: 'Upload File', icon: Upload },
    { id: 'templates', name: 'Templates', icon: Download },
    { id: 'history', name: 'Import History', icon: History },
  ];

  const handlePreviewData = (data, file) => {
    setImportData(data);
    setOriginalFile(file);
    setShowPreview(true);
  };

  const handleImportComplete = () => {
    setShowPreview(false);
    setImportData(null);
    setOriginalFile(null);
    setActiveTab('history');
  };

  const handleBackToUpload = () => {
    setShowPreview(false);
    setImportData(null);
    setOriginalFile(null);
  };

  return (
    <>
      <Helmet>
        <title>Import Transactions - Finwise</title>
        <meta name="description" content="Import bank statements from ACLEDA and ABA banks" />
      </Helmet>

      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white shadow rounded-lg p-6 dark:bg-[#1E1E2D]">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-[#1E3A8A] dark:text-white">Import Transactions</h1>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Import bank statements from ACLEDA and ABA banks in Excel format
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <FileSpreadsheet className="w-8 h-8 text-[#1E3A8A] dark:text-white" />
            </div>
          </div>
        </div>

        {/* Info Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start">
              <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-blue-900">Supported Formats</h3>
                <p className="text-sm text-blue-700 mt-1">
                  ACLEDA and ABA bank statement formats (.xlsx, .xls files)
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div className="flex items-start">
              <AlertCircle className="w-5 h-5 text-amber-600 mt-0.5 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-amber-900">File Requirements</h3>
                <p className="text-sm text-amber-700 mt-1">
                  Maximum file size: 10MB. Excel format required.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        {!showPreview && (
          <div className="bg-white dark:bg-[#1E1E2D] shadow rounded-lg">
            <div className="border-b border-gray-200 dark:border-gray-700">
              <nav className="-mb-px flex space-x-8 px-6">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 flex-1 justify-center text-center ${
                      activeTab === tab.id
                        ? 'dark:border-white dark:text-white border-[#162B5A] text-[#162B5A]'
                        : 'border-transparent dark:text-gray-400 text-gray-500 dark:hover:text-gray-500 dark:hover:border-gray-500 hover:border-[#1E3A8A] hover:text-[#1E3A8A]'
                    }`}
                  >
                    <tab.icon className="w-4 h-4 " />
                    <span>{tab.name}</span>
                  </button>
                ))}
              </nav>
            </div>

            <div className="p-6">
              {activeTab === 'upload' && (
                <ExcelUpload onPreviewData={handlePreviewData} />
              )}
              {activeTab === 'templates' && (
                <TemplateDownload />
              )}
              {activeTab === 'history' && (
                <ImportHistory />
              )}
            </div>
          </div>
        )}

        {/* Preview Modal/Section */}
        {showPreview && importData && (
          <ImportPreview
            data={importData}
            originalFile={originalFile}
            onImportComplete={handleImportComplete}
            onBack={handleBackToUpload}
          />
        )}
      </div>
    </>
  );
};

export default Import;
