'use strict';

module.exports = (sequelize, DataTypes) => {
  const Transaction = sequelize.define('Transaction', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    category_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'categories',
        key: 'id'
      }
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      validate: {
        isDate: true,
        notEmpty: true
      }
    },
    details: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 1000]
      }
    },
    money_in: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
      defaultValue: 0.00,
      validate: {
        min: 0
      }
    },
    money_out: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
      defaultValue: 0.00,
      validate: {
        min: 0
      }
    },
    currency: {
      type: DataTypes.STRING(3),
      allowNull: false,
      defaultValue: 'USD',
      validate: {
        len: [3, 3],
        isUppercase: true
      }
    },
    balance: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
      comment: 'Running balance after this transaction'
    },
    exchange_rate: {
      type: DataTypes.DECIMAL(10, 6),
      allowNull: true,
      defaultValue: 1.000000,
      comment: 'Exchange rate to base currency'
    },
    base_currency: {
      type: DataTypes.STRING(3),
      allowNull: true,
      defaultValue: 'USD',
      comment: 'Base currency for calculations'
    },
    imported_from: {
      type: DataTypes.ENUM('manual', 'excel', 'ocr', 'api'),
      defaultValue: 'manual',
      allowNull: false
    },
    receipt_image_url: {
      type: DataTypes.STRING(500),
      allowNull: true,
      comment: 'URL to receipt image if uploaded'
    },
    ocr_confidence: {
      type: DataTypes.DECIMAL(5, 4),
      allowNull: true,
      comment: 'OCR confidence score (0-1)'
    },
    tags: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Array of tags for categorization'
    },
    location: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'GPS coordinates or location name'
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Additional user notes'
    },
    is_recurring: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    recurring_pattern: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Recurring transaction pattern'
    },
    reference_number: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: 'Bank reference or transaction ID'
    }
  }, {
    tableName: 'transactions',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['category_id']
      },
      {
        fields: ['date']
      },
      {
        fields: ['user_id', 'date']
      },
      {
        fields: ['currency']
      },
      {
        fields: ['imported_from']
      },
      {
        fields: ['is_recurring']
      }
    ],
    validate: {
      eitherMoneyInOrOut() {
        if ((this.money_in || 0) === 0 && (this.money_out || 0) === 0) {
          throw new Error('Either money_in or money_out must be greater than 0');
        }
        if ((this.money_in || 0) > 0 && (this.money_out || 0) > 0) {
          throw new Error('Cannot have both money_in and money_out in the same transaction');
        }
      }
    }
  });

  Transaction.associate = function(models) {
    Transaction.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
    Transaction.belongsTo(models.Category, {
      foreignKey: 'category_id',
      as: 'category'
    });
  };

  // Instance methods
  Transaction.prototype.getAmount = function() {
    return (this.money_in || 0) - (this.money_out || 0);
  };

  Transaction.prototype.getType = function() {
    return (this.money_in || 0) > 0 ? 'income' : 'expense';
  };

  return Transaction;
};
