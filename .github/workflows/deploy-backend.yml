name: Deploy Backend to Digital Ocean

on:
  push:
    branches: [ main ]
    paths:
      - 'Backend/**'
      - 'Dockerfile'
      - 'docker-compose.yml'
      - '.github/workflows/deploy-backend.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'Backend/**'
      - 'Dockerfile'

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: testpassword
          MYSQL_DATABASE: finwise_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: Backend/package-lock.json
        
    - name: Install dependencies
      run: |
        cd Backend
        npm ci
        
    - name: Run tests
      run: |
        cd Backend
        npm test
      env:
        NODE_ENV: test
        DB_HOST: localhost
        DB_PORT: 3306
        DB_USER: root
        DB_PASSWORD: testpassword
        DB_NAME: finwise_test
        JWT_SECRET: test-jwt-secret
        SESSION_SECRET: test-session-secret
        
  build:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
        
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile
        push: true
        tags: ${{ secrets.DOCKER_USERNAME }}/finwise-backend:latest,${{ secrets.DOCKER_USERNAME }}/finwise-backend:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        
  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Deploy to Digital Ocean
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.DO_HOST }}
        username: ${{ secrets.DO_USERNAME }}
        key: ${{ secrets.DO_SSH_KEY }}
        port: ${{ secrets.DO_PORT }}
        script: |
          # Navigate to application directory
          cd /opt/finwise
          
          # Pull latest code
          git pull origin main
          
          # Stop existing container
          docker stop finwise-backend || true
          docker rm finwise-backend || true
          
          # Pull latest image
          docker pull ${{ secrets.DOCKER_USERNAME }}/finwise-backend:latest
          
          # Start new container
          docker run -d \
            --name finwise-backend \
            -p 5003:5003 \
            --env-file Backend/.env \
            -v $(pwd)/Backend/uploads:/app/uploads \
            -v $(pwd)/logs:/app/logs \
            --restart unless-stopped \
            --memory="1g" \
            --cpus="1.0" \
            ${{ secrets.DOCKER_USERNAME }}/finwise-backend:latest
          
          # Wait for container to start
          sleep 10
          
          # Health check
          if curl -f http://localhost:5003/api/health; then
            echo "✅ Deployment successful!"
          else
            echo "❌ Health check failed!"
            docker logs finwise-backend
            exit 1
          fi
          
          # Clean up old images
          docker image prune -f
          
  notify:
    needs: [test, build, deploy]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Notify deployment status
      run: |
        if [ "${{ needs.deploy.result }}" == "success" ]; then
          echo "✅ Backend deployment successful!"
        elif [ "${{ needs.deploy.result }}" == "skipped" ]; then
          echo "⏭️ Backend deployment skipped (not main branch)"
        else
          echo "❌ Backend deployment failed!"
          exit 1
        fi
