const { Transaction, Category, Budget, Goal, User } = require("../models");
const { validationResult } = require("express-validator");
const { Op, fn, col, literal } = require("sequelize");
const ExcelJS = require("exceljs");
const PDFDocument = require("pdfkit");
const fs = require('fs');
const path = require('path');

// Get financial summary report
const getFinancialSummary = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      start_date,
      end_date,
      period = "monthly",
      currency = "USD",
    } = req.query;

    const whereClause = { user_id: userId };

    if (start_date && end_date) {
      whereClause.date = {
        [Op.between]: [start_date, end_date],
      };
    }

    if (currency !== "all") {
      whereClause.currency = currency;
    }

    // Get total income and expenses
    const [incomeResult, expenseResult] = await Promise.all([
      Transaction.findOne({
        where: { ...whereClause, money_in: { [Op.gt]: 0 } },
        attributes: [[fn("SUM", col("money_in")), "total"]],
      }),
      Transaction.findOne({
        where: { ...whereClause, money_out: { [Op.gt]: 0 } },
        attributes: [[fn("SUM", col("money_out")), "total"]],
      }),
    ]);

    const totalIncome = parseFloat(incomeResult?.dataValues?.total || 0);
    const totalExpenses = parseFloat(expenseResult?.dataValues?.total || 0);
    const netIncome = totalIncome - totalExpenses;

    // Get category breakdown
    const categoryBreakdown = await Transaction.findAll({
      where: whereClause,
      include: [
        {
          model: Category,
          as: "category",
          attributes: ["id", "name", "name_km", "type", "icon", "color"],
        },
      ],
      attributes: [
        "category_id",
        [fn("SUM", col("money_in")), "total_income"],
        [fn("SUM", col("money_out")), "total_expenses"],
        [fn("COUNT", col("Transaction.id")), "transaction_count"],
      ],
      group: ["category_id", "category.id"],
      order: [[fn("SUM", col("money_out")), "DESC"]],
    });

    // Get monthly trends (last 12 months)
    const monthlyTrends = await Transaction.findAll({
      where: {
        user_id: userId,
        date: {
          [Op.gte]: new Date(new Date().setMonth(new Date().getMonth() - 12)),
        },
      },
      attributes: [
        [fn("DATE_FORMAT", col("date"), "%Y-%m"), "month"],
        [fn("SUM", col("money_in")), "income"],
        [fn("SUM", col("money_out")), "expenses"],
      ],
      group: [fn("DATE_FORMAT", col("date"), "%Y-%m")],
      order: [[fn("DATE_FORMAT", col("date"), "%Y-%m"), "ASC"]],
    });

    // Get recent transactions
    const recentTransactions = await Transaction.findAll({
      where: whereClause,
      include: [
        {
          model: Category,
          as: "category",
          attributes: ["name", "name_km", "icon", "color"],
        },
      ],
      order: [
        ["date", "DESC"],
        ["created_at", "DESC"],
      ],
      limit: 10,
    });

    res.json({
      success: true,
      data: {
        summary: {
          total_income: totalIncome,
          total_expenses: totalExpenses,
          net_income: netIncome,
          currency: currency,
        },
        category_breakdown: categoryBreakdown.map((item) => ({
          category: item.category,
          total_income: parseFloat(item.dataValues.total_income || 0),
          total_expenses: parseFloat(item.dataValues.total_expenses || 0),
          transaction_count: parseInt(item.dataValues.transaction_count || 0),
        })),
        monthly_trends: monthlyTrends.map((item) => ({
          month: item.dataValues.month,
          income: parseFloat(item.dataValues.income || 0),
          expenses: parseFloat(item.dataValues.expenses || 0),
          net:
            parseFloat(item.dataValues.income || 0) -
            parseFloat(item.dataValues.expenses || 0),
        })),
        recent_transactions: recentTransactions,
      },
    });
  } catch (error) {
    console.error("Get financial summary error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to generate financial summary",
    });
  }
};

// Get budget vs actual report
const getBudgetReport = async (req, res) => {
  try {
    const userId = req.user.id;
    const { start_date, end_date, period_type = "monthly" } = req.query;

    const whereClause = { user_id: userId, is_active: true };

    if (period_type) {
      whereClause.period_type = period_type;
    }

    const budgets = await Budget.findAll({
      where: whereClause,
      include: [
        {
          model: Category,
          as: "category",
          attributes: ["id", "name", "name_km", "type", "icon", "color"],
        },
      ],
    });

    const budgetReport = await Promise.all(
      budgets.map(async (budget) => {
        const actualSpent =
          (await Transaction.sum("money_out", {
            where: {
              user_id: userId,
              category_id: budget.category_id,
              date: {
                [Op.between]: [budget.period_start, budget.period_end],
              },
            },
          })) || 0;

        const budgetAmount = parseFloat(budget.amount);
        const actualAmount = parseFloat(actualSpent);
        const variance = budgetAmount - actualAmount;
        const variancePercentage =
          budgetAmount > 0 ? (variance / budgetAmount) * 100 : 0;

        return {
          budget: budget.toJSON(),
          budget_amount: budgetAmount,
          actual_amount: actualAmount,
          variance: variance,
          variance_percentage: variancePercentage,
          is_over_budget: actualAmount > budgetAmount,
          utilization_percentage:
            budgetAmount > 0 ? (actualAmount / budgetAmount) * 100 : 0,
        };
      })
    );

    res.json({
      success: true,
      data: budgetReport,
    });
  } catch (error) {
    console.error("Get budget report error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to generate budget report",
    });
  }
};

// Get goals progress report
const getGoalsReport = async (req, res) => {
  try {
    const userId = req.user.id;
    const { goal_type, active = "true" } = req.query;

    const whereClause = { user_id: userId };

    if (active !== "all") {
      whereClause.is_active = active === "true";
    }

    if (goal_type) {
      whereClause.goal_type = goal_type;
    }

    const goals = await Goal.findAll({
      where: whereClause,
      order: [
        ["priority", "DESC"],
        ["created_at", "DESC"],
      ],
    });

    const goalsReport = goals.map((goal) => {
      const targetAmount = parseFloat(goal.target_amount);
      const currentAmount = parseFloat(goal.current_amount);
      const progressPercentage =
        targetAmount > 0 ? (currentAmount / targetAmount) * 100 : 0;
      const remainingAmount = targetAmount - currentAmount;

      let estimatedCompletion = null;
      if (
        goal.monthly_contribution &&
        goal.monthly_contribution > 0 &&
        remainingAmount > 0
      ) {
        const monthsToComplete = Math.ceil(
          remainingAmount / parseFloat(goal.monthly_contribution)
        );
        const estimatedDate = new Date();
        estimatedDate.setMonth(estimatedDate.getMonth() + monthsToComplete);
        estimatedCompletion = estimatedDate.toISOString().split("T")[0];
      }

      return {
        goal: goal.toJSON(),
        progress_percentage: Math.min(progressPercentage, 100),
        remaining_amount: Math.max(remainingAmount, 0),
        is_achieved: currentAmount >= targetAmount,
        estimated_completion: estimatedCompletion,
        days_remaining: goal.target_date
          ? Math.ceil(
              (new Date(goal.target_date) - new Date()) / (1000 * 60 * 60 * 24)
            )
          : null,
      };
    });

    res.json({
      success: true,
      data: goalsReport,
    });
  } catch (error) {
    console.error("Get goals report error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to generate goals report",
    });
  }
};

// Export transactions to Excel
const exportTransactionsExcel = async (req, res) => {
  try {
    const userId = req.user.id;
    const { start_date, end_date, category_id, type } = req.query;

    const whereClause = { user_id: userId };

    if (start_date && end_date) {
      whereClause.date = {
        [Op.between]: [start_date, end_date],
      };
    }

    if (category_id) {
      whereClause.category_id = category_id;
    }

    if (type === "income") {
      whereClause.money_in = { [Op.gt]: 0 };
    } else if (type === "expense") {
      whereClause.money_out = { [Op.gt]: 0 };
    }

    const transactions = await Transaction.findAll({
      where: whereClause,
      include: [
        {
          model: Category,
          as: "category",
          attributes: ["name", "name_km", "type"],
        },
      ],
      order: [["date", "DESC"]],
    });

    // Create Excel workbook
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Transactions");

    // Add headers
    worksheet.columns = [
      { header: "Date", key: "date", width: 12 },
      { header: "Transaction Details", key: "details", width: 30 },
      { header: "Money In", key: "money_in", width: 12 },
      { header: "Currency", key: "currency_in", width: 10 },
      { header: "Money Out", key: "money_out", width: 12 },
      { header: "Currency", key: "currency_out", width: 10 },
      { header: "Category", key: "category", width: 20 },
      { header: "Notes", key: "notes", width: 30 },
    ];

    // Add data
    transactions.forEach((transaction) => {
      worksheet.addRow({
        date: transaction.date,
        details: transaction.details,
        money_in: transaction.money_in || "",
        currency_in: transaction.money_in > 0 ? transaction.currency : "",
        money_out: transaction.money_out || "",
        currency_out: transaction.money_out > 0 ? transaction.currency : "",
        category: transaction.category?.name || "",
        notes: transaction.notes || "",
      });
    });

    // Style the header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFE0E0E0" },
    };

    // Set response headers
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=transactions_${
        new Date().toISOString().split("T")[0]
      }.xlsx`
    );

    // Write to response
    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    console.error("Export Excel error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to export transactions to Excel",
    });
  }
};

// Export transactions to CSV
const exportTransactionsCSV = async (req, res) => {
  try {
    const userId = req.user.id;
    const { start_date, end_date, category_id, type } = req.query;

    const whereClause = { user_id: userId };

    if (start_date && end_date) {
      whereClause.date = {
        [Op.between]: [start_date, end_date],
      };
    }

    if (category_id) {
      whereClause.category_id = category_id;
    }

    if (type === "income") {
      whereClause.money_in = { [Op.gt]: 0 };
    } else if (type === "expense") {
      whereClause.money_out = { [Op.gt]: 0 };
    }

    const transactions = await Transaction.findAll({
      where: whereClause,
      include: [
        {
          model: Category,
          as: "category",
          attributes: ["name", "name_km", "type"],
        },
      ],
      order: [["date", "DESC"]],
    });

    // Create CSV content
    const headers = [
      "Date",
      "Transaction Details",
      "Money In",
      "Currency In",
      "Money Out",
      "Currency Out",
      "Category",
      "Notes",
    ];
    let csvContent = headers.join(",") + "\n";

    transactions.forEach((transaction) => {
      const row = [
        transaction.date,
        `"${transaction.details || ""}"`,
        transaction.money_in || "",
        transaction.money_in > 0 ? transaction.currency : "",
        transaction.money_out || "",
        transaction.money_out > 0 ? transaction.currency : "",
        `"${transaction.category?.name || ""}"`,
        `"${transaction.notes || ""}"`,
      ];
      csvContent += row.join(",") + "\n";
    });

    // Set response headers
    res.setHeader("Content-Type", "text/csv");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=transactions_${
        new Date().toISOString().split("T")[0]
      }.csv`
    );

    // Send CSV content
    res.send(csvContent);
  } catch (error) {
    console.error("Export CSV error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to export transactions to CSV",
    });
  }
};

/**
 * Enhanced export to Excel with better formatting and multiple sheets
 */
const exportToExcelEnhanced = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      startDate,
      endDate,
      period = 'month',
      category,
      type
    } = req.body;

    let start, end;

    if (startDate && endDate) {
      start = new Date(startDate);
      end = new Date(endDate);
    } else {
      end = new Date();
      start = new Date();

      switch (period) {
        case 'week':
          start.setDate(end.getDate() - 7);
          break;
        case 'month':
          start.setMonth(end.getMonth() - 1);
          break;
        case 'year':
          start.setFullYear(end.getFullYear() - 1);
          break;
        default:
          start.setMonth(end.getMonth() - 1);
      }
    }

    const whereClause = {
      user_id: userId,
      date: {
        [Op.between]: [start, end]
      }
    };

    if (category) whereClause.category_id = category;
    if (type) {
      if (type === 'income') {
        whereClause.money_in = { [Op.gt]: 0 };
      } else if (type === 'expense') {
        whereClause.money_out = { [Op.gt]: 0 };
      }
    }

    // Get user info
    const user = await User.findByPk(userId);

    // Get transactions
    const transactions = await Transaction.findAll({
      where: whereClause,
      include: [{ model: Category, as: 'category' }],
      order: [['date', 'DESC']]
    });

    // Calculate summary
    const totalIncome = transactions
      .filter(t => t.money_in > 0)
      .reduce((sum, t) => sum + parseFloat(t.money_in), 0);

    const totalExpenses = transactions
      .filter(t => t.money_out > 0)
      .reduce((sum, t) => sum + parseFloat(t.money_out), 0);

    const netIncome = totalIncome - totalExpenses;

    // Create Excel workbook
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'Finwise';
    workbook.created = new Date();

    // Summary sheet
    const summarySheet = workbook.addWorksheet('Summary', {
      headerFooter: { firstHeader: 'Finwise Financial Report' }
    });

    // Add title
    summarySheet.mergeCells('A1:D1');
    summarySheet.getCell('A1').value = 'Financial Report Summary';
    summarySheet.getCell('A1').font = { size: 16, bold: true };
    summarySheet.getCell('A1').alignment = { horizontal: 'center' };

    // Add period info
    summarySheet.mergeCells('A2:D2');
    summarySheet.getCell('A2').value = `Period: ${start.toLocaleDateString()} - ${end.toLocaleDateString()}`;
    summarySheet.getCell('A2').font = { size: 12 };
    summarySheet.getCell('A2').alignment = { horizontal: 'center' };

    // Add user info
    summarySheet.mergeCells('A3:D3');
    summarySheet.getCell('A3').value = `Generated for: ${user.name} (${user.email})`;
    summarySheet.getCell('A3').font = { size: 10, italic: true };
    summarySheet.getCell('A3').alignment = { horizontal: 'center' };

    // Add summary data
    summarySheet.addRow([]);
    summarySheet.addRow(['Metric', 'Amount', 'Currency', 'Percentage']);
    summarySheet.getRow(5).font = { bold: true };

    summarySheet.addRow(['Total Income', totalIncome, 'USD', '']);
    summarySheet.addRow(['Total Expenses', totalExpenses, 'USD', '']);
    summarySheet.addRow(['Net Income', netIncome, 'USD', '']);
    summarySheet.addRow(['Transaction Count', transactions.length, '', '']);

    // Format currency columns
    summarySheet.getColumn('B').numFmt = '$#,##0.00';

    // Transactions sheet
    const transactionsSheet = workbook.addWorksheet('Transactions');
    transactionsSheet.columns = [
      { header: 'Date', key: 'date', width: 15 },
      { header: 'Category', key: 'category', width: 20 },
      { header: 'Details', key: 'details', width: 30 },
      { header: 'Income', key: 'income', width: 15 },
      { header: 'Expense', key: 'expense', width: 15 },
      { header: 'Currency', key: 'currency', width: 10 },
      { header: 'Notes', key: 'notes', width: 30 }
    ];

    // Add transaction data
    transactions.forEach(transaction => {
      transactionsSheet.addRow({
        date: transaction.date,
        category: transaction.category?.name || 'Uncategorized',
        details: transaction.details,
        income: transaction.money_in > 0 ? transaction.money_in : '',
        expense: transaction.money_out > 0 ? transaction.money_out : '',
        currency: transaction.currency,
        notes: transaction.notes
      });
    });

    // Format currency columns
    transactionsSheet.getColumn('D').numFmt = '$#,##0.00';
    transactionsSheet.getColumn('E').numFmt = '$#,##0.00';

    // Set response headers
    const filename = `finwise-report-${start.toISOString().split('T')[0]}-to-${end.toISOString().split('T')[0]}.xlsx`;
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // Write to response
    await workbook.xlsx.write(res);
    res.end();

  } catch (error) {
    console.error('Enhanced Excel export error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to export Excel report'
    });
  }
};

/**
 * Enhanced export to PDF with better formatting
 */
const exportToPDFEnhanced = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      startDate,
      endDate,
      period = 'month',
      category,
      type
    } = req.body;

    let start, end;

    if (startDate && endDate) {
      start = new Date(startDate);
      end = new Date(endDate);
    } else {
      end = new Date();
      start = new Date();

      switch (period) {
        case 'week':
          start.setDate(end.getDate() - 7);
          break;
        case 'month':
          start.setMonth(end.getMonth() - 1);
          break;
        case 'year':
          start.setFullYear(end.getFullYear() - 1);
          break;
        default:
          start.setMonth(end.getMonth() - 1);
      }
    }

    const whereClause = {
      user_id: userId,
      date: {
        [Op.between]: [start, end]
      }
    };

    if (category) whereClause.category_id = category;
    if (type) {
      if (type === 'income') {
        whereClause.money_in = { [Op.gt]: 0 };
      } else if (type === 'expense') {
        whereClause.money_out = { [Op.gt]: 0 };
      }
    }

    // Get user info
    const user = await User.findByPk(userId);

    // Get transactions
    const transactions = await Transaction.findAll({
      where: whereClause,
      include: [{ model: Category, as: 'category' }],
      order: [['date', 'DESC']]
    });

    // Calculate summary
    const totalIncome = transactions
      .filter(t => t.money_in > 0)
      .reduce((sum, t) => sum + parseFloat(t.money_in), 0);

    const totalExpenses = transactions
      .filter(t => t.money_out > 0)
      .reduce((sum, t) => sum + parseFloat(t.money_out), 0);

    const netIncome = totalIncome - totalExpenses;

    // Create PDF
    const doc = new PDFDocument({ margin: 50 });

    // Set response headers
    const filename = `finwise-report-${start.toISOString().split('T')[0]}-to-${end.toISOString().split('T')[0]}.pdf`;
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // Pipe PDF to response
    doc.pipe(res);

    // Add header
    doc.fontSize(20).text('Finwise Financial Report', { align: 'center' });
    doc.moveDown();

    // Add period and user info
    doc.fontSize(12)
       .text(`Period: ${start.toLocaleDateString()} - ${end.toLocaleDateString()}`, { align: 'center' })
       .text(`Generated for: ${user.name} (${user.email})`, { align: 'center' })
       .text(`Generated on: ${new Date().toLocaleDateString()}`, { align: 'center' });

    doc.moveDown(2);

    // Add summary section
    doc.fontSize(16).text('Summary', { underline: true });
    doc.moveDown();

    doc.fontSize(12)
       .text(`Total Income: $${totalIncome.toFixed(2)}`)
       .text(`Total Expenses: $${totalExpenses.toFixed(2)}`)
       .text(`Net Income: $${netIncome.toFixed(2)}`)
       .text(`Total Transactions: ${transactions.length}`);

    doc.moveDown(2);

    // Add transactions section
    doc.fontSize(16).text('Transactions', { underline: true });
    doc.moveDown();

    // Table headers
    const tableTop = doc.y;
    const dateX = 50;
    const categoryX = 120;
    const detailsX = 200;
    const amountX = 350;
    const typeX = 420;

    doc.fontSize(10)
       .text('Date', dateX, tableTop)
       .text('Category', categoryX, tableTop)
       .text('Details', detailsX, tableTop)
       .text('Amount', amountX, tableTop)
       .text('Type', typeX, tableTop);

    // Draw line under headers
    doc.moveTo(dateX, tableTop + 15)
       .lineTo(500, tableTop + 15)
       .stroke();

    let currentY = tableTop + 25;

    // Add transaction rows
    transactions.slice(0, 30).forEach((transaction, index) => { // Limit to 30 transactions for PDF
      if (currentY > 700) { // Start new page if needed
        doc.addPage();
        currentY = 50;
      }

      const amount = transaction.money_in > 0 ? transaction.money_in : transaction.money_out;
      const type = transaction.money_in > 0 ? 'Income' : 'Expense';

      doc.fontSize(9)
         .text(transaction.date.toLocaleDateString(), dateX, currentY)
         .text(transaction.category?.name || 'Uncategorized', categoryX, currentY)
         .text(transaction.details.substring(0, 20) + '...', detailsX, currentY)
         .text(`$${amount.toFixed(2)}`, amountX, currentY)
         .text(type, typeX, currentY);

      currentY += 20;
    });

    if (transactions.length > 30) {
      doc.moveDown()
         .text(`... and ${transactions.length - 30} more transactions`, { align: 'center', fontSize: 10 });
    }

    // Finalize PDF
    doc.end();

  } catch (error) {
    console.error('Enhanced PDF export error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to export PDF report'
    });
  }
};

module.exports = {
  getFinancialSummary,
  getBudgetReport,
  getGoalsReport,
  exportTransactionsExcel,
  exportTransactionsCSV,
  exportToExcelEnhanced,
  exportToPDFEnhanced,
};
