#!/usr/bin/env node

/**
 * Test script for Finwise Telegram Bot
 */

const axios = require('axios');
const { logger } = require('../utils/logger');

const BOT_BASE_URL = 'http://localhost:3002';
const BACKEND_BASE_URL = 'http://localhost:5003';

class BotTester {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      tests: []
    };
  }

  async runTest(name, testFn) {
    try {
      console.log(`🧪 Testing: ${name}`);
      await testFn();
      this.results.passed++;
      this.results.tests.push({ name, status: 'PASSED' });
      console.log(`✅ ${name} - PASSED\n`);
    } catch (error) {
      this.results.failed++;
      this.results.tests.push({ name, status: 'FAILED', error: error.message });
      console.log(`❌ ${name} - FAILED: ${error.message}\n`);
    }
  }

  async testBotHealth() {
    const response = await axios.get(`${BOT_BASE_URL}/health`);
    if (response.status !== 200) {
      throw new Error(`Expected status 200, got ${response.status}`);
    }
    if (response.data.status !== 'healthy') {
      throw new Error(`Expected status 'healthy', got '${response.data.status}'`);
    }
  }

  async testBotStatus() {
    const response = await axios.get(`${BOT_BASE_URL}/api/bot/status`);
    if (response.status !== 200) {
      throw new Error(`Expected status 200, got ${response.status}`);
    }
    if (!response.data.botActive) {
      throw new Error('Bot should be active');
    }
  }

  async testBackendConnection() {
    try {
      const response = await axios.get(`${BACKEND_BASE_URL}/api/users/me`);
      // We expect this to fail with 401 (no token), which means backend is responding
      if (response.status === 401) {
        return; // This is expected
      }
    } catch (error) {
      if (error.response && error.response.status === 401) {
        return; // This is expected - backend is responding
      }
      throw new Error(`Backend connection failed: ${error.message}`);
    }
  }

  async testBotMetrics() {
    const response = await axios.get(`${BOT_BASE_URL}/metrics`);
    if (response.status !== 200) {
      throw new Error(`Expected status 200, got ${response.status}`);
    }
    // Should contain some basic metrics
    if (!response.data.uptime) {
      throw new Error('Metrics should include uptime');
    }
  }

  async testWebhookEndpoint() {
    // Test that webhook endpoint exists (even if we can't test full functionality)
    try {
      const response = await axios.post(`${BOT_BASE_URL}/webhook/telegram`, {
        update_id: 123,
        message: {
          message_id: 1,
          from: { id: 123, first_name: 'Test' },
          chat: { id: 123, type: 'private' },
          date: Math.floor(Date.now() / 1000),
          text: '/test'
        }
      });
      // We expect this to work (200) or fail gracefully
      if (response.status !== 200 && response.status !== 500) {
        throw new Error(`Unexpected status: ${response.status}`);
      }
    } catch (error) {
      if (error.response && (error.response.status === 200 || error.response.status === 500)) {
        return; // These are acceptable responses
      }
      throw error;
    }
  }

  async testConfigValidation() {
    // Test that required environment variables are set
    const config = require('../config/config');
    
    if (!config.telegram.botToken) {
      throw new Error('TELEGRAM_BOT_TOKEN is required');
    }
    
    if (!config.api.baseUrl) {
      throw new Error('Backend API URL is required');
    }
    
    if (!config.server.port) {
      throw new Error('Server port is required');
    }
  }

  async testLoggerFunctionality() {
    // Test that logger is working
    const testMessage = 'Test log message';
    logger.info(testMessage);
    
    // Check if log file exists and contains our message
    const fs = require('fs');
    const path = require('path');
    
    const logFile = path.join(__dirname, '../logs/telegram-bot.log');
    if (fs.existsSync(logFile)) {
      const logContent = fs.readFileSync(logFile, 'utf8');
      if (!logContent.includes(testMessage)) {
        throw new Error('Log message not found in log file');
      }
    }
  }

  async runAllTests() {
    console.log('🚀 Starting Finwise Telegram Bot Tests\n');
    console.log('=' * 50);

    await this.runTest('Bot Health Check', () => this.testBotHealth());
    await this.runTest('Bot Status Check', () => this.testBotStatus());
    await this.runTest('Backend Connection', () => this.testBackendConnection());
    await this.runTest('Bot Metrics', () => this.testBotMetrics());
    await this.runTest('Webhook Endpoint', () => this.testWebhookEndpoint());
    await this.runTest('Configuration Validation', () => this.testConfigValidation());
    await this.runTest('Logger Functionality', () => this.testLoggerFunctionality());

    console.log('=' * 50);
    console.log('📊 Test Results:');
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`📈 Success Rate: ${((this.results.passed / (this.results.passed + this.results.failed)) * 100).toFixed(1)}%`);

    if (this.results.failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results.tests
        .filter(test => test.status === 'FAILED')
        .forEach(test => {
          console.log(`  • ${test.name}: ${test.error}`);
        });
    }

    console.log('\n🎉 Testing completed!');
    
    // Exit with error code if any tests failed
    process.exit(this.results.failed > 0 ? 1 : 0);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const tester = new BotTester();
  tester.runAllTests().catch(error => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = BotTester;
