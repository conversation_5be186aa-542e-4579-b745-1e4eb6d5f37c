import React, { useState, useEffect } from "react";
import { Helmet } from "react-helmet-async";
import {
  Plus,
  Target,
  Edit,
  Trash2,
  Calendar,
  DollarSign,
  TrendingUp,
  CheckCircle,
  AlertCircle,
  Clock,
} from "lucide-react";
import useGoalStore from "../store/goalStore";
import GoalForm from "../components/goals/GoalForm";

const Goals = () => {
  const [showForm, setShowForm] = useState(false);
  const [editingGoal, setEditingGoal] = useState(null);

  const {
    goals,
    isLoading,
    error,
    fetchGoals,
    deleteGoal,
    getGoalProgress,
    getGoalStatus,
    clearError,
  } = useGoalStore();

  useEffect(() => {
    fetchGoals();
  }, [fetchGoals]);

  const handleCreateGoal = () => {
    setEditingGoal(null);
    setShowForm(true);
  };

  const handleEditGoal = (goal) => {
    setEditingGoal(goal);
    setShowForm(true);
  };

  const handleDeleteGoal = async (goalId) => {
    if (window.confirm("Are you sure you want to delete this goal?")) {
      await deleteGoal(goalId);
    }
  };

  const handleFormClose = () => {
    setShowForm(false);
    setEditingGoal(null);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "completed":
        return "text-green-600 bg-green-100";
      case "on-track":
        return "text-blue-600 bg-blue-100";
      case "behind":
        return "text-yellow-600 bg-yellow-100";
      case "overdue":
        return "text-red-600 bg-red-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-4 h-4" />;
      case "overdue":
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  if (error) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Error Loading Goals
        </h3>
        <p className="text-gray-500 mb-4">{error}</p>
        <button
          onClick={() => {
            clearError();
            fetchGoals();
          }}
          className="btn btn-primary"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>Goals - Finwise</title>
        <meta name="description" content="Set and track your financial goals" />
      </Helmet>

      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between pb-4 border-b border-[#c6d2dd]">
          <div>
            <h1 className="text-3xl font-bold text-[#636160]">Goals</h1>
            <p className="text-gray-500">
              Set financial goals and track your progress.
            </p>
          </div>
          <button onClick={handleCreateGoal} className="flex items-center px-4 py-2 rounded-lg bg-[#aabbcd] text-white hover:bg-[#8da5bd] transition shadow-sm">
            <Plus className="w-4 h-4 mr-2" />
            Create Goal
          </button>
        </div>

        {/* Goals List */}
        {isLoading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-500 mt-4">Loading goals...</p>
          </div>
        ) : goals.length === 0 ? (
          <div className="card">
            <div className="card-content">
              <div className="text-center py-12">
                <div className="mx-auto h-12 w-12 flex items-center bg-[#8da5bd] justify-center rounded-full  mb-4">
                  <Target className="h-6 w-6 text-primary-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No Goals Yet
                </h3>
                <p className="text-gray-500 mb-4">
                  Set savings goals and track your progress towards achieving
                  them.
                </p>
                <button 
                onClick={handleCreateGoal} 
                  className="flex items-center px-4 py-2 rounded-lg bg-[#aabbcd] text-white hover:bg-[#8da5bd] transition shadow-sm mx-auto"
                ></button>
                <button onClick={handleCreateGoal} className="px-4 py-2 flex items-center rounded-lg bg-[#8da5bd] text-white hover:bg-[#636160] transition shadow mx-auto">
                  <Plus className="w-4 h-4 mr-2" />
                  Set Your First Goal
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {goals.map((goal) => {
              const progress = getGoalProgress(goal);
              const status = getGoalStatus(goal);

              return (
                <div key={goal.id} className="card">
                  <div className="card-content">
                    {/* Goal Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">
                          {goal.name}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {goal.description}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          onClick={() => handleEditGoal(goal)}
                          className="p-1 text-gray-400 hover:text-gray-600"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteGoal(goal.id)}
                          className="p-1 text-gray-400 hover:text-red-600"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="mb-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700">
                          Progress
                        </span>
                        <span className="text-sm text-gray-600">
                          {progress.toFixed(1)}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${Math.min(progress, 100)}%` }}
                        ></div>
                      </div>
                    </div>

                    {/* Goal Details */}
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center text-sm text-gray-600">
                          <DollarSign className="w-4 h-4 mr-1" />
                          <span>Current</span>
                        </div>
                        <span className="text-sm font-medium">
                          {formatCurrency(goal.current_amount)}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center text-sm text-gray-600">
                          <Target className="w-4 h-4 mr-1" />
                          <span>Target</span>
                        </div>
                        <span className="text-sm font-medium">
                          {formatCurrency(goal.target_amount)}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center text-sm text-gray-600">
                          <Calendar className="w-4 h-4 mr-1" />
                          <span>Target Date</span>
                        </div>
                        <span className="text-sm font-medium">
                          {formatDate(goal.target_date)}
                        </span>
                      </div>

                      {/* Status Badge */}
                      <div className="flex items-center justify-between pt-2 border-t border-gray-200">
                        <span className="text-sm text-gray-600">Status</span>
                        <div
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                            status
                          )}`}
                        >
                          {getStatusIcon(status)}
                          <span className="ml-1 capitalize">
                            {status.replace("-", " ")}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Goal Form Modal */}
        {showForm && (
          <GoalForm
            goal={editingGoal}
            onClose={handleFormClose}
            onSuccess={() => {
              handleFormClose();
              fetchGoals();
            }}
          />
        )}
      </div>
    </>
  );
};

export default Goals;
