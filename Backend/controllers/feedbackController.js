const { Feedback, User } = require('../models');

/**
 * Check if user can provide feedback this month
 */
const canProvideFeedback = async (req, res) => {
  try {
    const userId = req.user.id;
    
    const hasProvidedFeedback = await Feedback.hasUserProvidedFeedbackThisMonth(userId);
    
    res.json({
      success: true,
      data: {
        canProvideFeedback: !hasProvidedFeedback,
        hasProvidedThisMonth: hasProvidedFeedback
      }
    });

  } catch (error) {
    console.error('Check feedback eligibility error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check feedback eligibility'
    });
  }
};

/**
 * Submit monthly feedback
 */
const submitFeedback = async (req, res) => {
  try {
    const userId = req.user.id;
    const { content, rating, category = 'general', isAnonymous = false } = req.body;

    // Validate input
    if (!content || content.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Feedback content is required'
      });
    }

    if (content.length > 2000) {
      return res.status(400).json({
        success: false,
        error: 'Feedback content cannot exceed 2000 characters'
      });
    }

    if (rating && (rating < 1 || rating > 5)) {
      return res.status(400).json({
        success: false,
        error: 'Rating must be between 1 and 5'
      });
    }

    // Check if user already provided feedback this month
    const hasProvidedFeedback = await Feedback.hasUserProvidedFeedbackThisMonth(userId);
    
    if (hasProvidedFeedback) {
      return res.status(400).json({
        success: false,
        error: 'You have already provided feedback for this month'
      });
    }

    // Create feedback
    const feedback = await Feedback.createMonthlyFeedback(
      userId,
      content.trim(),
      rating,
      category
    );

    // Update anonymous flag if specified
    if (isAnonymous) {
      await feedback.update({ is_anonymous: true });
    }

    res.json({
      success: true,
      message: 'Feedback submitted successfully',
      data: {
        feedbackId: feedback.id,
        month: feedback.feedback_month,
        year: feedback.feedback_year,
        category: feedback.category,
        rating: feedback.rating
      }
    });

  } catch (error) {
    console.error('Submit feedback error:', error);
    
    if (error.message === 'User has already provided feedback for this month') {
      return res.status(400).json({
        success: false,
        error: error.message
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to submit feedback'
    });
  }
};

/**
 * Get user's feedback history
 */
const getFeedbackHistory = async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 20 } = req.query;

    const offset = (page - 1) * limit;

    const feedback = await Feedback.findAndCountAll({
      where: { user_id: userId },
      order: [['feedback_year', 'DESC'], ['feedback_month', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        feedback: feedback.rows,
        pagination: {
          total: feedback.count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(feedback.count / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get feedback history error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get feedback history'
    });
  }
};

/**
 * Skip feedback for this month
 */
const skipFeedback = async (req, res) => {
  try {
    const userId = req.user.id;

    // Check if user already provided feedback this month
    const hasProvidedFeedback = await Feedback.hasUserProvidedFeedbackThisMonth(userId);
    
    if (hasProvidedFeedback) {
      return res.status(400).json({
        success: false,
        error: 'You have already provided feedback for this month'
      });
    }

    // Create a "skipped" feedback entry to prevent showing popup again this month
    const now = new Date();
    const feedback = await Feedback.create({
      user_id: userId,
      feedback_content: '[SKIPPED]',
      feedback_month: now.getMonth() + 1,
      feedback_year: now.getFullYear(),
      category: 'other',
      status: 'closed'
    });

    res.json({
      success: true,
      message: 'Feedback skipped for this month',
      data: {
        feedbackId: feedback.id,
        month: feedback.feedback_month,
        year: feedback.feedback_year
      }
    });

  } catch (error) {
    console.error('Skip feedback error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to skip feedback'
    });
  }
};

/**
 * Get feedback statistics (admin only)
 */
const getFeedbackStats = async (req, res) => {
  try {
    const { month, year } = req.query;

    const stats = await Feedback.getFeedbackStats(
      month ? parseInt(month) : null,
      year ? parseInt(year) : null
    );

    // Get total feedback count
    const totalCount = await Feedback.count({
      where: {
        ...(month && { feedback_month: parseInt(month) }),
        ...(year && { feedback_year: parseInt(year) })
      }
    });

    // Get average rating
    const avgRating = await Feedback.findOne({
      attributes: [
        [Feedback.sequelize.fn('AVG', Feedback.sequelize.col('rating')), 'avgRating']
      ],
      where: {
        rating: { [Feedback.sequelize.Op.not]: null },
        ...(month && { feedback_month: parseInt(month) }),
        ...(year && { feedback_year: parseInt(year) })
      }
    });

    res.json({
      success: true,
      data: {
        totalFeedback: totalCount,
        averageRating: avgRating?.dataValues?.avgRating || null,
        categoryStats: stats,
        period: {
          month: month ? parseInt(month) : null,
          year: year ? parseInt(year) : null
        }
      }
    });

  } catch (error) {
    console.error('Get feedback stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get feedback statistics'
    });
  }
};

module.exports = {
  canProvideFeedback,
  submitFeedback,
  getFeedbackHistory,
  skipFeedback,
  getFeedbackStats
};
