const { body, query, param } = require('express-validator');

// Create transaction validation
const validateCreateTransaction = [
  body('date')
    .isISO8601()
    .withMessage('Date must be a valid ISO 8601 date')
    .custom((value) => {
      const date = new Date(value);
      const now = new Date();
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(now.getFullYear() - 1);
      const oneYearFromNow = new Date();
      oneYearFromNow.setFullYear(now.getFullYear() + 1);
      
      if (date < oneYearAgo || date > oneYearFromNow) {
        throw new Error('Date must be within one year from today');
      }
      return true;
    }),

  body('details')
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Transaction details must be between 1 and 1000 characters')
    .escape(),

  body('money_in')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Money in must be a positive number')
    .custom((value, { req }) => {
      const moneyOut = req.body.money_out;
      if ((!value || value <= 0) && (!moneyOut || moneyOut <= 0)) {
        throw new Error('Either money_in or money_out must be greater than 0');
      }
      if (value > 0 && moneyOut > 0) {
        throw new Error('Cannot have both money_in and money_out in the same transaction');
      }
      return true;
    }),

  body('money_out')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Money out must be a positive number'),

  body('currency')
    .optional()
    .isLength({ min: 3, max: 3 })
    .isUppercase()
    .withMessage('Currency must be a 3-letter uppercase code (e.g., USD, KHR)'),

  body('category_id')
    .optional()
    .isUUID()
    .withMessage('Category ID must be a valid UUID'),

  body('notes')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Notes must not exceed 2000 characters')
    .escape(),

  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array')
    .custom((tags) => {
      if (tags.length > 10) {
        throw new Error('Maximum 10 tags allowed');
      }
      for (const tag of tags) {
        if (typeof tag !== 'string' || tag.length > 50) {
          throw new Error('Each tag must be a string with maximum 50 characters');
        }
      }
      return true;
    }),

  body('location')
    .optional()
    .isObject()
    .withMessage('Location must be an object')
    .custom((location) => {
      if (location.latitude && (typeof location.latitude !== 'number' || location.latitude < -90 || location.latitude > 90)) {
        throw new Error('Latitude must be a number between -90 and 90');
      }
      if (location.longitude && (typeof location.longitude !== 'number' || location.longitude < -180 || location.longitude > 180)) {
        throw new Error('Longitude must be a number between -180 and 180');
      }
      if (location.name && (typeof location.name !== 'string' || location.name.length > 200)) {
        throw new Error('Location name must be a string with maximum 200 characters');
      }
      return true;
    }),

  body('receipt_image_url')
    .optional()
    .isURL()
    .withMessage('Receipt image URL must be a valid URL'),

  body('reference_number')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Reference number must not exceed 100 characters')
    .escape()
];

// Update transaction validation
const validateUpdateTransaction = [
  param('id')
    .isUUID()
    .withMessage('Transaction ID must be a valid UUID'),

  body('date')
    .optional()
    .isISO8601()
    .withMessage('Date must be a valid ISO 8601 date')
    .custom((value) => {
      const date = new Date(value);
      const now = new Date();
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(now.getFullYear() - 1);
      const oneYearFromNow = new Date();
      oneYearFromNow.setFullYear(now.getFullYear() + 1);
      
      if (date < oneYearAgo || date > oneYearFromNow) {
        throw new Error('Date must be within one year from today');
      }
      return true;
    }),

  body('details')
    .optional()
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Transaction details must be between 1 and 1000 characters')
    .escape(),

  body('money_in')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Money in must be a positive number'),

  body('money_out')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Money out must be a positive number'),

  body('currency')
    .optional()
    .isLength({ min: 3, max: 3 })
    .isUppercase()
    .withMessage('Currency must be a 3-letter uppercase code (e.g., USD, KHR)'),

  body('category_id')
    .optional()
    .isUUID()
    .withMessage('Category ID must be a valid UUID'),

  body('notes')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Notes must not exceed 2000 characters')
    .escape(),

  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array')
    .custom((tags) => {
      if (tags.length > 10) {
        throw new Error('Maximum 10 tags allowed');
      }
      for (const tag of tags) {
        if (typeof tag !== 'string' || tag.length > 50) {
          throw new Error('Each tag must be a string with maximum 50 characters');
        }
      }
      return true;
    }),

  body('location')
    .optional()
    .isObject()
    .withMessage('Location must be an object'),

  body('receipt_image_url')
    .optional()
    .isURL()
    .withMessage('Receipt image URL must be a valid URL'),

  body('reference_number')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Reference number must not exceed 100 characters')
    .escape()
];

// Get transactions validation (query parameters)
const validateGetTransactions = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),

  query('category')
    .optional()
    .isUUID()
    .withMessage('Category must be a valid UUID'),

  query('type')
    .optional()
    .isIn(['income', 'expense'])
    .withMessage('Type must be either "income" or "expense"'),

  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),

  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid ISO 8601 date')
    .custom((value, { req }) => {
      if (req.query.startDate && new Date(value) < new Date(req.query.startDate)) {
        throw new Error('End date must be after start date');
      }
      return true;
    }),

  query('search')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters')
    .escape(),

  query('sortBy')
    .optional()
    .isIn(['date', 'created_at', 'money_in', 'money_out', 'details'])
    .withMessage('Sort by must be one of: date, created_at, money_in, money_out, details'),

  query('sortOrder')
    .optional()
    .isIn(['ASC', 'DESC', 'asc', 'desc'])
    .withMessage('Sort order must be either ASC or DESC')
];

// Delete transaction validation
const validateDeleteTransaction = [
  param('id')
    .isUUID()
    .withMessage('Transaction ID must be a valid UUID')
];

// Bulk delete validation
const validateBulkDelete = [
  body('ids')
    .isArray({ min: 1, max: 100 })
    .withMessage('IDs must be an array with 1-100 items')
    .custom((ids) => {
      for (const id of ids) {
        if (typeof id !== 'string' || !/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)) {
          throw new Error('All IDs must be valid UUIDs');
        }
      }
      return true;
    })
];

module.exports = {
  validateCreateTransaction,
  validateUpdateTransaction,
  validateGetTransactions,
  validateDeleteTransaction,
  validateBulkDelete
};
