'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('notifications', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      type: {
        type: Sequelize.ENUM(
          'payment_success',
          'payment_failed', 
          'subscription_expiring',
          'subscription_expired',
          'usage_limit_reached',
          'usage_limit_warning',
          'system_maintenance',
          'security_alert',
          'feature_update',
          'welcome',
          'goal_achieved',
          'budget_exceeded',
          'monthly_report'
        ),
        allowNull: false,
        comment: 'Type of notification'
      },
      title: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: 'Notification title'
      },
      message: {
        type: Sequelize.TEXT,
        allowNull: false,
        comment: 'Notification message content'
      },
      data: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Additional data related to the notification'
      },
      priority: {
        type: Sequelize.ENUM('low', 'medium', 'high', 'urgent'),
        defaultValue: 'medium',
        allowNull: false,
        comment: 'Notification priority level'
      },
      is_read: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false,
        comment: 'Whether the notification has been read'
      },
      read_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'When the notification was read'
      },
      expires_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'When the notification expires (optional)'
      },
      action_url: {
        type: Sequelize.STRING(500),
        allowNull: true,
        comment: 'URL to navigate to when notification is clicked'
      },
      action_text: {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: 'Text for the action button'
      },
      sent_via: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Channels through which notification was sent (email, push, etc.)'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // Add indexes
    await queryInterface.addIndex('notifications', ['type'], { name: 'notifications_type_idx' });
    await queryInterface.addIndex('notifications', ['is_read'], { name: 'notifications_is_read_idx' });
    await queryInterface.addIndex('notifications', ['priority'], { name: 'notifications_priority_idx' });
    await queryInterface.addIndex('notifications', ['created_at'], { name: 'notifications_created_at_idx' });
    await queryInterface.addIndex('notifications', ['expires_at'], { name: 'notifications_expires_at_idx' });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('notifications');
  }
};
