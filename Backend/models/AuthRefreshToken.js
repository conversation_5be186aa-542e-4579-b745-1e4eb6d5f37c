'use strict';

module.exports = (sequelize, DataTypes) => {
  const AuthRefreshToken = sequelize.define('AuthRefreshToken', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    token_hash: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true
    },
    expires_at: {
      type: DataTypes.DATE,
      allowNull: false,
      validate: {
        isDate: true,
        isAfter: new Date().toISOString()
      }
    },
    is_revoked: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    device_info: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Device information for security tracking'
    },
    ip_address: {
      type: DataTypes.STRING(45),
      allowNull: true,
      comment: 'IP address when token was created'
    },
    user_agent: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'User agent when token was created'
    }
  }, {
    tableName: 'auth_refresh_tokens',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['token_hash']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['expires_at']
      },
      {
        fields: ['is_revoked']
      }
    ]
  });

  AuthRefreshToken.associate = function(models) {
    AuthRefreshToken.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
  };

  // Instance methods
  AuthRefreshToken.prototype.isExpired = function() {
    return new Date() > this.expires_at;
  };

  AuthRefreshToken.prototype.isValid = function() {
    return !this.is_revoked && !this.isExpired();
  };

  return AuthRefreshToken;
};
