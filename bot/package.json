{"name": "finwise-telegram-bot", "version": "1.0.0", "description": "Standalone Telegram Bot Service for Finwise Financial Management", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "build": "echo 'No build step required for Node.js'", "deploy": "node scripts/deploy.js", "health-check": "node scripts/health-check.js"}, "keywords": ["telegram", "bot", "finwise", "financial", "management", "nodejs", "api"], "author": "Finwise Team", "license": "MIT", "dependencies": {"axios": "^1.6.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^8.1.0", "helmet": "^7.1.0", "joi": "^17.11.0", "moment": "^2.29.4", "mysql2": "^3.6.5", "node-cron": "^3.0.3", "rate-limiter-flexible": "^2.4.2", "redis": "^4.6.10", "sequelize": "^6.35.1", "telegraf": "^4.15.6", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/jest": "^29.5.8", "eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/finwise/telegram-bot.git"}, "bugs": {"url": "https://github.com/finwise/telegram-bot/issues"}, "homepage": "https://github.com/finwise/telegram-bot#readme"}