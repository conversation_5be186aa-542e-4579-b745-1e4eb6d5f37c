import React from 'react';
import { Globe } from 'lucide-react';
import useLanguageStore from '../../store/languageStore';

const LanguageToggle = ({ className = '' }) => {
  const { language, setLanguage, getLanguageLabel } = useLanguageStore();

  const languages = [
    { value: 'en', label: 'English', flag: '🇺🇸' },
    { value: 'km', label: 'ខ្មែរ', flag: '🇰🇭' }
  ];

  return (
    <div className={`relative ${className}`}>
      <select
        value={language}
        onChange={(e) => setLanguage(e.target.value)}
        className="
          appearance-none bg-transparent border-none text-sm font-medium
          text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100
          focus:outline-none focus:ring-0 cursor-pointer pr-6
        "
      >
        {languages.map(({ value, label, flag }) => (
          <option key={value} value={value} className="bg-white dark:bg-gray-800">
            {flag} {label}
          </option>
        ))}
      </select>
      <Globe className="absolute right-0 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
    </div>
  );
};

export default LanguageToggle;
