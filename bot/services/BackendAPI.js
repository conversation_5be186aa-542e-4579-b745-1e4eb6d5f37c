/**
 * Backend API Service for Telegram Bot
 */

const axios = require('axios');
const { logger } = require('../utils/logger');
const config = require('../config/config');

class BackendAPI {
  constructor() {
    this.baseURL = config.api.baseUrl;
    this.timeout = config.api.timeout;
    this.maxRetries = config.api.maxRetries;
    
    // Create axios instance
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Finwise-Telegram-Bot/1.0.0'
      }
    });

    // Setup interceptors
    this.setupInterceptors();
  }

  setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        logger.debug('API Request', {
          method: config.method,
          url: config.url,
          data: config.data
        });
        return config;
      },
      (error) => {
        logger.error('API Request Error', { error: error.message });
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        logger.debug('API Response', {
          status: response.status,
          url: response.config.url
        });
        return response;
      },
      (error) => {
        logger.error('API Response Error', {
          status: error.response?.status,
          url: error.config?.url,
          message: error.message
        });
        return Promise.reject(error);
      }
    );
  }

  // User Management
  async getUserByTelegramId(telegramId) {
    try {
      const response = await this.client.get(`/users/telegram/${telegramId}`);
      return response.data;
    } catch (error) {
      if (error.response?.status === 404) {
        return null; // User not found
      }
      throw error;
    }
  }

  async linkUserAccount(telegramId, linkCode) {
    try {
      const response = await this.client.post('/users/link-telegram', {
        telegramId: telegramId.toString(),
        linkCode
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async updateUserTelegramInfo(userId, telegramData) {
    try {
      const response = await this.client.patch(`/users/${userId}/telegram`, telegramData);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Transaction Management
  async getUserTransactions(userId, options = {}) {
    try {
      const params = new URLSearchParams();
      if (options.limit) params.append('limit', options.limit);
      if (options.offset) params.append('offset', options.offset);
      if (options.startDate) params.append('startDate', options.startDate);
      if (options.endDate) params.append('endDate', options.endDate);
      if (options.category) params.append('category', options.category);

      const response = await this.client.get(`/transactions?userId=${userId}&${params}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async createTransaction(userId, transactionData) {
    try {
      const response = await this.client.post('/transactions', {
        ...transactionData,
        user_id: userId
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async deleteTransaction(transactionId, userId) {
    try {
      const response = await this.client.delete(`/transactions/${transactionId}?userId=${userId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Dashboard & Analytics
  async getDashboardStats(userId) {
    try {
      const response = await this.client.get(`/dashboard/stats?userId=${userId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async getMonthlyReport(userId, month, year) {
    try {
      const response = await this.client.get(`/dashboard/monthly-report?userId=${userId}&month=${month}&year=${year}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Budget Management
  async getUserBudgets(userId) {
    try {
      const response = await this.client.get(`/budgets?userId=${userId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async createBudget(userId, budgetData) {
    try {
      const response = await this.client.post('/budgets', {
        ...budgetData,
        user_id: userId
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Goal Management
  async getUserGoals(userId) {
    try {
      const response = await this.client.get(`/goals?userId=${userId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async createGoal(userId, goalData) {
    try {
      const response = await this.client.post('/goals', {
        ...goalData,
        user_id: userId
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Categories
  async getCategories(userId) {
    try {
      const response = await this.client.get(`/categories?userId=${userId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Notifications
  async sendNotification(userId, notificationData) {
    try {
      const response = await this.client.post('/notifications', {
        ...notificationData,
        user_id: userId
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Health check
  async healthCheck() {
    try {
      const response = await this.client.get('/health');
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Retry mechanism for failed requests
  async retryRequest(requestFn, maxRetries = this.maxRetries) {
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await requestFn();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxRetries) {
          break;
        }
        
        // Wait before retry (exponential backoff)
        const delay = Math.pow(2, attempt) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        
        logger.warn(`API request failed, retrying (${attempt}/${maxRetries})`, {
          error: error.message
        });
      }
    }
    
    throw lastError;
  }
}

module.exports = new BackendAPI();
