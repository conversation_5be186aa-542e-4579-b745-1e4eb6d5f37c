name: Deploy to Digital Ocean

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "18"
          cache: "npm"
          cache-dependency-path: Backend/package-lock.json

      - name: Install backend dependencies
        run: |
          cd Backend
          npm ci

      - name: Run backend tests
        run: |
          cd Backend
          npm test
        env:
          NODE_ENV: test

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
      - uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to Docker Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ secrets.DOCKER_REGISTRY }}
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: ${{ secrets.DOCKER_REGISTRY }}/finwise-backend:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
      - name: Deploy to Digital Ocean
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.DO_HOST }}
          username: ${{ secrets.DO_USERNAME }}
          key: ${{ secrets.DO_SSH_KEY }}
          script: |
            # Pull latest image
            docker pull ${{ secrets.DOCKER_REGISTRY }}/finwise-backend:latest

            # Stop and remove old container
            docker stop finwise-backend || true
            docker rm finwise-backend || true

            # Run new container
            docker run -d \
              --name finwise-backend \
              -p 5003:5003 \
              --env-file /home/<USER>/.env \
              --restart unless-stopped \
              -v /home/<USER>/uploads:/app/uploads \
              ${{ secrets.DOCKER_REGISTRY }}/finwise-backend:latest

            # Clean up old images
            docker image prune -f
