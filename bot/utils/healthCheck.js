/**
 * Health check utility for Finwise Telegram Bot
 */

const { logger } = require('./logger');

class HealthCheck {
  constructor() {
    this.startTime = Date.now();
    this.checks = new Map();
  }

  // Register a health check
  register(name, checkFunction, interval = 30000) {
    this.checks.set(name, {
      check: checkFunction,
      interval,
      lastCheck: null,
      status: 'unknown',
      error: null
    });

    // Run initial check
    this.runCheck(name);

    // Schedule periodic checks
    setInterval(() => {
      this.runCheck(name);
    }, interval);

    logger.info(`Health check registered: ${name}`);
  }

  // Run a specific health check
  async runCheck(name) {
    const checkInfo = this.checks.get(name);
    if (!checkInfo) {
      return false;
    }

    try {
      checkInfo.lastCheck = Date.now();
      const result = await checkInfo.check();
      checkInfo.status = result ? 'healthy' : 'unhealthy';
      checkInfo.error = null;
      return result;
    } catch (error) {
      checkInfo.status = 'error';
      checkInfo.error = error.message;
      logger.error(`Health check failed: ${name}`, { error: error.message });
      return false;
    }
  }

  // Get health status for all checks
  async getStatus() {
    const status = {
      uptime: Date.now() - this.startTime,
      timestamp: new Date().toISOString(),
      status: 'healthy',
      checks: {}
    };

    let overallHealthy = true;

    for (const [name, checkInfo] of this.checks) {
      const isHealthy = await this.runCheck(name);
      
      status.checks[name] = {
        status: checkInfo.status,
        lastCheck: checkInfo.lastCheck,
        error: checkInfo.error
      };

      if (!isHealthy) {
        overallHealthy = false;
      }
    }

    status.status = overallHealthy ? 'healthy' : 'unhealthy';
    return status;
  }

  // Express middleware for health endpoint
  middleware() {
    return async (req, res) => {
      try {
        const status = await this.getStatus();
        const httpStatus = status.status === 'healthy' ? 200 : 503;
        
        res.status(httpStatus).json(status);
      } catch (error) {
        logger.error('Health check endpoint error', { error: error.message });
        res.status(500).json({
          status: 'error',
          error: 'Health check failed',
          timestamp: new Date().toISOString()
        });
      }
    };
  }

  // Default health checks
  setupDefaultChecks(bot, apiClient) {
    // Bot connectivity check
    if (bot) {
      this.register('telegram_bot', async () => {
        try {
          const me = await bot.getMe();
          return !!me.id;
        } catch (error) {
          return false;
        }
      }, 60000); // Check every minute
    }

    // API connectivity check
    if (apiClient) {
      this.register('backend_api', async () => {
        try {
          const response = await apiClient.get('/health');
          return response.status === 200;
        } catch (error) {
          return false;
        }
      }, 30000); // Check every 30 seconds
    }

    // Memory usage check
    this.register('memory_usage', async () => {
      const usage = process.memoryUsage();
      const usedMB = usage.heapUsed / 1024 / 1024;
      return usedMB < 500; // Alert if using more than 500MB
    }, 60000);

    // CPU usage check (basic)
    this.register('process_health', async () => {
      return process.uptime() > 0;
    }, 30000);
  }
}

// Create singleton instance
const healthCheck = new HealthCheck();

// Add convenience methods for the server
healthCheck.start = (bot) => {
  healthCheck.setupDefaultChecks(bot);
  return healthCheck;
};

healthCheck.stop = () => {
  // Stop all health checks if needed
  return healthCheck;
};

module.exports = healthCheck;
