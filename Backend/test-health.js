/**
 * Simple server connectivity test
 */

const axios = require("axios");

async function testServerConnectivity() {
  try {
    console.log("🏥 Testing server connectivity...");

    // Test OCR endpoint (should return method not allowed or similar)
    const response = await axios.get("http://127.0.0.1:5003/api/ocr", {
      timeout: 5000,
    });

    console.log("✅ Server responded!");
    console.log("Status:", response.status);
    console.log("Data:", response.data);
  } catch (error) {
    console.log("Server response received (even if error):");

    if (error.response) {
      console.log("✅ Server is running!");
      console.log("Status:", error.response.status);
      console.log("Status text:", error.response.statusText);
      if (error.response.data) {
        console.log("Data:", error.response.data);
      }
    } else {
      console.log("❌ Server connection failed:", error.message);
      console.log("Error code:", error.code);
    }
  }
}

testServerConnectivity();
