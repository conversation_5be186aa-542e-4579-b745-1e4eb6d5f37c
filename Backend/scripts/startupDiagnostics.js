require('dotenv').config();
const databaseService = require('../services/databaseService');
const facebookAppService = require('../services/facebookAppService');

class StartupDiagnostics {
  constructor() {
    this.results = {
      database: null,
      facebook: null,
      environment: null,
      overall: 'pending'
    };
  }

  async runAllDiagnostics() {
    console.log('🔍 Running Finwise Application Diagnostics...\n');

    try {
      // Test environment variables
      await this.testEnvironmentVariables();
      
      // Test database connection
      await this.testDatabaseConnection();
      
      // Test Facebook app configuration
      await this.testFacebookConfiguration();
      
      // Generate summary report
      this.generateSummaryReport();
      
      return this.results;
    } catch (error) {
      console.error('❌ Diagnostics failed:', error.message);
      this.results.overall = 'failed';
      return this.results;
    }
  }

  async testEnvironmentVariables() {
    console.log('📋 Testing Environment Variables...');
    
    const requiredVars = [
      'DB_HOST', 'DB_PORT', 'DB_USER', 'DB_PASSWORD', 'DB_NAME', 'DB_DIALECT',
      'JWT_SECRET', 'SESSION_SECRET', 'PORT', 'NODE_ENV',
      'FACEBOOK_APP_ID', 'FACEBOOK_APP_SECRET'
    ];

    const missing = [];
    const present = [];

    requiredVars.forEach(varName => {
      if (process.env[varName]) {
        present.push(varName);
      } else {
        missing.push(varName);
      }
    });

    this.results.environment = {
      status: missing.length === 0 ? 'success' : 'warning',
      present: present.length,
      missing: missing.length,
      missingVars: missing
    };

    if (missing.length === 0) {
      console.log('✅ All required environment variables are present');
    } else {
      console.log(`⚠️ Missing environment variables: ${missing.join(', ')}`);
    }
    console.log('');
  }

  async testDatabaseConnection() {
    console.log('🗄️ Testing Database Connection...');
    
    try {
      const dbConnection = await databaseService.connect();
      
      // Test basic query
      await dbConnection.authenticate();
      
      this.results.database = {
        status: 'success',
        message: 'Database connection successful',
        type: dbConnection.getDialect()
      };
      
      console.log('✅ Database connection successful');
      console.log(`   Database type: ${dbConnection.getDialect()}`);
      
    } catch (error) {
      this.results.database = {
        status: 'error',
        message: error.message,
        suggestions: [
          'Check if database server is running',
          'Verify database credentials in .env file',
          'Check network connectivity to database host',
          'Ensure database exists and user has proper permissions'
        ]
      };
      
      console.log('❌ Database connection failed:', error.message);
    }
    console.log('');
  }

  async testFacebookConfiguration() {
    console.log('📘 Testing Facebook App Configuration...');
    
    try {
      const facebookStatus = await facebookAppService.checkAppStatus();
      
      this.results.facebook = facebookStatus;
      
      if (facebookStatus.isActive) {
        console.log('✅ Facebook app configuration is valid');
        console.log(`   App Name: ${facebookStatus.appName}`);
        console.log(`   App ID: ${facebookStatus.appId}`);
        console.log(`   Status: ${facebookStatus.status}`);
      } else {
        console.log('❌ Facebook app configuration issues detected');
        console.log(`   Error: ${facebookStatus.error}`);
        
        if (facebookStatus.suggestions) {
          console.log('   Suggestions:');
          facebookStatus.suggestions.forEach((suggestion, index) => {
            console.log(`   ${index + 1}. ${suggestion.issue}: ${suggestion.solution}`);
          });
        }
      }
      
    } catch (error) {
      this.results.facebook = {
        status: 'error',
        message: error.message,
        isActive: false
      };
      
      console.log('❌ Facebook configuration test failed:', error.message);
    }
    console.log('');
  }

  generateSummaryReport() {
    console.log('📊 DIAGNOSTIC SUMMARY REPORT');
    console.log('================================');
    
    // Environment Variables
    const envStatus = this.results.environment?.status === 'success' ? '✅' : '⚠️';
    console.log(`${envStatus} Environment Variables: ${this.results.environment?.status || 'unknown'}`);
    
    // Database
    const dbStatus = this.results.database?.status === 'success' ? '✅' : '❌';
    console.log(`${dbStatus} Database Connection: ${this.results.database?.status || 'unknown'}`);
    
    // Facebook
    const fbStatus = this.results.facebook?.isActive ? '✅' : '❌';
    console.log(`${fbStatus} Facebook App: ${this.results.facebook?.isActive ? 'Active' : 'Inactive'}`);
    
    // Overall Status
    const allGood = this.results.environment?.status === 'success' && 
                   this.results.database?.status === 'success' && 
                   this.results.facebook?.isActive;
    
    this.results.overall = allGood ? 'success' : 'issues_detected';
    
    console.log('================================');
    if (allGood) {
      console.log('🎉 All systems are ready! You can start the application.');
    } else {
      console.log('⚠️ Some issues detected. Please review the details above.');
    }
    console.log('');
  }

  // Method to fix common issues automatically
  async attemptAutoFix() {
    console.log('🔧 Attempting automatic fixes...\n');
    
    // Auto-fix database connection by trying fallback
    if (this.results.database?.status === 'error') {
      console.log('🔄 Attempting database connection with fallback options...');
      try {
        const dbConnection = await databaseService.connect();
        console.log('✅ Database connection established with fallback');
        this.results.database.status = 'success';
      } catch (error) {
        console.log('❌ Auto-fix failed for database connection');
      }
    }
    
    // Provide Facebook app fix instructions
    if (!this.results.facebook?.isActive) {
      console.log('📘 Facebook App Fix Instructions:');
      console.log('1. Go to https://developers.facebook.com/apps/');
      console.log(`2. Select your app (ID: ${process.env.FACEBOOK_APP_ID})`);
      console.log('3. Go to Settings > Basic');
      console.log('4. Make sure App Status is "Live" (not "In Development")');
      console.log('5. Add "localhost" to App Domains');
      console.log('6. Add "http://localhost:5173" to Valid OAuth Redirect URIs');
      console.log('7. Save changes and wait a few minutes for propagation\n');
    }
  }
}

// Run diagnostics if called directly
if (require.main === module) {
  const diagnostics = new StartupDiagnostics();
  diagnostics.runAllDiagnostics()
    .then(results => {
      if (results.overall !== 'success') {
        diagnostics.attemptAutoFix();
      }
      process.exit(results.overall === 'success' ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Diagnostics failed:', error);
      process.exit(1);
    });
}

module.exports = StartupDiagnostics;
