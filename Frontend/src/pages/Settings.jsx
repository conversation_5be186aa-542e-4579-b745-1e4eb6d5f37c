import React from 'react';
import { Helmet } from 'react-helmet-async';
import { Settings as SettingsIcon, Shield, Bell, Palette } from 'lucide-react';

const Settings = () => {
  return (
    <>
      <Helmet>
        <title>Settings - Finwise</title>
        <meta name="description" content="Configure your application settings" />
      </Helmet>

      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600">Manage your application preferences and security settings.</p>
        </div>

        {/* Settings Cards */}
        <div className="space-y-6">
          {/* Security Settings */}
          <div className="card">
            <div className="card-header">
              <div className="flex items-center">
                <Shield className="h-5 w-5 text-gray-600 mr-2" />
                <h3 className="card-title">Security</h3>
              </div>
              <p className="card-description">Manage your account security settings.</p>
            </div>
            <div className="card-content">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Two-Factor Authentication</h4>
                    <p className="text-sm text-gray-500">Add an extra layer of security to your account</p>
                  </div>
                  <button className="btn btn-outline btn-sm">Enable</button>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Change Password</h4>
                    <p className="text-sm text-gray-500">Update your account password</p>
                  </div>
                  <button className="btn btn-outline btn-sm">Change</button>
                </div>
              </div>
            </div>
          </div>

          {/* Notification Settings */}
          <div className="card">
            <div className="card-header">
              <div className="flex items-center">
                <Bell className="h-5 w-5 text-gray-600 mr-2" />
                <h3 className="card-title">Notifications</h3>
              </div>
              <p className="card-description">Configure how you receive notifications.</p>
            </div>
            <div className="card-content">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Email Notifications</h4>
                    <p className="text-sm text-gray-500">Receive updates via email</p>
                  </div>
                  <input type="checkbox" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Budget Alerts</h4>
                    <p className="text-sm text-gray-500">Get notified when approaching budget limits</p>
                  </div>
                  <input type="checkbox" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Goal Reminders</h4>
                    <p className="text-sm text-gray-500">Reminders about your financial goals</p>
                  </div>
                  <input type="checkbox" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" />
                </div>
              </div>
            </div>
          </div>

          {/* Appearance Settings */}
          <div className="card">
            <div className="card-header">
              <div className="flex items-center">
                <Palette className="h-5 w-5 text-gray-600 mr-2" />
                <h3 className="card-title">Appearance</h3>
              </div>
              <p className="card-description">Customize the look and feel of the application.</p>
            </div>
            <div className="card-content">
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Theme</h4>
                  <div className="flex space-x-4">
                    <label className="flex items-center">
                      <input type="radio" name="theme" value="light" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300" defaultChecked />
                      <span className="ml-2 text-sm text-gray-700">Light</span>
                    </label>
                    <label className="flex items-center">
                      <input type="radio" name="theme" value="dark" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300" />
                      <span className="ml-2 text-sm text-gray-700">Dark</span>
                    </label>
                    <label className="flex items-center">
                      <input type="radio" name="theme" value="system" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300" />
                      <span className="ml-2 text-sm text-gray-700">System</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Settings;
