# 🤖 Finwise Telegram Bot Service

A standalone, production-ready Telegram bot service for the Finwise financial management platform. This service provides complete frontend functionality through Telegram, allowing users to manage their finances via chat interface.

## 🚀 Features

### 🔗 Account Management
- **Easy Account Linking**: Multiple methods (email, username, User ID)
- **Secure Authentication**: JWT-based authentication with backend API
- **Profile Management**: View and manage user profiles

### 💰 Financial Management
- **Transaction Management**: Add, view, search, and categorize transactions
- **Smart Input**: Natural language transaction input (`expense 25.50 Coffee food`)
- **Advanced Search**: Complex filtering (`category:food amount:>20 date:2024-01`)
- **Balance Tracking**: Real-time balance and financial statistics

### 📊 Analytics & Insights
- **AI-Powered Insights**: Personalized financial recommendations
- **Detailed Statistics**: Spending patterns, savings rate, category analysis
- **Goal Tracking**: Progress monitoring and achievement tracking
- **Budget Management**: Budget status and spending alerts

### 💎 Premium Features
- **Subscription Management**: Upgrade, billing, and plan management
- **Advanced Analytics**: Enhanced insights and reporting
- **Priority Support**: Dedicated support for premium users

### 📁 Import/Export
- **Multiple Formats**: Excel, PDF, CSV export capabilities
- **Bank Integration**: ABA and ACLEDA bank statement imports
- **Custom Reports**: Tailored financial reports and analysis

## 🏗️ Architecture

### Service Structure
```
bot/
├── server.js              # Main application entry point
├── config/                # Configuration management
├── services/              # Core business logic
├── controllers/           # Command handlers and API controllers
├── utils/                 # Utility functions and helpers
├── tests/                 # Comprehensive test suite
├── scripts/               # Deployment and utility scripts
└── logs/                  # Application logs
```

### Key Components
- **TelegramBotService**: Core bot functionality and command routing
- **BackendAPI**: Communication layer with main Finwise backend
- **Controllers**: Modular command handlers for different features
- **Health Monitoring**: Comprehensive health checks and monitoring
- **Rate Limiting**: Protection against abuse and spam

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- Docker (for containerized deployment)
- Telegram Bot Token (from @BotFather)
- Access to Finwise backend API

### Local Development

1. **Clone and Install**
   ```bash
   cd bot/
   npm install
   ```

2. **Environment Setup**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start Development Server**
   ```bash
   npm run dev
   ```

4. **Run Tests**
   ```bash
   npm test
   npm run test:coverage
   ```

### Environment Variables

```bash
# Required
TELEGRAM_BOT_TOKEN=your_bot_token_here
BACKEND_API_URL=http://localhost:5003/api

# Production
NODE_ENV=production
TELEGRAM_WEBHOOK_URL=https://yourdomain.com/webhook/telegram
BACKEND_API_KEY=your_backend_api_key

# Optional
PORT=3001
LOG_LEVEL=info
REDIS_URL=redis://localhost:6379
```

## 🌐 Production Deployment

### Digital Ocean Deployment

1. **Prepare Environment**
   ```bash
   export DO_DROPLET_IP=your_droplet_ip
   export DO_DOMAIN=your_domain.com  # Optional
   export DO_SSH_USER=root
   ```

2. **Deploy**
   ```bash
   npm run deploy
   ```

3. **Configure Environment on Server**
   ```bash
   # SSH to your droplet
   ssh root@your_droplet_ip
   
   # Create environment file
   nano /opt/finwise-bot/.env
   ```

4. **Set Telegram Webhook**
   ```bash
   curl -X POST "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/setWebhook" \
        -H "Content-Type: application/json" \
        -d '{"url": "https://yourdomain.com/webhook/telegram"}'
   ```

### Docker Deployment

```bash
# Build image
docker build -t finwise-telegram-bot .

# Run container
docker run -d \
  --name finwise-bot \
  --restart unless-stopped \
  -p 3001:3001 \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/.env:/app/.env \
  finwise-telegram-bot
```

### Manual Server Setup

```bash
# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
npm install -g pm2

# Clone and setup
git clone <repository_url> /opt/finwise-bot
cd /opt/finwise-bot
npm install --production

# Start with PM2
pm2 start server.js --name finwise-bot
pm2 startup
pm2 save
```

## 🔧 Configuration

### Bot Commands
The bot supports 20+ interactive commands:

**Account Management**
- `/start` - Welcome and account status
- `/link` - Link Finwise account
- `/profile` - View profile information

**Financial Overview**
- `/balance` - Account balance and summary
- `/stats` - Detailed financial statistics
- `/goals` - Financial goals with progress
- `/budgets` - Budget status and analysis

**Transactions**
- `/recent` - View recent transactions
- `/add` - Add new transaction
- `/search` - Search transactions
- `/export` - Export transaction data

**Premium & Support**
- `/premium` - Check premium status
- `/upgrade` - Upgrade to premium
- `/billing` - Billing information
- `/help` - Show all commands
- `/support` - Contact support

### Smart Features
- **Email Auto-Link**: Send email → automatic account linking
- **Transaction Input**: `expense 25.50 Coffee food` → transaction creation
- **Search Queries**: `category:food amount:>20` → filtered results
- **User ID Linking**: Send UUID → direct account linking

## 🧪 Testing

### Test Suite
```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Run specific test file
npm test -- bot.test.js

# Watch mode for development
npm run test:watch
```

### Test Categories
- **Unit Tests**: Individual component testing
- **Integration Tests**: API communication testing
- **End-to-End Tests**: Complete user workflow testing
- **Performance Tests**: Load and response time testing

## 📊 Monitoring & Health Checks

### Health Endpoints
- `GET /health` - Basic health status
- `GET /api/bot/status` - Detailed bot status
- `GET /api/bot/metrics` - Performance metrics

### Monitoring Setup
The deployment includes automatic monitoring:
- **Health Checks**: Every 5 minutes via cron
- **Auto-Restart**: Failed services automatically restart
- **Log Rotation**: Automatic log management
- **Alerting**: Configurable alerts for failures

### Log Management
```bash
# View logs
docker logs finwise-bot

# Follow logs
docker logs -f finwise-bot

# View specific log files
tail -f /opt/finwise-bot/logs/bot.log
tail -f /opt/finwise-bot/logs/error.log
```

## 🔒 Security

### Security Features
- **Rate Limiting**: Protection against spam and abuse
- **Input Validation**: All user inputs are validated and sanitized
- **Secure Communication**: HTTPS/TLS for all external communications
- **Authentication**: JWT-based authentication with backend
- **Error Handling**: Secure error messages without sensitive data exposure

### Best Practices
- Run as non-root user in containers
- Use environment variables for sensitive configuration
- Regular security updates and dependency management
- Comprehensive logging for security monitoring

## 🤝 API Integration

### Backend Communication
The bot communicates with the main Finwise backend via REST API:

```javascript
// Example API calls
await backendAPI.getUser(telegramId);
await backendAPI.createTransaction(userId, transactionData);
await backendAPI.getTransactionStats(userId);
```

### Error Handling
- **Retry Logic**: Automatic retries for network failures
- **Graceful Degradation**: Service continues even if backend is unavailable
- **User-Friendly Messages**: Clear error communication to users

## 📈 Performance

### Optimization Features
- **Connection Pooling**: Efficient database connections
- **Rate Limiting**: Prevents system overload
- **Caching**: Redis-based caching for improved performance
- **Async Processing**: Non-blocking operations

### Scalability
- **Horizontal Scaling**: Multiple bot instances can run simultaneously
- **Load Balancing**: Nginx reverse proxy for load distribution
- **Resource Monitoring**: Memory and CPU usage tracking

## 🆘 Troubleshooting

### Common Issues

**Bot Not Responding**
```bash
# Check service status
docker ps | grep finwise-bot

# Check logs
docker logs finwise-bot

# Restart service
docker restart finwise-bot
```

**Webhook Issues**
```bash
# Check webhook status
curl "https://api.telegram.org/bot<TOKEN>/getWebhookInfo"

# Reset webhook
curl -X POST "https://api.telegram.org/bot<TOKEN>/setWebhook" \
     -d "url=https://yourdomain.com/webhook/telegram"
```

**Backend Connection Issues**
```bash
# Test backend connectivity
curl http://localhost:5003/api/health

# Check bot health
curl http://localhost:3001/health
```

## 📞 Support

### Getting Help
- **Documentation**: This README and inline code comments
- **Issues**: GitHub Issues for bug reports and feature requests
- **Support**: Email <EMAIL> for assistance

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**🎉 The Finwise Telegram Bot provides complete financial management functionality through a simple chat interface!**
