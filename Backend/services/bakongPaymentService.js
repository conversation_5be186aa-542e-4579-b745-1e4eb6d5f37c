const BakongKHQR = require('./BakongKHQR');
const PaymentService = require('./PaymentService');
const AutoPaymentMonitor = require('./AutoPaymentMonitor');
const { Payment, Subscription, User } = require('../models');
const { v4: uuidv4 } = require('uuid');
const path = require('path');
const notificationService = require('./notificationService');

class BakongPaymentService {
  constructor() {
    this.khqr = new BakongKHQR(process.env.BAKONG_DEVELOPER_TOKEN);
    this.paymentService = new PaymentService();
    this.autoMonitor = new AutoPaymentMonitor(this.paymentService);
    
    // Connect auto monitor to payment service
    this.paymentService.setAutoMonitor(this.autoMonitor);
    
    // Start auto monitoring
    this.autoMonitor.start();
    
    // Set up event listeners
    this.setupEventListeners();
    
    console.log('🏦 Bakong Payment Service initialized');
  }

  setupEventListeners() {
    // Listen for successful payments
    this.autoMonitor.on('payment_success', async (data) => {
      try {
        await this.handlePaymentSuccess(data);
      } catch (error) {
        console.error('Error handling payment success:', error);
      }
    });

    // Listen for payment expiration
    this.autoMonitor.on('payment_expired', async (data) => {
      try {
        await this.handlePaymentExpiration(data);
      } catch (error) {
        console.error('Error handling payment expiration:', error);
      }
    });
  }

  /**
   * Create a premium subscription payment
   */
  async createPremiumPayment(userId, sessionId = null) {
    try {
      console.log(`💳 Creating premium payment for user: ${userId}`);

      // Get user details
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Create subscription record
      const subscription = await Subscription.create({
        user_id: userId,
        plan_type: 'Premium',
        status: 'pending',
        price_paid: 1.99,
        currency: 'USD',
        payment_method: 'Bakong'
      });

      // Generate unique bill number (max 25 characters for Bakong)
      const timestamp = Date.now().toString().slice(-8); // Last 8 digits
      const userIdShort = userId.substring(0, 8);
      const billNumber = `P${timestamp}${userIdShort}`.substring(0, 25);

      // Create payment record
      const payment = await Payment.create({
        user_id: userId,
        subscription_id: subscription.id,
        amount: 1.99,
        currency: 'USD',
        status: 'pending',
        payment_method: 'Bakong',
        bill_number: billNumber
      });

      // Generate QR code payment
      const paymentData = {
        amount: 1.99,
        currency: 'USD',
        billNumber: billNumber,
        storeLabel: 'Finwise Premium',
        terminalLabel: 'Subscription',
        merchantName: process.env.DEFAULT_MERCHANT_NAME || 'Choeng Rayu',
        merchantCity: process.env.DEFAULT_MERCHANT_CITY || 'Phnom Penh',
        sessionId: sessionId || userId
      };

      const qrResult = await this.paymentService.createPayment(paymentData);

      // Update payment with QR details
      await payment.update({
        qr_code: qrResult.qrCode,
        qr_md5_hash: qrResult.md5Hash,
        payment_reference: qrResult.transactionId,
        metadata: {
          qrResult,
          sessionId
        }
      });

      console.log(`✅ Premium payment created: ${payment.id}`);
      console.log(`🔑 QR MD5 Hash: ${qrResult.md5Hash}`);
      console.log(`📋 Bill Number: ${billNumber}`);
      console.log(`🖼️  Raw imagePath from qrResult:`, qrResult.imagePath);
      console.log(`🖼️  Type of imagePath:`, typeof qrResult.imagePath);

      // Convert local file path to API path for frontend
      let apiImagePath = null;
      if (qrResult.imagePath && typeof qrResult.imagePath === 'string') {
        // Extract filename from full path and create API path
        const filename = path.basename(qrResult.imagePath);
        apiImagePath = `/qr/${filename}`;
      }
      
      console.log(`🔄 Converted apiImagePath:`, apiImagePath);

      return {
        payment,
        subscription,
        qrResult,
        paymentData: {
          paymentId: payment.id,
          subscriptionId: subscription.id,
          amount: 1.99,
          currency: 'USD',
          billNumber,
          qrCode: qrResult.qrCode,
          md5Hash: qrResult.md5Hash,
          deepLink: qrResult.deepLink,
          imagePath: apiImagePath
        }
      };

    } catch (error) {
      console.error('Error creating premium payment:', error);
      throw error;
    }
  }

  /**
   * Check payment status
   */
  async checkPaymentStatus(paymentId) {
    try {
      const payment = await Payment.findByPk(paymentId, {
        include: [
          { model: User, as: 'user' },
          { model: Subscription, as: 'subscription' }
        ]
      });

      if (!payment) {
        throw new Error('Payment not found');
      }

      if (!payment.qr_md5_hash) {
        throw new Error('Payment QR hash not found');
      }

      // Check with Bakong API
      const status = await this.khqr.checkPayment(payment.qr_md5_hash);
      
      if (status === 'PAID' && payment.status === 'pending') {
        // Payment was successful, process it
        await this.processSuccessfulPayment(payment);
      }

      return {
        paymentId: payment.id,
        status: payment.status,
        bakongStatus: status,
        amount: payment.amount,
        currency: payment.currency,
        billNumber: payment.bill_number,
        user: payment.user,
        subscription: payment.subscription
      };

    } catch (error) {
      console.error('Error checking payment status:', error);
      throw error;
    }
  }

  /**
   * Handle successful payment from auto monitor
   */
  async handlePaymentSuccess(data) {
    try {
      console.log('🎉 Processing successful payment from auto monitor:', data.md5Hash);

      // Find payment by MD5 hash
      const payment = await Payment.findByQRHash(data.md5Hash);
      
      if (!payment) {
        console.warn(`Payment not found for MD5 hash: ${data.md5Hash}`);
        return;
      }

      if (payment.status === 'completed') {
        console.log('Payment already processed');
        return;
      }

      await this.processSuccessfulPayment(payment);

    } catch (error) {
      console.error('Error handling payment success:', error);
    }
  }

  /**
   * Process successful payment
   */
  async processSuccessfulPayment(payment) {
    try {
      console.log(`💰 Processing successful payment: ${payment.id}`);

      // Mark payment as completed
      await payment.markAsPaid();

      // Get subscription and user
      const subscription = await Subscription.findByPk(payment.subscription_id);
      const user = await User.findByPk(payment.user_id);

      if (subscription && user) {
        // Activate subscription
        const now = new Date();
        const expiryDate = new Date();
        expiryDate.setMonth(expiryDate.getMonth() + 1); // 1 month from now

        await subscription.update({
          status: 'active',
          started_at: now,
          expires_at: expiryDate
        });

        // Upgrade user to Premium
        await user.upgradeToPremium(expiryDate);

        console.log(`🎊 User ${user.id} upgraded to Premium until ${expiryDate.toISOString()}`);

        // Send payment success notification
        await notificationService.createPaymentSuccessNotification(user.id, {
          amount: payment.amount,
          currency: payment.currency,
          planType: 'Premium',
          paymentId: payment.id,
          subscriptionId: subscription.id
        });
      }

      return {
        success: true,
        payment,
        subscription,
        user
      };

    } catch (error) {
      console.error('Error processing successful payment:', error);
      throw error;
    }
  }

  /**
   * Handle payment expiration
   */
  async handlePaymentExpiration(data) {
    try {
      console.log('⏰ Handling payment expiration:', data.md5Hash);

      const payment = await Payment.findByQRHash(data.md5Hash);
      
      if (payment && payment.status === 'pending') {
        await payment.update({
          status: 'cancelled',
          failure_reason: 'Payment expired - no payment received within monitoring period'
        });

        // Also cancel the associated subscription
        if (payment.subscription_id) {
          const subscription = await Subscription.findByPk(payment.subscription_id);
          if (subscription && subscription.status === 'pending') {
            await subscription.update({
              status: 'cancelled',
              cancelled_at: new Date(),
              cancellation_reason: 'Payment expired'
            });
          }
        }

        console.log(`❌ Payment ${payment.id} marked as expired`);
      }

    } catch (error) {
      console.error('Error handling payment expiration:', error);
    }
  }

  /**
   * Get payment by MD5 hash
   */
  async getPaymentByHash(md5Hash) {
    try {
      return await Payment.findByQRHash(md5Hash);
    } catch (error) {
      console.error('Error getting payment by hash:', error);
      throw error;
    }
  }

  /**
   * Get user's payment history
   */
  async getUserPayments(userId) {
    try {
      return await Payment.findAll({
        where: { user_id: userId },
        include: [{ model: Subscription, as: 'subscription' }],
        order: [['created_at', 'DESC']]
      });
    } catch (error) {
      console.error('Error getting user payments:', error);
      throw error;
    }
  }

  /**
   * Get monitoring status
   */
  getMonitoringStatus() {
    return this.autoMonitor.getMonitoringStatus();
  }

  /**
   * Force check a payment
   */
  async forceCheckPayment(md5Hash) {
    return await this.autoMonitor.forceCheck(md5Hash);
  }
}

module.exports = BakongPaymentService;
