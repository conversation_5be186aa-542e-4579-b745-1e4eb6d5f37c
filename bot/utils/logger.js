/**
 * Logger utility for Finwise Telegram Bot
 */

const winston = require('winston');
const path = require('path');
const fs = require('fs');

// Ensure logs directory exists
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
}

// Custom format for logs
const logFormat = winston.format.combine(
    winston.format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss'
    }),
    winston.format.errors({ stack: true }),
    winston.format.printf(({ level, message, timestamp, stack }) => {
        return `${timestamp} | ${level.toUpperCase().padStart(8)} | ${stack || message}`;
    })
);

// Create logger
const logger = winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: logFormat,
    transports: [
        // File transport for all logs
        new winston.transports.File({
            filename: path.join(logsDir, 'telegram-bot.log'),
            maxsize: 5242880, // 5MB
            maxFiles: 5,
            tailable: true
        }),
        // File transport for errors only
        new winston.transports.File({
            filename: path.join(logsDir, 'telegram-bot-error.log'),
            level: 'error',
            maxsize: 5242880, // 5MB
            maxFiles: 5,
            tailable: true
        })
    ]
});

// Add console transport for development
if (process.env.NODE_ENV !== 'production') {
    logger.add(new winston.transports.Console({
        format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple()
        )
    }));
}

// Helper functions for bot-specific logging
const logBotEvent = (event, data = {}) => {
    logger.info(`Bot event: ${event}`, {
        event,
        timestamp: new Date().toISOString(),
        data
    });
};

const logUserInteraction = (userId, username, command, data = {}) => {
    logger.info('User interaction', {
        userId,
        username,
        command,
        timestamp: new Date().toISOString(),
        data
    });
};

const logApiCall = (endpoint, method, payload, response, error = null) => {
    const logData = {
        endpoint,
        method,
        payloadSize: payload ? JSON.stringify(payload).length : 0,
        responseCode: response?.status || response?.responseCode || null,
        error: error ? error.message : null,
        timestamp: new Date().toISOString()
    };
    
    if (error) {
        logger.error(`API call failed: ${method} ${endpoint}`, logData);
    } else {
        logger.info(`API call successful: ${method} ${endpoint}`, logData);
    }
    
    return logData;
};

const logError = (error, context = {}) => {
    logger.error('Error occurred', {
        error: error.message,
        stack: error.stack,
        context,
        timestamp: new Date().toISOString()
    });
};

const logBotStartup = (botInfo) => {
    logger.info('🤖 Telegram Bot Started', {
        botUsername: botInfo.username,
        botId: botInfo.id,
        timestamp: new Date().toISOString()
    });
    
    console.log('\n🤖========================================🤖');
    console.log('          FINWISE TELEGRAM BOT            ');
    console.log('🤖========================================🤖');
    console.log(`🤖 Bot Username: @${botInfo.username}`);
    console.log(`🆔 Bot ID: ${botInfo.id}`);
    console.log(`⏰ Started At: ${new Date().toLocaleString()}`);
    console.log('🤖========================================🤖\n');
};

const logBotShutdown = () => {
    logger.info('🛑 Telegram Bot Shutting Down', {
        timestamp: new Date().toISOString()
    });
    
    console.log('\n🛑 Finwise Telegram Bot shutting down...');
};

module.exports = {
    logger,
    logBotEvent,
    logUserInteraction,
    logApiCall,
    logError,
    logBotStartup,
    logBotShutdown
};
