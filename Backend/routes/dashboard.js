const express = require('express');
const router = express.Router();
const dashboardController = require('../controllers/dashboardController');
const { authenticateToken } = require('../middleware/auth');

// Apply authentication middleware to all routes
router.use(authenticateToken);

// @route   GET /api/dashboard/stats
// @desc    Get dashboard statistics
// @access  Private
router.get('/stats', dashboardController.getDashboardStats);

// @route   GET /api/dashboard/charts/:type
// @desc    Get chart data for dashboard
// @access  Private
router.get('/charts/:type', dashboardController.getChartData);

// @route   GET /api/dashboard/insights
// @desc    Get financial insights
// @access  Private
router.get('/insights', dashboardController.getInsights);

module.exports = router;
