'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('feedback', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      feedback_content: {
        type: Sequelize.TEXT,
        allowNull: false,
        validate: {
          notEmpty: true,
          len: [1, 2000]
        },
        comment: 'User feedback content'
      },
      feedback_month: {
        type: Sequelize.INTEGER,
        allowNull: false,
        validate: {
          min: 1,
          max: 12
        },
        comment: 'Month when feedback was given (1-12)'
      },
      feedback_year: {
        type: Sequelize.INTEGER,
        allowNull: false,
        validate: {
          min: 2020
        },
        comment: 'Year when feedback was given'
      },
      rating: {
        type: Sequelize.INTEGER,
        allowNull: true,
        validate: {
          min: 1,
          max: 5
        },
        comment: 'Optional rating from 1-5 stars'
      },
      category: {
        type: Sequelize.ENUM('general', 'feature_request', 'bug_report', 'improvement', 'other'),
        defaultValue: 'general',
        allowNull: false,
        comment: 'Feedback category'
      },
      is_anonymous: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        comment: 'Whether feedback should be treated as anonymous'
      },
      status: {
        type: Sequelize.ENUM('new', 'reviewed', 'in_progress', 'resolved', 'closed'),
        defaultValue: 'new',
        allowNull: false,
        comment: 'Feedback processing status'
      },
      admin_response: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Admin response to feedback'
      },
      responded_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'When admin responded to feedback'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // Add unique constraint
    await queryInterface.addConstraint('feedback', {
      fields: ['user_id', 'feedback_month', 'feedback_year'],
      type: 'unique',
      name: 'unique_user_feedback_month_year'
    });

    // Add indexes
    await queryInterface.addIndex('feedback', ['user_id']);
    await queryInterface.addIndex('feedback', ['feedback_month', 'feedback_year']);
    await queryInterface.addIndex('feedback', ['category']);
    await queryInterface.addIndex('feedback', ['status']);
    await queryInterface.addIndex('feedback', ['rating']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('feedback');
  }
};
