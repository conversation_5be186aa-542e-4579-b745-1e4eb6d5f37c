import React, { useState, useEffect } from "react";
import { X } from "lucide-react";
import useBudgetStore from "../../store/budgetStore";

const BudgetForm = ({ budget, categories, onClose, onSuccess }) => {
  const createBudget = useBudgetStore((state) => state.createBudget);
  const updateBudget = useBudgetStore((state) => state.updateBudget);

  const [formData, setFormData] = useState({
    name: "",
    category_id: "",
    amount: "",
    period_type: "monthly",
    period_start: "",
    period_end: "",
  });

  // Utility: calculate period start/end
  const calculatePeriodDates = (type, startDate = new Date()) => {
    let start = new Date(startDate);
    let end = new Date(start);

    switch (type) {
      case "weekly":
        end.setDate(start.getDate() + 6);
        break;
      case "monthly":
        end = new Date(start.getFullYear(), start.getMonth() + 1, 0);
        break;
      case "quarterly":
        end = new Date(start.getFullYear(), start.getMonth() + 3, 0);
        break;
      case "yearly":
        end = new Date(start.getFullYear(), 11, 31);
        break;
      default:
        break;
    }

    return {
      start: start.toISOString().split("T")[0],
      end: end.toISOString().split("T")[0],
    };
  };

  useEffect(() => {
    if (budget) {
      setFormData({
        ...budget,
        period_start: budget.period_start.split("T")[0],
        period_end: budget.period_end.split("T")[0],
      });
    } else {
      const { start, end } = calculatePeriodDates(formData.period_type);
      setFormData((prev) => ({
        ...prev,
        period_start: start,
        period_end: end,
      }));
    }
  }, [budget]);

  //Auto-update dates when user changes type (only create mode)
  useEffect(() => {
    if (!budget) {
      const { start, end } = calculatePeriodDates(formData.period_type);
      setFormData((prev) => ({
        ...prev,
        period_start: start,
        period_end: end,
      }));
    }
  }, [formData.period_type]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      if (budget) {
        await updateBudget(budget.id, formData);
      } else {
        await createBudget(formData);
      }
      onSuccess();
      onClose();
    } catch (err) {
      console.error("Error saving budget:", err);
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-2xl shadow-lg p-6 w-96 relative">
        <button
          onClick={onClose}
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-800"
        >
          <X size={20} />
        </button>

        <h2 className="text-xl font-bold mb-4">
          {budget ? "Edit Budget" : "New Budget"}
        </h2>

        <form onSubmit={handleSubmit} className="space-y-3">
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            placeholder="Budget Name"
            className="w-full border rounded-lg p-2"
            required
          />

          <select
            name="category_id"
            value={formData.category_id}
            onChange={handleChange}
            className="w-full border rounded-lg p-2"
            required
          >
            <option value="">Select Category</option>
            {categories.map((cat) => (
              <option key={cat.id} value={cat.id}>
                {cat.name}
              </option>
            ))}
          </select>

          <input
            type="number"
            name="amount"
            value={formData.amount}
            onChange={handleChange}
            placeholder="Amount"
            className="w-full border rounded-lg p-2"
            required
          />

          <select
            name="period_type"
            value={formData.period_type}
            onChange={handleChange}
            className="w-full border rounded-lg p-2"
          >
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
            <option value="quarterly">Quarterly</option>
            <option value="yearly">Yearly</option>
          </select>

          <div className="flex justify-between gap-2">
            <input
              type="date"
              name="period_start"
              value={formData.period_start}
              onChange={handleChange}
              className="w-1/2 border rounded-lg p-2"
              disabled={!budget} // only editable in edit mode
            />
            <input
              type="date"
              name="period_end"
              value={formData.period_end}
              onChange={handleChange}
              className="w-1/2 border rounded-lg p-2"
              disabled={!budget} // only editable in edit mode
            />
          </div>

          <button
            type="submit"
            className="w-full bg-blue-600 text-white rounded-lg p-2 hover:bg-blue-700"
          >
            {budget ? "Update Budget" : "Create Budget"}
          </button>
        </form>
      </div>
    </div>
  );
};

export default BudgetForm;
