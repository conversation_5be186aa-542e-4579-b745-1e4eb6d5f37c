# Node modules
node_modules
Backend/node_modules
Frontend/node_modules

# Environment files (will be set via environment variables)
Backend/.env
Frontend/.env
.env

# Development files
Backend/.env.development
Frontend/.env.development

# Build artifacts
Frontend/dist
Backend/dist

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# IDE files
.vscode
.idea
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Documentation
*.md
!README.md

# Test files
Backend/tests
Frontend/src/**/*.test.*
Frontend/src/**/*.spec.*

# Uploads (will be handled by volume)
Backend/uploads/*
!Backend/uploads/.gitkeep
