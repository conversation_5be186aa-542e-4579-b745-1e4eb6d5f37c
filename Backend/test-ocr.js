const fs = require('fs');
const path = require('path');
const enhancedOcrService = require('./services/enhancedOcrService');
const receiptOcrService = require('./services/receiptOcrService');

async function testSwedishReceiptOCR() {
  console.log('🧪 Testing Enhanced OCR with Swedish H&M Receipt Format...\n');

  try {
    // Test with empty buffer to trigger mock service
    const emptyBuffer = Buffer.alloc(0);
    
    console.log('='.repeat(60));
    console.log('📋 Testing Enhanced OCR Service');
    console.log('='.repeat(60));
    
    const enhancedResult = await enhancedOcrService.processImage(emptyBuffer);
    
    if (enhancedResult.success) {
      console.log('✅ Enhanced OCR Success!\n');
      console.log('📄 Full Text Detected:');
      console.log('-'.repeat(40));
      console.log(enhancedResult.data.full_text);
      console.log('-'.repeat(40));
      
      if (enhancedResult.data.total_detection) {
        console.log('\n💰 Total Amount Detection:');
        console.log(`   Amount: ${enhancedResult.data.total_detection.amount_text}`);
        console.log(`   Value: ${enhancedResult.data.total_detection.amount_value}`);
        console.log(`   Currency: ${enhancedResult.data.total_detection.currency}`);
        console.log(`   Confidence: ${enhancedResult.data.total_detection.confidence}`);
        console.log(`   Keyword: ${enhancedResult.data.total_detection.keyword}`);
      }
      
      if (enhancedResult.data.date_extraction && enhancedResult.data.date_extraction.found) {
        console.log('\n📅 Date Detection:');
        console.log(`   Date: ${enhancedResult.data.date_extraction.date}`);
        console.log(`   Formatted: ${enhancedResult.data.date_extraction.formatted_date}`);
        console.log(`   Source: ${enhancedResult.data.date_extraction.source}`);
        console.log(`   Confidence: ${enhancedResult.data.date_extraction.confidence}`);
      }
      
      if (enhancedResult.data.description_extraction && enhancedResult.data.description_extraction.found) {
        console.log('\n🏪 Merchant Detection:');
        console.log(`   Description: ${enhancedResult.data.description_extraction.description}`);
        console.log(`   Merchant: ${enhancedResult.data.description_extraction.merchant_name}`);
        console.log(`   Business Type: ${enhancedResult.data.description_extraction.business_type}`);
        console.log(`   Confidence: ${enhancedResult.data.description_extraction.confidence}`);
      }
    } else {
      console.log('❌ Enhanced OCR Failed:', enhancedResult.data.error);
    }

    console.log('\n\n='.repeat(60));
    console.log('📋 Testing Receipt OCR Service');
    console.log('='.repeat(60));
    
    const receiptResult = await receiptOcrService.processImage(emptyBuffer);
    
    if (receiptResult.success) {
      console.log('✅ Receipt OCR Success!\n');
      console.log('📄 Full Text Detected:');
      console.log('-'.repeat(40));
      console.log(receiptResult.data.full_text);
      console.log('-'.repeat(40));
      
      if (receiptResult.data.total_detection) {
        console.log('\n💰 Total Amount Detection:');
        console.log(`   Amount: ${receiptResult.data.total_detection.amount_text}`);
        console.log(`   Value: ${receiptResult.data.total_detection.amount_value}`);
        console.log(`   Currency: ${receiptResult.data.total_detection.currency}`);
        console.log(`   Confidence: ${receiptResult.data.total_detection.confidence}`);
        console.log(`   Keyword: ${receiptResult.data.total_detection.keyword}`);
      }
      
      if (receiptResult.data.date_extraction && receiptResult.data.date_extraction.found) {
        console.log('\n📅 Date Detection:');
        console.log(`   Date: ${receiptResult.data.date_extraction.date}`);
        console.log(`   Formatted: ${receiptResult.data.date_extraction.formatted_date}`);
        console.log(`   Source: ${receiptResult.data.date_extraction.source}`);
      }
      
      if (receiptResult.data.store_info) {
        console.log('\n🏪 Store Detection:');
        console.log(`   Store Name: ${receiptResult.data.store_info.store_name || 'Not detected'}`);
        console.log(`   Business Type: ${receiptResult.data.store_info.business_type || 'Not detected'}`);
      }
    } else {
      console.log('❌ Receipt OCR Failed:', receiptResult.data.error);
    }

    console.log('\n\n🎯 OCR Testing Summary:');
    console.log('='.repeat(60));
    console.log(`Enhanced OCR: ${enhancedResult.success ? '✅ SUCCESS' : '❌ FAILED'}`);
    console.log(`Receipt OCR:  ${receiptResult.success ? '✅ SUCCESS' : '❌ FAILED'}`);
    
    // Check for Swedish currency detection
    const enhancedCurrency = enhancedResult.data.total_detection?.currency;
    const receiptCurrency = receiptResult.data.total_detection?.currency;
    
    console.log('\n🔍 Swedish Format Detection:');
    console.log(`Enhanced OCR Currency: ${enhancedCurrency || 'Not detected'}`);
    console.log(`Receipt OCR Currency:  ${receiptCurrency || 'Not detected'}`);
    
    if (enhancedCurrency === 'SEK' || receiptCurrency === 'SEK') {
      console.log('✅ Swedish Krona (SEK) detected successfully!');
    } else {
      console.log('⚠️  Swedish Krona (SEK) not detected in currency field');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

// Run the test
if (require.main === module) {
  testSwedishReceiptOCR();
}

module.exports = { testSwedishReceiptOCR };
