import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import useAuthStore from '../../store/authStore';
import { API_ENDPOINTS } from '../../config/api';

const FacebookLogin = ({ onSuccess, onError, className = '' }) => {
  const [loading, setLoading] = useState(false);
  const { login } = useAuthStore();

  const handleFacebookLogin = async () => {
    setLoading(true);
    
    try {
      // Check if Facebook SDK is loaded
      if (typeof window.FB === 'undefined') {
        throw new Error('Facebook SDK not loaded');
      }

      // Get Facebook login status
      window.FB.getLoginStatus((response) => {
        if (response.status === 'connected') {
          // User is already logged in
          handleFacebookAuth(response.authResponse.accessToken);
        } else {
          // User needs to login
          window.FB.login((loginResponse) => {
            if (loginResponse.authResponse) {
              handleFacebookAuth(loginResponse.authResponse.accessToken);
            } else {
              setLoading(false);
              toast.error('Facebook login cancelled');
            }
          }, { scope: 'email,public_profile' });
        }
      });
    } catch (error) {
      setLoading(false);
      console.error('Facebook login error:', error);
      toast.error('Facebook login failed');
      onError?.(error);
    }
  };

  const handleFacebookAuth = async (accessToken) => {
    try {
      const response = await fetch(API_ENDPOINTS.AUTH.FACEBOOK, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ accessToken }),
      });

      const data = await response.json();

      if (data.success) {
        // Store token and user data
        localStorage.setItem('token', data.data.token);
        login(data.data.user, data.data.token);
        
        toast.success(data.message);
        onSuccess?.(data.data);
      } else {
        throw new Error(data.error);
      }
    } catch (error) {
      console.error('Facebook authentication error:', error);
      toast.error(error.message || 'Facebook authentication failed');
      onError?.(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <button
      onClick={handleFacebookLogin}
      disabled={loading}
      className={`
        flex items-center justify-center w-full px-4 py-2 
        bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400
        text-white font-medium rounded-lg transition-colors duration-200
        ${className}
      `}
    >
      {loading ? (
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          <span>Connecting...</span>
        </div>
      ) : (
        <div className="flex items-center space-x-2">
          <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
          </svg>
          <span>Continue with Facebook</span>
        </div>
      )}
    </button>
  );
};

export default FacebookLogin;
