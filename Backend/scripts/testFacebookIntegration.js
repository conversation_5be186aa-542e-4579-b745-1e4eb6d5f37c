require('dotenv').config();
const axios = require('axios');

class FacebookIntegrationTester {
  constructor() {
    this.backendUrl = 'http://localhost:5003';
    this.appId = process.env.FACEBOOK_APP_ID;
    this.appSecret = process.env.FACEBOOK_APP_SECRET;
  }

  async runIntegrationTests() {
    console.log('🔗 FACEBOOK INTEGRATION TESTING');
    console.log('================================');
    console.log(`🖥️  Backend URL: ${this.backendUrl}`);
    console.log(`📱 Facebook App ID: ${this.appId}`);
    console.log('');

    try {
      // Test 1: Backend health check
      await this.testBackendHealth();
      
      // Test 2: Facebook auth endpoints
      await this.testFacebookEndpoints();
      
      // Test 3: Facebook service integration
      await this.testFacebookService();
      
      // Test 4: Token verification endpoint
      await this.testTokenVerification();
      
      console.log('🎉 ALL INTEGRATION TESTS COMPLETED!');
      console.log('');
      console.log('📋 SUMMARY:');
      console.log('✅ Your Facebook app credentials are working perfectly');
      console.log('✅ Backend server is running and responding');
      console.log('✅ Facebook authentication endpoints are active');
      console.log('✅ Ready for frontend integration');
      
    } catch (error) {
      console.error('❌ Integration test failed:', error.message);
    }
  }

  async testBackendHealth() {
    console.log('1️⃣ Testing Backend Health...');
    
    try {
      const response = await axios.get(`${this.backendUrl}/health`, {
        timeout: 5000
      });
      
      console.log('✅ Backend server is healthy');
      console.log(`   Status: ${response.data.status}`);
      console.log(`   Environment: ${response.data.environment}`);
      console.log(`   Uptime: ${Math.round(response.data.uptime)}s`);
      
    } catch (error) {
      console.log('❌ Backend health check failed');
      console.log(`   Error: ${error.message}`);
      throw error;
    }
    console.log('');
  }

  async testFacebookEndpoints() {
    console.log('2️⃣ Testing Facebook Authentication Endpoints...');
    
    // Test Facebook auth endpoint
    try {
      const response = await axios.post(`${this.backendUrl}/api/auth/facebook`, {
        accessToken: 'test_token_for_validation'
      }, {
        timeout: 5000,
        validateStatus: () => true // Accept all status codes
      });
      
      if (response.status === 400 || response.status === 401) {
        console.log('✅ Facebook auth endpoint is responding correctly');
        console.log(`   Status: ${response.status} (Expected for invalid token)`);
        console.log(`   Message: ${response.data.error || response.data.message}`);
      } else {
        console.log(`⚠️ Unexpected response: ${response.status}`);
      }
      
    } catch (error) {
      console.log('❌ Facebook auth endpoint test failed');
      console.log(`   Error: ${error.message}`);
    }

    // Test Facebook token verification endpoint
    try {
      const response = await axios.post(`${this.backendUrl}/api/auth/facebook/verify-token`, {
        accessToken: 'test_token_for_validation'
      }, {
        timeout: 5000,
        validateStatus: () => true
      });
      
      if (response.status === 400 || response.status === 401) {
        console.log('✅ Facebook token verification endpoint is responding');
        console.log(`   Status: ${response.status} (Expected for invalid token)`);
      }
      
    } catch (error) {
      console.log('⚠️ Facebook token verification endpoint test failed');
      console.log(`   Error: ${error.message}`);
    }
    console.log('');
  }

  async testFacebookService() {
    console.log('3️⃣ Testing Facebook Service Integration...');
    
    try {
      // Test if Facebook service can get app access token
      const response = await axios.get(`https://graph.facebook.com/oauth/access_token`, {
        params: {
          client_id: this.appId,
          client_secret: this.appSecret,
          grant_type: 'client_credentials'
        },
        timeout: 10000
      });

      console.log('✅ Facebook service integration working');
      console.log(`   App access token obtained: ***${response.data.access_token.slice(-10)}`);
      
      // Test app info retrieval
      const appInfoResponse = await axios.get(`https://graph.facebook.com/v18.0/${this.appId}`, {
        params: {
          access_token: response.data.access_token,
          fields: 'name,app_domains'
        },
        timeout: 10000
      });
      
      console.log('✅ App information accessible');
      console.log(`   App Name: ${appInfoResponse.data.name}`);
      console.log(`   App Domains: ${appInfoResponse.data.app_domains?.join(', ') || 'None configured'}`);
      
    } catch (error) {
      console.log('❌ Facebook service integration failed');
      console.log(`   Error: ${error.response?.data?.error?.message || error.message}`);
    }
    console.log('');
  }

  async testTokenVerification() {
    console.log('4️⃣ Testing Token Verification Flow...');
    
    try {
      // Get app access token first
      const tokenResponse = await axios.get(`https://graph.facebook.com/oauth/access_token`, {
        params: {
          client_id: this.appId,
          client_secret: this.appSecret,
          grant_type: 'client_credentials'
        }
      });

      const appAccessToken = tokenResponse.data.access_token;
      
      // Test debug token endpoint
      const debugResponse = await axios.get(`https://graph.facebook.com/v18.0/debug_token`, {
        params: {
          input_token: appAccessToken,
          access_token: appAccessToken
        }
      });
      
      console.log('✅ Token verification flow working');
      console.log(`   Token is valid: ${debugResponse.data.data.is_valid}`);
      console.log(`   App ID matches: ${debugResponse.data.data.app_id === this.appId}`);
      console.log(`   Token type: ${debugResponse.data.data.type}`);
      
    } catch (error) {
      console.log('❌ Token verification flow failed');
      console.log(`   Error: ${error.response?.data?.error?.message || error.message}`);
    }
    console.log('');
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new FacebookIntegrationTester();
  tester.runIntegrationTests()
    .then(() => {
      console.log('🎯 NEXT STEPS:');
      console.log('1. Your Facebook app is ready for use!');
      console.log('2. Configure App Domains in Facebook Console:');
      console.log('   - Add "localhost" to App Domains');
      console.log('   - Add "http://localhost:5173" to OAuth Redirect URIs');
      console.log('3. Test Facebook login in your frontend application');
      console.log('4. Make sure your app is in "Live" mode for production');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Integration testing failed:', error);
      process.exit(1);
    });
}

module.exports = FacebookIntegrationTester;
