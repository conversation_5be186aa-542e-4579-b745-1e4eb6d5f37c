# Document AI Setup Guide

This guide will help you set up Google Cloud Document AI for your expense tracker OCR functionality.

## Prerequisites

1. Google Cloud Project with billing enabled
2. Document AI API enabled
3. Service account with Document AI permissions

## Step 1: Enable Document AI API

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project `ocr-docai-470014`
3. Navigate to **APIs & Services** > **Library**
4. Search for "Document AI API"
5. Click **Enable**

## Step 2: Create a Document AI Processor

1. Go to [Document AI Console](https://console.cloud.google.com/ai/document-ai)
2. Click **Create Processor**
3. Select **Form Parser** or **Document OCR** processor type
4. Choose your region (recommend: `us` or `eu`)
5. Give it a name like "Receipt Parser"
6. Click **Create**
7. **Important**: Copy the Processor ID from the processor details page

## Step 3: Update Configuration

1. Open `Backend/services/documentAiService.js`
2. Update the processor configuration:

```javascript
this.processorConfig = {
  projectId: 'ocr-docai-470014',
  location: 'us', // or your chosen location
  processorId: 'YOUR_PROCESSOR_ID_HERE', // Replace with actual processor ID
};
```

## Step 4: Verify Service Account Permissions

Your service account (`ocr-docai-470014-aa0f10c5af80.json`) needs these permissions:

- `roles/documentai.apiUser`
- `roles/storage.objectViewer` (if using Cloud Storage)

To add permissions:

1. Go to **IAM & Admin** > **IAM**
2. Find your service account
3. Click **Edit**
4. Add the required roles

## Step 5: Test the Setup

Run the test script to verify everything is working:

```bash
cd Backend
node test-document-ai.js
```

## Step 6: Test with Real Images

1. Start your backend server:
```bash
npm run dev
```

2. Test the OCR endpoint:
```bash
curl -X POST http://localhost:5001/api/ocr/scan \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "image=@path/to/receipt.jpg"
```

3. Check the status endpoint:
```bash
curl http://localhost:5001/api/ocr/status \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Processor Types

### Form Parser (Recommended)
- Best for structured documents like receipts
- Automatically extracts key-value pairs
- Good for merchant names, dates, totals

### Document OCR
- Basic text extraction
- Good fallback option
- Less structured output

### Custom Processor
- Can be trained for specific receipt formats
- Requires training data
- Best accuracy for your specific use case

## Cost Considerations

- Document AI pricing: $1.50 per 1,000 pages
- First 1,000 pages per month are free
- Monitor usage in Google Cloud Console

## Troubleshooting

### Common Issues

1. **"Processor not found" error**
   - Verify processor ID is correct
   - Check if processor is in the same region as configured

2. **"Permission denied" error**
   - Verify service account has correct permissions
   - Check if API is enabled

3. **"Billing not enabled" error**
   - Enable billing for your Google Cloud project
   - Verify payment method is valid

4. **"Quota exceeded" error**
   - Check your API quotas in Google Cloud Console
   - Request quota increase if needed

### Debug Mode

Enable debug logging by setting environment variable:
```bash
export DEBUG=true
```

## Fallback Strategy

The system uses a multi-tier fallback approach:

1. **Primary**: Document AI (best accuracy)
2. **Fallback 1**: Enhanced OCR with Vision API
3. **Fallback 2**: Basic Vision API
4. **Fallback 3**: Mock service (development)

## Performance Tips

1. **Image Quality**: Use high-resolution, well-lit images
2. **Image Format**: JPEG and PNG work best
3. **File Size**: Keep under 10MB for best performance
4. **Preprocessing**: Consider image enhancement before processing

## Security

- Never commit service account keys to version control
- Use environment variables for sensitive configuration
- Regularly rotate service account keys
- Monitor API usage for unusual activity

## Monitoring

Monitor your Document AI usage:

1. Go to **APIs & Services** > **Credentials**
2. Check quotas and usage
3. Set up billing alerts
4. Monitor error rates in logs

## Next Steps

1. Test with various receipt types
2. Fine-tune extraction patterns if needed
3. Consider training a custom processor for better accuracy
4. Implement caching for frequently processed receipts
5. Add performance monitoring and alerting
