import axios from "axios";
import toast from "react-hot-toast";

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || "/api",
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Token management
const getToken = () => localStorage.getItem("accessToken");
const getRefreshToken = () => localStorage.getItem("refreshToken");
const setTokens = (accessToken, refreshToken) => {
  localStorage.setItem("accessToken", accessToken);
  localStorage.setItem("refreshToken", refreshToken);
};
const clearTokens = () => {
  localStorage.removeItem("accessToken");
  localStorage.removeItem("refreshToken");
};

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      const refreshToken = getRefreshToken();
      if (refreshToken) {
        try {
          const response = await axios.post(
            `${import.meta.env.VITE_API_URL || "/api"}/auth/refresh`,
            { refreshToken },
            {
              headers: {
                "Content-Type": "application/json",
              },
            }
          );

          const { accessToken, refreshToken: newRefreshToken } =
            response.data.data.tokens;
          setTokens(accessToken, newRefreshToken);

          // Retry the original request with new token
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          return api(originalRequest);
        } catch (refreshError) {
          // Refresh failed, redirect to login
          clearTokens();
          window.location.href = "/login";
          return Promise.reject(refreshError);
        }
      } else {
        // No refresh token, redirect to login
        clearTokens();
        window.location.href = "/login";
      }
    }

    // Handle other errors
    if (error.response?.data?.error) {
      toast.error(error.response.data.error);
    } else if (error.message) {
      toast.error(error.message);
    } else {
      toast.error("An unexpected error occurred");
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  register: (userData) => api.post("/auth/register", userData),
  login: (credentials) => api.post("/auth/login", credentials),
  logout: (refreshToken) => api.post("/auth/logout", { refreshToken }),
  refreshToken: (refreshToken) => api.post("/auth/refresh", { refreshToken }),
  forgotPassword: (payload) => api.post("/auth/forgot-password", payload),
  resetPassword: (token, password, confirmPassword) =>
    api.post("/auth/reset-password", { token, password, confirmPassword }),
  verifyPasswordResetOTP: (identifier, otp) =>
    api.post('/auth/verify-password-reset-otp', { identifier, otp }),
  verifyEmail: (token) => api.post("/auth/verify-email", { token }),
  resendVerification: (email) =>
    api.post("/auth/resend-verification", { email }),
};

// User API
export const userAPI = {
  getProfile: () => api.get("/users/profile"),
  updateProfile: (userData) => api.put("/users/profile", userData),
  changePassword: (passwordData) =>
    api.put("/users/change-password", passwordData),
  deleteAccount: () => api.delete("/users/account"),
  updateSettings: (settingsData) => api.put("/users/settings", settingsData),
  getUserStats: () => api.get("/users/stats"),
};

// Transaction API
export const transactionAPI = {
  getTransactions: (params) => api.get("/transactions", { params }),
  getTransaction: (id) => api.get(`/transactions/${id}`),
  createTransaction: (transactionData) =>
    api.post("/transactions", transactionData),
  updateTransaction: (id, transactionData) =>
    api.put(`/transactions/${id}`, transactionData),
  deleteTransaction: (id) => api.delete(`/transactions/${id}`),
  bulkDelete: (ids) => api.delete("/transactions/bulk", { data: { ids } }),
};

// Category API
export const categoryAPI = {
  getCategories: (params) => api.get("/categories", { params }),
  getCategory: (id) => api.get(`/categories/${id}`),
  createCategory: (categoryData) => api.post("/categories", categoryData),
  updateCategory: (id, categoryData) =>
    api.put(`/categories/${id}`, categoryData),
  deleteCategory: (id) => api.delete(`/categories/${id}`),
};

// Budget API
export const budgetAPI = {
  getBudgets: (params) => api.get("/budgets", { params }),
  getBudget: (id) => api.get(`/budgets/${id}`),
  createBudget: (budgetData) => api.post("/budgets", budgetData),
  updateBudget: (id, budgetData) => api.put(`/budgets/${id}`, budgetData),
  deleteBudget: (id) => api.delete(`/budgets/${id}`),
  getBudgetProgress: (id) => api.get(`/budgets/${id}/progress`),
  getBudgetAnalysis: (id, params) =>
    api.get(`/budgets/${id}/analysis`, { params }),
  getBudgetWarnings: (params) => api.get("/budgets/warnings", { params }),
};

// Goal API
export const goalAPI = {
  getGoals: (params) => api.get("/goals", { params }),
  getGoal: (id) => api.get(`/goals/${id}`),
  createGoal: (goalData) => api.post("/goals", goalData),
  updateGoal: (id, goalData) => api.put(`/goals/${id}`, goalData),
  deleteGoal: (id) => api.delete(`/goals/${id}`),
  updateGoalProgress: (id, amount) =>
    api.put(`/goals/${id}/progress`, { amount }),
  getGoalAnalysis: (id, params) => api.get(`/goals/${id}/analysis`, { params }),
};

// OCR API
export const ocrAPI = {
  scanReceipt: (imageFile, options = {}) => {
    const formData = new FormData();
    formData.append("image", imageFile);

    // Add optional parameters
    if (options.enhanceImage !== undefined) {
      formData.append("enhanceImage", options.enhanceImage);
    }
    if (options.detectLanguage) {
      formData.append("detectLanguage", options.detectLanguage);
    }
    if (options.extractItems !== undefined) {
      formData.append("extractItems", options.extractItems);
    }
    if (options.confidenceThreshold !== undefined) {
      formData.append("confidenceThreshold", options.confidenceThreshold);
    }

    return api.post("/ocr/scan", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },
  getStatus: () => api.get("/ocr/status"),
};

// Import/Export API
export const importExportAPI = {
  // Excel Import Functions
  previewImport: async (formData) => {
    const response = await api.post("/import/preview", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  },

  executeImport: async (formData, options = {}) => {
    // Add options to formData
    Object.keys(options).forEach((key) => {
      formData.append(key, options[key]);
    });

    const response = await api.post("/import/execute", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  },

  downloadTemplate: async (format) => {
    const response = await api.get(`/import/template/${format}`, {
      responseType: "blob",
    });
    return response.data;
  },

  getImportHistory: async (params = {}) => {
    const response = await api.get("/import/history", { params });
    return response.data;
  },

  getFormats: async () => {
    const response = await api.get("/import/formats");
    return response.data;
  },

  // Legacy functions for backward compatibility
  importExcel: (file) => {
    const formData = new FormData();
    formData.append("file", file);
    return api.post("/import/excel", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  exportExcel: (params) =>
    api.get("/export/excel/transactions", {
      params,
      responseType: "blob",
    }),

  exportCSV: (params) =>
    api.get("/export/csv/transactions", {
      params,
      responseType: "blob",
    }),

  exportPDF: (params) =>
    api.get("/export/pdf", {
      params,
      responseType: "blob",
    }),

  getImportLogs: (params) => api.get("/import/logs", { params }),
};

// Dashboard API
export const dashboardAPI = {
  getStats: async (params) => {
    const response = await api.get("/dashboard/stats", { params });
    return response.data;
  },

  getChartData: async (type, params = { period: "6months" }) => {
    const response = await api.get(`/dashboard/charts/${type}`, { params });
    return response.data;
  },

  getInsights: async (params) => {
    const response = await api.get("/dashboard/insights", { params });
    return response.data;
  },
};

// Utility functions
export const downloadFile = (blob, filename) => {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

export { api as default, setTokens, clearTokens, getToken, getRefreshToken };
