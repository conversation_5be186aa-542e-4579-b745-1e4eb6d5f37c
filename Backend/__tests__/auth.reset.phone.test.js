const request = require('supertest');
require('dotenv').config();

// Force test env
process.env.NODE_ENV = 'test';
process.env.ENABLE_SMS = 'false'; // prevent real SMS
process.env.PASSWORD_RESET_TWO_STEP = 'false';

const { sequelize, User, PasswordResetToken } = require('../models');
const appFactory = () => require('../server');

// Helper to ensure server only initialized once
let app;

beforeAll(async () => {
  // Ensure DB is connected
  await sequelize.authenticate();
  // Create a test user with multiple phone formats if not existing
  const phone = '+855969983479';
  let user = await User.findOne({ where: { phone } });
  if (!user) {
    user = await User.create({
      name: 'Phone Test',
      email: '<EMAIL>',
      phone,
      password_hash: 'TempPass123!',
    });
  }
  app = appFactory();
});

afterAll(async () => {
  await sequelize.close();
});

describe('Password reset via phone normalization', () => {
  test('Initiate forgot password using local phone format 0969983479', async () => {
    const res = await request(app)
      .post('/api/auth/forgot-password')
      .send({ identifier: '0969983479' })
      .expect(200);

    expect(res.body.success).toBe(true);
    expect(res.body.data.method).toBe('sms');
    // Ensure a token stored
    const user = await User.findOne({ where: { phone: '+855969983479' } });
    const tokenCount = await PasswordResetToken.count({ where: { user_id: user.id } });
    expect(tokenCount).toBeGreaterThan(0);
  });
});
