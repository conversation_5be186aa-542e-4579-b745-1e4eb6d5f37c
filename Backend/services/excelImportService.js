const XLSX = require("xlsx");
const crypto = require("crypto");
const { Transaction } = require("../models");

class ExcelImportService {
  constructor() {
    // Format detection rules
    this.formatDetectionRules = {
      ACLEDA: [
        "CASH IN",
        "CASH IN(Cr)",
        "CASH OUT",
        "CASH OUT(Dr)",
        "CASH IN (Cr)",
        "CASH OUT (Dr)",
        "(Cr)",
        "(Dr)",
      ],
      ABA: ["Money In", "Money Out", "MONEY IN", "MONEY OUT"],
    };

    // Column mapping for internal fields
    this.internalFields = {
      date: "date",
      details: "details",
      money_in: "money_in",
      money_out: "money_out",
      balance: "balance",
      currency: "currency",
      imported_from: "imported_from",
    };

    // ACLEDA format column mappings (case-insensitive)
    this.acledaColumnMappings = {
      date: ["date", "trans date", "transaction date", "DATE"],
      details: [
        "particulars",
        "description",
        "transaction details",
        "descriptions",
        "DESCRIPTIONS",
      ],
      money_in: [
        "cash in(cr)",
        "cash in",
        "cr",
        "cash in (cr)",
        "CASH IN (Cr)",
        "CASH IN",
      ],
      money_out: [
        "cash out(dr)",
        "cash out",
        "dr",
        "cash out (dr)",
        "CASH OUT (Dr)",
        "CASH OUT",
      ],
      balance: ["balance", "running balance", "BALANCE"],
      currency: ["ccy", "currency", "curr", "CCY"],
    };

    // ABA format column mappings (case-insensitive)
    this.abaColumnMappings = {
      date: ["date", "transaction date", "DATE"],
      details: [
        "transaction details",
        "description",
        "details",
        "TRANSACTION DETAILS",
      ],
      money_in: ["money in", "credit", "cr", "MONEY IN"],
      money_out: ["money out", "debit", "dr", "MONEY OUT"],
      balance: ["balance", "account balance", "BALANCE"],
      currency: ["ccy", "currency", "curr", "CCY"],
    };

    // Supported currencies
    this.supportedCurrencies = [
      "USD",
      "KHR",
      "VND",
      "EUR",
      "SEK",
      "NOK",
      "DKK",
      "MYR",
      "THB",
      "SGD",
    ];
  }

  /**
   * Main import function - processes Excel file and returns structured data
   * @param {Buffer} fileBuffer - Excel file buffer
   * @param {string} userId - User ID for import tracking
   * @param {Object} options - Import options
   * @returns {Object} Import result with processed transactions
   */
  async importExcelFile(fileBuffer, userId, options = {}) {
    try {
      console.log("Starting Excel import process...");

      // Step 1: Parse Excel file
      const workbook = XLSX.read(fileBuffer, { type: "buffer" });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];

      // Convert to JSON with header row detection
      const rawData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
        defval: "",
        blankrows: false,
      });

      if (rawData.length === 0) {
        throw new Error("Excel file is empty or contains no data");
      }

      console.log(`Parsed ${rawData.length} rows from Excel file`);

      // Step 2: Detect format (ACLEDA vs ABA)
      const detectedFormat = this.detectFormat(rawData);
      console.log(`Detected format: ${detectedFormat}`);

      // Step 3: Find header row and extract column mappings
      const { headerRowIndex, columnMappings } = this.findHeaderAndMapColumns(
        rawData,
        detectedFormat
      );
      console.log(`Header found at row ${headerRowIndex + 1}`);

      // Step 4: Extract and process transaction rows
      const transactionRows = rawData.slice(headerRowIndex + 1);
      const processedTransactions = this.processTransactionRows(
        transactionRows,
        columnMappings,
        detectedFormat,
        options
      );

      console.log(`Processed ${processedTransactions.length} transactions`);

      // Step 5: Apply duplicate detection
      const { uniqueTransactions, duplicates } = await this.detectDuplicates(
        processedTransactions,
        userId
      );

      console.log(`Found ${duplicates.length} potential duplicates`);

      // Step 6: Generate import summary
      const importSummary = {
        success: true,
        format: detectedFormat,
        totalRows: transactionRows.length,
        processedTransactions: processedTransactions.length,
        uniqueTransactions: uniqueTransactions.length,
        duplicates: duplicates.length,
        errors: processedTransactions.filter((t) => t.hasError).length,
        transactions: uniqueTransactions,
        duplicateTransactions: duplicates,
        errorTransactions: processedTransactions.filter((t) => t.hasError),
        metadata: {
          userId,
          importedAt: new Date().toISOString(),
          fileName: options.fileName || "unknown.xlsx",
          detectedFormat,
          columnMappings,
        },
      };

      return importSummary;
    } catch (error) {
      console.error("Excel import failed:", error);
      return {
        success: false,
        error: error.message,
        totalRows: 0,
        processedTransactions: 0,
        uniqueTransactions: 0,
        duplicates: 0,
        errors: 0,
        transactions: [],
        duplicateTransactions: [],
        errorTransactions: [],
      };
    }
  }

  /**
   * Detect Excel format (ACLEDA vs ABA) based on header content
   * @param {Array} rawData - Raw Excel data
   * @returns {string} Detected format ('ACLEDA' or 'ABA')
   */
  detectFormat(rawData) {
    // Look through first 10 rows for format indicators
    const searchRows = rawData.slice(0, Math.min(10, rawData.length));

    for (const row of searchRows) {
      const rowText = row.join(" ").toUpperCase();

      // Check for ACLEDA indicators
      for (const indicator of this.formatDetectionRules.ACLEDA) {
        if (rowText.includes(indicator.toUpperCase())) {
          return "ACLEDA";
        }
      }

      // Check for ABA indicators
      for (const indicator of this.formatDetectionRules.ABA) {
        if (rowText.includes(indicator.toUpperCase())) {
          return "ABA";
        }
      }
    }

    // Default to ACLEDA if no clear indicators found
    console.log("Could not detect format clearly, defaulting to ACLEDA");
    return "ACLEDA";
  }

  /**
   * Find header row and create column mappings
   * @param {Array} rawData - Raw Excel data
   * @param {string} format - Detected format
   * @returns {Object} Header row index and column mappings
   */
  findHeaderAndMapColumns(rawData, format) {
    const mappings =
      format === "ACLEDA" ? this.acledaColumnMappings : this.abaColumnMappings;

    // Look for header row (usually contains column names)
    for (let i = 0; i < Math.min(20, rawData.length); i++) {
      const row = rawData[i];
      if (!row || row.length === 0) continue;

      // Check if this row looks like a header (contains expected column names)
      const normalizedRow = row.map((cell) =>
        (cell || "").toString().toLowerCase().trim()
      );

      // Special check for ACLEDA format - look for the actual transaction header
      if (format === "ACLEDA") {
        const rowText = normalizedRow.join(" ");
        if (
          rowText.includes("date") &&
          (rowText.includes("cash out") ||
            rowText.includes("cash in") ||
            rowText.includes("descriptions"))
        ) {
          // This is likely the transaction header row
          const columnMappings = {};

          for (let colIndex = 0; colIndex < normalizedRow.length; colIndex++) {
            const cellValue = normalizedRow[colIndex];

            // Map ACLEDA columns
            if (cellValue.includes("date")) {
              columnMappings.date = colIndex;
            } else if (
              cellValue.includes("description") ||
              cellValue.includes("particular")
            ) {
              columnMappings.details = colIndex;
            } else if (
              cellValue.includes("cash out") ||
              cellValue.includes("dr")
            ) {
              columnMappings.money_out = colIndex;
            } else if (
              cellValue.includes("cash in") ||
              cellValue.includes("cr")
            ) {
              columnMappings.money_in = colIndex;
            } else if (cellValue.includes("balance")) {
              columnMappings.balance = colIndex;
            }
          }

          // Ensure we have the minimum required columns
          if (
            columnMappings.date !== undefined &&
            columnMappings.details !== undefined &&
            (columnMappings.money_in !== undefined ||
              columnMappings.money_out !== undefined)
          ) {
            console.log(
              `Found ACLEDA header row at index ${i} with columns:`,
              columnMappings
            );
            return { headerRowIndex: i, columnMappings };
          }
        }
      } else {
        // ABA format detection
        const rowText = normalizedRow.join(" ");
        if (
          rowText.includes("date") &&
          (rowText.includes("money in") || rowText.includes("money out"))
        ) {
          const columnMappings = {};

          for (let colIndex = 0; colIndex < normalizedRow.length; colIndex++) {
            const cellValue = normalizedRow[colIndex];

            // Map ABA columns
            if (cellValue.includes("date")) {
              columnMappings.date = colIndex;
            } else if (
              cellValue.includes("transaction details") ||
              cellValue.includes("description")
            ) {
              columnMappings.details = colIndex;
            } else if (
              cellValue.includes("money out") ||
              cellValue.includes("debit")
            ) {
              columnMappings.money_out = colIndex;
            } else if (
              cellValue.includes("money in") ||
              cellValue.includes("credit")
            ) {
              columnMappings.money_in = colIndex;
            } else if (cellValue.includes("balance")) {
              columnMappings.balance = colIndex;
            } else if (
              cellValue.includes("ccy") ||
              cellValue.includes("currency")
            ) {
              columnMappings.currency = colIndex;
            }
          }

          // Ensure we have the minimum required columns
          if (
            columnMappings.date !== undefined &&
            columnMappings.details !== undefined &&
            (columnMappings.money_in !== undefined ||
              columnMappings.money_out !== undefined)
          ) {
            console.log(
              `Found ABA header row at index ${i} with columns:`,
              columnMappings
            );
            return { headerRowIndex: i, columnMappings };
          }
        }
      }
    }

    throw new Error(`Could not find valid header row for ${format} format`);
  }

  /**
   * Process transaction rows and convert to internal format
   * @param {Array} transactionRows - Raw transaction rows
   * @param {Object} columnMappings - Column index mappings
   * @param {string} format - Detected format
   * @param {Object} options - Processing options
   * @returns {Array} Processed transactions
   */
  processTransactionRows(
    transactionRows,
    columnMappings,
    format,
    options = {}
  ) {
    const processedTransactions = [];

    for (let rowIndex = 0; rowIndex < transactionRows.length; rowIndex++) {
      const row = transactionRows[rowIndex];

      // Skip empty rows
      if (
        !row ||
        row.length === 0 ||
        row.every((cell) => !cell || cell.toString().trim() === "")
      ) {
        continue;
      }

      try {
        const transaction = this.processTransactionRow(
          row,
          columnMappings,
          format,
          rowIndex + 1
        );
        if (transaction) {
          processedTransactions.push(transaction);
        }
      } catch (error) {
        console.error(`Error processing row ${rowIndex + 1}:`, error.message);

        // Add error transaction for reporting
        processedTransactions.push({
          rowNumber: rowIndex + 1,
          hasError: true,
          error: error.message,
          rawData: row,
          date: null,
          details: null,
          money_in: null,
          money_out: null,
          balance: null,
          currency: null,
          imported_from: "excel",
        });
      }
    }

    return processedTransactions;
  }

  /**
   * Process a single transaction row
   * @param {Array} row - Single row data
   * @param {Object} columnMappings - Column mappings
   * @param {string} format - Format type
   * @param {number} rowNumber - Row number for error reporting
   * @returns {Object} Processed transaction
   */
  processTransactionRow(row, columnMappings, format, rowNumber) {
    const transaction = {
      rowNumber,
      hasError: false,
      error: null,
      rawData: row,
      date: null,
      details: null,
      money_in: null,
      money_out: null,
      balance: null,
      currency: null,
      imported_from: "excel",
    };

    // Extract and validate date
    if (columnMappings.date !== undefined) {
      const dateValue = row[columnMappings.date];
      transaction.date = this.parseDate(dateValue);
      if (!transaction.date) {
        throw new Error(`Invalid date: ${dateValue}`);
      }
    } else {
      throw new Error("Date column not found");
    }

    // Extract details/description
    if (columnMappings.details !== undefined) {
      transaction.details = this.sanitizeText(row[columnMappings.details]);
    }

    // Extract amounts based on format
    this.extractAmounts(row, columnMappings, transaction, format);

    // Extract currency
    transaction.currency = this.extractCurrency(row, columnMappings) || "USD";

    // Extract balance if available
    if (columnMappings.balance !== undefined) {
      transaction.balance = this.parseAmount(row[columnMappings.balance]);
    }

    // Validate required fields
    if (!transaction.date) {
      throw new Error("Date is required");
    }

    if (!transaction.money_in && !transaction.money_out) {
      throw new Error(
        "At least one amount (money_in or money_out) is required"
      );
    }

    return transaction;
  }

  /**
   * Extract amounts from row based on format
   * @param {Array} row - Row data
   * @param {Object} columnMappings - Column mappings
   * @param {Object} transaction - Transaction object to populate
   * @param {string} format - Format type
   */
  extractAmounts(row, columnMappings, transaction, format) {
    // Extract money_in
    if (columnMappings.money_in !== undefined) {
      const moneyInValue = row[columnMappings.money_in];
      if (moneyInValue && moneyInValue.toString().trim() !== "") {
        transaction.money_in = this.parseAmount(moneyInValue);
      }
    }

    // Extract money_out
    if (columnMappings.money_out !== undefined) {
      const moneyOutValue = row[columnMappings.money_out];
      if (moneyOutValue && moneyOutValue.toString().trim() !== "") {
        transaction.money_out = this.parseAmount(moneyOutValue);
      }
    }

    // Validation: both amounts should not be present simultaneously
    if (
      transaction.money_in &&
      transaction.money_out &&
      transaction.money_in > 0 &&
      transaction.money_out > 0
    ) {
      console.warn(
        `Row ${transaction.rowNumber}: Both money_in and money_out present, keeping larger amount`
      );

      if (transaction.money_in >= transaction.money_out) {
        transaction.money_out = null;
      } else {
        transaction.money_in = null;
      }
    }
  }

  /**
   * Parse date from various formats
   * @param {*} dateValue - Date value from Excel
   * @returns {string|null} ISO date string or null
   */
  parseDate(dateValue) {
    if (!dateValue) return null;

    try {
      let date;
      const dateStr = dateValue.toString().trim();

      // Handle Excel serial date numbers
      if (typeof dateValue === "number") {
        // Excel date serial number (days since 1900-01-01)
        date = new Date((dateValue - 25569) * 86400 * 1000);
      } else {
        // Handle various text date formats
        if (dateStr.includes("/")) {
          // Handle DD/MM/YYYY or MM/DD/YYYY
          const parts = dateStr.split("/");
          if (parts.length === 3) {
            const day = parseInt(parts[0]);
            const month = parseInt(parts[1]);
            const year = parseInt(parts[2]);

            // If day > 12, it's definitely DD/MM/YYYY format
            if (day > 12) {
              date = new Date(year, month - 1, day);
            } else if (month > 12) {
              // If month > 12, it's MM/DD/YYYY format
              date = new Date(year, day - 1, month);
            } else {
              // Ambiguous case - try DD/MM/YYYY first (more common internationally)
              date = new Date(year, month - 1, day);
              // If that creates an invalid date, try MM/DD/YYYY
              if (isNaN(date.getTime())) {
                date = new Date(year, day - 1, month);
              }
            }
          }
        } else if (dateStr.includes("-")) {
          // Handle YYYY-MM-DD or DD-MM-YYYY
          date = new Date(dateStr);
        } else if (dateStr.match(/\w+\s+\d+,?\s+\d{4}/)) {
          // Handle "Jul 01, 2025" or "July 1 2025" format
          date = new Date(dateStr);
        } else {
          // Try direct parsing
          date = new Date(dateStr);
        }
      }

      // Validate date
      if (isNaN(date.getTime())) {
        return null;
      }

      // Check if date is reasonable (not too far in past/future)
      const now = new Date();
      const tenYearsAgo = new Date(now.getFullYear() - 10, 0, 1);
      const twoYearsFromNow = new Date(now.getFullYear() + 2, 11, 31);

      if (date < tenYearsAgo || date > twoYearsFromNow) {
        return null;
      }

      return date.toISOString().split("T")[0]; // Return YYYY-MM-DD format
    } catch (error) {
      return null;
    }
  }

  /**
   * Parse amount from text, handling various formats
   * @param {*} amountValue - Amount value from Excel
   * @returns {number|null} Parsed amount or null
   */
  parseAmount(amountValue) {
    if (!amountValue) return null;

    try {
      let amountStr = amountValue.toString().trim();

      // Remove currency symbols and extra text
      amountStr = amountStr.replace(/[A-Za-z\s]/g, ""); // Remove letters and spaces
      amountStr = amountStr.replace(/[^\d,.-]/g, ""); // Keep only digits, commas, periods, minus

      // Handle empty string after cleaning
      if (!amountStr) return null;

      // Handle negative amounts in parentheses: (1,234.56) -> -1234.56
      if (amountStr.startsWith("(") && amountStr.endsWith(")")) {
        amountStr = "-" + amountStr.slice(1, -1);
      }

      // Handle thousand separators
      if (amountStr.includes(",") && amountStr.includes(".")) {
        // Format like 1,234.56 - comma is thousand separator
        amountStr = amountStr.replace(/,/g, "");
      } else if (amountStr.includes(",") && !amountStr.includes(".")) {
        // Could be 1,234 (thousands) or 1,56 (decimal)
        const parts = amountStr.split(",");
        if (parts.length === 2 && parts[1].length <= 2) {
          // Likely decimal: 1,56 -> 1.56
          amountStr = amountStr.replace(",", ".");
        } else {
          // Likely thousands: 1,234 -> 1234
          amountStr = amountStr.replace(/,/g, "");
        }
      }

      const amount = parseFloat(amountStr);
      return isNaN(amount) ? null : Math.abs(amount); // Always return positive amount
    } catch (error) {
      return null;
    }
  }

  /**
   * Extract currency from row data
   * @param {Array} row - Row data
   * @param {Object} columnMappings - Column mappings
   * @returns {string} Currency code
   */
  extractCurrency(row, columnMappings) {
    // Check dedicated currency column first
    if (columnMappings.currency !== undefined) {
      const currencyValue = row[columnMappings.currency];
      if (currencyValue) {
        const currency = currencyValue.toString().trim().toUpperCase();
        if (this.supportedCurrencies.includes(currency)) {
          return currency;
        }
      }
    }

    // Check amount columns for currency indicators
    const amountColumns = [
      columnMappings.money_in,
      columnMappings.money_out,
      columnMappings.balance,
    ];

    for (const colIndex of amountColumns) {
      if (colIndex !== undefined && row[colIndex]) {
        const cellValue = row[colIndex].toString().toUpperCase();

        // Check for currency indicators in the cell
        if (cellValue.includes("KHR") || cellValue.includes("៛")) return "KHR";
        if (cellValue.includes("USD") || cellValue.includes("$")) return "USD";
        if (cellValue.includes("EUR") || cellValue.includes("€")) return "EUR";
        if (cellValue.includes("VND") || cellValue.includes("₫")) return "VND";
        if (
          cellValue.includes("SEK") ||
          cellValue.includes("NOK") ||
          cellValue.includes("DKK") ||
          cellValue.includes("KR")
        )
          return "SEK";
      }
    }

    return "USD"; // Default currency
  }

  /**
   * Sanitize text content
   * @param {*} text - Text to sanitize
   * @returns {string} Sanitized text
   */
  sanitizeText(text) {
    if (!text) return "";

    return text
      .toString()
      .trim()
      .replace(/[\r\n\t]/g, " ") // Replace line breaks and tabs with spaces
      .replace(/\s+/g, " ") // Normalize multiple spaces
      .substring(0, 1000); // Limit length
  }

  /**
   * Detect duplicate transactions
   * @param {Array} transactions - Processed transactions
   * @param {string} userId - User ID
   * @returns {Object} Unique transactions and duplicates
   */
  async detectDuplicates(transactions, userId) {
    const uniqueTransactions = [];
    const duplicates = [];
    const seenHashes = new Set();

    // Get existing transactions from database for the user (last 90 days)
    const existingTransactions = await this.getRecentTransactions(userId);
    const existingHashes = new Set(
      existingTransactions.map((t) =>
        this.generateDuplicateKeyFromTransaction(t, userId)
      )
    );

    for (const transaction of transactions) {
      if (transaction.hasError) {
        uniqueTransactions.push(transaction);
        continue;
      }

      // Generate duplicate detection key
      const duplicateKey = this.generateDuplicateKey(transaction, userId);

      // Check for duplicates within the import file
      if (seenHashes.has(duplicateKey)) {
        duplicates.push({
          ...transaction,
          duplicateReason: "Duplicate within import file",
          duplicateKey,
        });
        continue;
      }

      // Check for duplicates against existing database transactions
      if (existingHashes.has(duplicateKey)) {
        duplicates.push({
          ...transaction,
          duplicateReason: "Duplicate with existing transaction",
          duplicateKey,
        });
        continue;
      }

      seenHashes.add(duplicateKey);
      uniqueTransactions.push(transaction);
    }

    return { uniqueTransactions, duplicates };
  }

  /**
   * Get recent transactions from database for duplicate detection
   * @param {string} userId - User ID
   * @returns {Array} Recent transactions
   */
  async getRecentTransactions(userId) {
    try {
      const ninetyDaysAgo = new Date();
      ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

      const transactions = await Transaction.findAll({
        where: {
          user_id: userId,
          date: {
            [require("sequelize").Op.gte]: ninetyDaysAgo,
          },
        },
        attributes: [
          "date",
          "details",
          "money_in",
          "money_out",
          "imported_from",
        ],
        raw: true,
      });

      return transactions;
    } catch (error) {
      console.error("Error fetching recent transactions:", error);
      return []; // Return empty array if database query fails
    }
  }

  /**
   * Generate duplicate key from existing transaction
   * @param {Object} transaction - Existing transaction from database
   * @param {string} userId - User ID
   * @returns {string} Duplicate detection key
   */
  generateDuplicateKeyFromTransaction(transaction, userId) {
    const amount = transaction.money_in || transaction.money_out || 0;
    const amountSide = transaction.money_in ? "IN" : "OUT";
    const truncatedDetails = (transaction.details || "").substring(0, 100);
    const detailsHash = crypto
      .createHash("md5")
      .update(truncatedDetails)
      .digest("hex")
      .substring(0, 8);

    // Format date to YYYY-MM-DD if it's a Date object
    const dateStr =
      transaction.date instanceof Date
        ? transaction.date.toISOString().split("T")[0]
        : transaction.date;

    return `${userId}_${dateStr}_${amountSide}:${amount}_${detailsHash}`;
  }

  /**
   * Generate duplicate detection key
   * @param {Object} transaction - Transaction object
   * @param {string} userId - User ID
   * @returns {string} Duplicate detection key
   */
  generateDuplicateKey(transaction, userId) {
    // Create a deterministic key based on user, date, amount, and partial description
    const amount = transaction.money_in || transaction.money_out || 0;
    const amountSide = transaction.money_in ? "IN" : "OUT";
    const truncatedDetails = (transaction.details || "").substring(0, 100);
    const detailsHash = crypto
      .createHash("md5")
      .update(truncatedDetails)
      .digest("hex")
      .substring(0, 8);

    return `${userId}_${transaction.date}_${amountSide}:${amount}_${detailsHash}`;
  }

  /**
   * Create sample template for download
   * @param {string} format - Format type ('ACLEDA' or 'ABA')
   * @returns {Buffer} Excel file buffer
   */
  createSampleTemplate(format) {
    const sampleData =
      format === "ACLEDA"
        ? this.getAcledaSampleData()
        : this.getAbaSampleData();

    const worksheet = XLSX.utils.aoa_to_sheet(sampleData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Sample");

    return XLSX.write(workbook, { type: "buffer", bookType: "xlsx" });
  }

  /**
   * Get ACLEDA sample data
   * @returns {Array} Sample data array
   */
  getAcledaSampleData() {
    return [
      ["ACCOUNT STATEMENT"],
      ["FROM Jul 01, 2025 TO Jul 31, 2025"],
      [""],
      ["CHOENG RAYU"],
      ["Address : Phnom Penh"],
      [""],
      ["TRANSACTION DETAILS"],
      ["DATE", "DESCRIPTIONS", "CASH OUT (Dr)", "CASH IN (Cr)", "BALANCE"],
      ["Jul 01, 2025", "Paid To MERCHANT NAME", "1,000.00", "", "7,592.00 KHR"],
      [
        "Jul 02, 2025",
        "Received From SENDER NAME",
        "",
        "50,000.00",
        "57,592.00 KHR",
      ],
      ["Jul 03, 2025", "ATM Withdrawal", "5,000.00", "", "52,592.00 KHR"],
    ];
  }

  /**
   * Get ABA sample data
   * @returns {Array} Sample data array
   */
  getAbaSampleData() {
    return [
      ["ACCOUNT ACTIVITY"],
      [""],
      [
        "Date",
        "Transaction Details",
        "Money In",
        "Ccy",
        "Money Out",
        "Ccy",
        "Balance",
        "Ccy",
      ],
      [
        "Jul 01, 2025",
        "FUNDS TRANSFERRED TO MERCHANT",
        "",
        "",
        "3.50",
        "USD",
        "102.05",
        "USD",
      ],
      [
        "Jul 02, 2025",
        "PURCHASE AT STORE",
        "",
        "",
        "1.25",
        "USD",
        "100.80",
        "USD",
      ],
      [
        "Jul 02, 2025",
        "FUNDS RECEIVED FROM SENDER",
        "50.00",
        "USD",
        "",
        "",
        "150.80",
        "USD",
      ],
    ];
  }

  /**
   * Validate import file before processing
   * @param {Buffer} fileBuffer - File buffer
   * @returns {Object} Validation result
   */
  validateImportFile(fileBuffer) {
    try {
      // Check file size (max 10MB)
      if (fileBuffer.length > 10 * 1024 * 1024) {
        return { valid: false, error: "File size exceeds 10MB limit" };
      }

      // Try to parse as Excel
      const workbook = XLSX.read(fileBuffer, { type: "buffer" });

      if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
        return { valid: false, error: "Excel file contains no worksheets" };
      }

      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      if (data.length < 2) {
        return {
          valid: false,
          error:
            "Excel file must contain at least a header row and one data row",
        };
      }

      return { valid: true, rowCount: data.length };
    } catch (error) {
      return { valid: false, error: "Invalid Excel file format" };
    }
  }
}

module.exports = new ExcelImportService();
