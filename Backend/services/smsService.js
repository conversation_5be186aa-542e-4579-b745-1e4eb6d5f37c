const { admin } = require("../config/firebase.js");
let twilioClient = null;
if (
  process.env.SMS_PROVIDER === "twilio" &&
  process.env.TWILIO_ACCOUNT_SID &&
  process.env.TWILIO_AUTH_TOKEN
) {
  try {
    twilioClient = require("twilio")(
      process.env.TWILIO_ACCOUNT_SID,
      process.env.TWILIO_AUTH_TOKEN
    );
    console.log("✅ Twilio client initialized");
  } catch (e) {
    console.error("❌ Failed to initialize Twilio client:", e.message);
  }
}

class SMSService {
  constructor() {
    this.initialized = false;
    this.initializeService();
  }

  initializeService() {
    try {
      // Check if Firebase Admin is properly initialized
      if (admin.apps.length > 0) {
        this.initialized = true;
        console.log("✅ SMS service initialized successfully");
      } else {
        console.log(
          "⚠️ Firebase Admin not initialized, SMS service will use mock mode"
        );
      }
    } catch (error) {
      console.error("❌ Failed to initialize SMS service:", error);
    }
  }

  async sendOTP(phoneNumber, otp, type = "password_reset", options = {}) {
    try {
      if (process.env.ENABLE_SMS !== "true") {
        return this.sendMockSMS(phoneNumber, otp, type, options);
      }

      if (process.env.SMS_PROVIDER === "twilio" && twilioClient) {
        return this.sendViaTwilio(phoneNumber, otp, type, options);
      }

      // Fallback to mock for now (Firebase phone auth not implemented here)
      return this.sendMockSMS(phoneNumber, otp, type, options);
    } catch (error) {
      console.error("❌ Failed to send SMS:", error);
      throw new Error("Failed to send SMS");
    }
  }

  async sendMockSMS(phoneNumber, otp, type, options = {}) {
    try {
      const message = this.getSMSMessage(otp, type, options);

      console.log(`📱 Mock SMS to ${phoneNumber}:`);
      console.log(`Message: ${message}`);
      console.log(`OTP: ${otp}`);

      // Simulate SMS sending delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      return {
        success: true,
        messageId: `mock-sms-${Date.now()}`,
        provider: "mock",
      };
    } catch (error) {
      console.error("❌ Failed to send mock SMS:", error);
      throw new Error("Failed to send mock SMS");
    }
  }

  async sendRealSMS(phoneNumber, otp, type, options = {}) {
    try {
      // This would be the real Firebase SMS implementation
      // Note: Firebase SMS requires additional setup and billing

      const message = this.getSMSMessage(otp, type, options);

      // Example Firebase SMS implementation (requires proper setup):
      /*
      const messaging = admin.messaging();
      
      const smsMessage = {
        notification: {
          title: 'Finwise Verification',
          body: message
        },
        token: phoneNumber // This would need to be a FCM token, not phone number
      };
      
      const response = await messaging.send(smsMessage);
      */

      // For now, fallback to mock
      return this.sendMockSMS(phoneNumber, otp, type);
    } catch (error) {
      console.error("❌ Failed to send real SMS:", error);
      throw new Error("Failed to send SMS via Firebase");
    }
  }

  getSMSMessage(otp, type, options = {}) {
    // Combined flow message (OTP + short link) if provided
    if (options.combined && options.resetLink) {
      // Keep within 160 chars
      return `Finwise Reset: Code ${otp} (10m). Or open: ${options.resetLink}`.slice(0, 160);
    }
    const messages = {
      password_reset: `Finwise: Reset code ${otp} (10m).`,
      email_verification: `Finwise: Email verify code ${otp} (10m).`,
      login_verification: `Finwise: Login verify code ${otp} (10m).`,
      default: `Finwise: Code ${otp} (10m).`,
    };
    return messages[type] || messages.default;
  }

  formatPhoneNumber(phoneNumber) {
    // Remove all non-digit characters
    let cleaned = phoneNumber.replace(/\D/g, "");

    // Add country code if not present (assuming Cambodia +855)
    if (!cleaned.startsWith("855") && !cleaned.startsWith("+855")) {
      if (cleaned.startsWith("0")) {
        cleaned = "855" + cleaned.substring(1);
      } else {
        cleaned = "855" + cleaned;
      }
    }

    // Add + prefix
    if (!cleaned.startsWith("+")) {
      cleaned = "+" + cleaned;
    }

    return cleaned;
  }

  validatePhoneNumber(phoneNumber) {
    const formatted = this.formatPhoneNumber(phoneNumber);

    // Basic validation for Cambodian phone numbers
    const cambodianPhoneRegex = /^\+855[1-9]\d{7,8}$/;

    return {
      isValid: cambodianPhoneRegex.test(formatted),
      formatted: formatted,
      original: phoneNumber,
    };
  }

  async verifyConnection() {
    try {
      if (this.initialized) {
        console.log("✅ SMS service connection verified (Firebase)");
        return true;
      } else {
        console.log("⚠️ SMS service running in mock mode");
        return true; // Mock mode is always "connected"
      }
    } catch (error) {
      console.error("❌ SMS service connection failed:", error);
      return false;
    }
  }

  // Method to send SMS via third-party provider (alternative to Firebase)
  async sendViaTwilio(phoneNumber, otp, type, options = {}) {
    try {
      const body = this.getSMSMessage(otp, type, options);
      const messagingServiceSid = process.env.TWILIO_MESSAGING_SERVICE_SID;
      const fromNumber = process.env.TWILIO_FROM_NUMBER;
      if (!messagingServiceSid && !fromNumber) {
        console.warn("⚠️ No Twilio MessagingServiceSid or TWILIO_FROM_NUMBER set; using mock");
        return this.sendMockSMS(phoneNumber, otp, type, options);
      }
      const msg = await twilioClient.messages.create({
        to: phoneNumber,
        body,
        ...(messagingServiceSid
          ? { messagingServiceSid }
          : { from: fromNumber })
      });
      console.log("✅ Twilio SMS sent:", msg.sid);
      return { success: true, messageId: msg.sid, provider: "twilio" };
    } catch (e) {
      console.error("❌ Twilio SMS failed, falling back to mock:", e.message);
      return this.sendMockSMS(phoneNumber, otp, type, options);
    }
  }

  // Method to send SMS via local provider (for Cambodia)
  async sendViaLocalProvider(phoneNumber, otp, type) {
    // This would integrate with local Cambodian SMS providers
    // For now, we'll use mock
    console.log(
      "📱 Local SMS provider integration not implemented, using mock"
    );
    return this.sendMockSMS(phoneNumber, otp, type);
  }
}

module.exports = new SMSService();
