const express = require("express");
const router = express.Router();

// Import controllers and middleware
const ocrController = require("../controllers/ocrController");
const { authenticateToken } = require("../middleware/auth");
const { validateOCRScan } = require("../validators/ocrValidators");
const { checkUsageLimit, trackUsage } = require("../middleware/usageLimits");

// Apply authentication middleware to all routes
router.use(authenticateToken);

// @route   POST /api/ocr/scan
// @desc    Scan receipt image and extract transaction data
// @access  Private
router.post(
  "/scan",
  checkUsageLimit('ocr_scan'),
  trackUsage,
  ocrController.upload.single("image"),
  validateOCRScan,
  ocrController.scanReceipt
);

// @route   GET /api/ocr/status
// @desc    Get OCR service status
// @access  Private
router.get("/status", ocrController.getOCRStatus);

module.exports = router;
