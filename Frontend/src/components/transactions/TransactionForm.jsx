import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import {
  X,
  DollarSign,
  Calendar,
  FileText,
  Tag,
  Camera,
  Target,
} from "lucide-react";
import useTransactionStore from "../../store/transactionStore";
import ReceiptScanner from "../ocr/ReceiptScanner";

const TransactionForm = ({
  isOpen,
  onClose,
  transaction = null,
  initialData = null,
  onSuccess,
}) => {
  const [transactionType, setTransactionType] = useState("expense");
  const [showScanner, setShowScanner] = useState(false);
  const {
    createTransaction,
    updateTransaction,
    categories,
    fetchCategories,
    isLoading,
  } = useTransactionStore();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm({
    defaultValues: {
      date: new Date().toISOString().split("T")[0],
      details: "",
      amount: "",
      currency: "USD",
      category_id: "",
      notes: "",
      reference_number: "",
    },
  });

  const watchedCategoryId = watch("category_id");

  // OCR handler
  const handleOCRData = (extractedData) => {
    const isIncome = extractedData.money_in > 0;
    setTransactionType(isIncome ? "income" : "expense");

    setValue("date", extractedData.date);
    setValue("details", extractedData.details);
    setValue("amount", isIncome ? extractedData.money_in : extractedData.money_out);
    setValue("currency", extractedData.currency);
    setValue("category_id", extractedData.category_id || "");
    setValue("notes", extractedData.notes || "");
    setValue("reference_number", extractedData.reference_number || "");

    setShowScanner(false);
  };

  useEffect(() => {
    if (isOpen) {
      fetchCategories();

      if (transaction) {
        const isIncome = transaction.money_in > 0;
        setTransactionType(isIncome ? "income" : "expense");

        reset({
          date: transaction.date,
          details: transaction.details,
          amount: isIncome ? transaction.money_in : transaction.money_out,
          currency: transaction.currency,
          category_id: transaction.category_id || "",
          notes: transaction.notes || "",
          reference_number: transaction.reference_number || "",
        });
      } else if (initialData) {
        const isIncome = initialData.money_in > 0;
        setTransactionType(isIncome ? "income" : "expense");

        reset({
          date: initialData.date || new Date().toISOString().split("T")[0],
          details: initialData.details || "",
          amount: isIncome ? initialData.money_in : initialData.money_out,
          currency: initialData.currency || "USD",
          category_id: initialData.category_id || "",
          notes: initialData.notes || "",
          reference_number: initialData.reference_number || "",
        });
      } else {
        reset({
          date: new Date().toISOString().split("T")[0],
          details: "",
          amount: "",
          currency: "USD",
          category_id: "",
          notes: "",
          reference_number: "",
        });
        setTransactionType("expense");
      }
    }
  }, [isOpen, transaction, initialData, reset, fetchCategories]);

  const onSubmit = async (data) => {
    const transactionData = {
      date: data.date,
      details: data.details,
      currency: data.currency,
      category_id: data.category_id || null,
      notes: data.notes || null,
      reference_number: data.reference_number || null,
      money_in: transactionType === "income" ? parseFloat(data.amount) : 0,
      money_out: transactionType === "expense" ? parseFloat(data.amount) : 0,
    };

    let result;
    if (transaction) {
      result = await updateTransaction(transaction.id, transactionData);
    } else {
      result = await createTransaction(transactionData);
    }

    if (result.success) {
      onClose();
      if (onSuccess) onSuccess();
    }
  };

  const filteredCategories = categories.filter(
    (cat) => cat.type === transactionType
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 py-6 text-center sm:p-0">
        {/* Overlay */}
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={onClose}
        ></div>

        {/* Modal */}
        <div className="inline-block w-full max-w-lg p-6 my-8 overflow-hidden text-left align-middle transition-all transform
          bg-[#e2e8ec] dark:bg-[#1E1E2D] shadow-2xl rounded-xl"
        >
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-[#636160] dark:text-[#e2e8ec]">
              {transaction ? "Edit Transaction" : "Add New Transaction"}
            </h3>

            <div className="flex items-center space-x-2">
              {/* Add Goal button */}
              <button
                type="button"
                className="flex items-center px-3 py-1.5 text-xs font-medium bg-[#1E3A8A] dark:bg-[#7E57C2] hover:bg-[#5C66B3] text-white rounded-md transition"
              >
                <Target className="w-4 h-4 mr-1" />
                Add Goal
              </button>

              {/* OCR button */}
              {!transaction && (
                <button
                  type="button"
                  onClick={() => setShowScanner(true)}
                  className="p-2 rounded-md bg-white dark:bg-[#252736] text-[#1E3A8A] dark:text-white shadow hover:bg-[#8B5CF6] dark:hover:bg-[#3A3B4E] hover:text-white transition"
                  title="Scan Receipt"
                >
                  <Camera className="w-4 h-4" />
                </button>
              )}

              {/* Close */}
              <button
                onClick={onClose}
                className="p-2 rounded-md hover:bg-gray-200 dark:hover:bg-[#3A3B4E]"
              >
                <X className="w-5 h-5 text-gray-600 dark:text-gray-200" />
              </button>
            </div>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-5 text-gray-700 dark:text-gray-200">
            {/* Transaction Type */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Transaction Type
              </label>
              <div className="flex space-x-4">
                {["income", "expense"].map((type) => (
                  <label
                    key={type}
                    className={`px-3 py-1 rounded-lg cursor-pointer border transition
                      ${transactionType === type
                        ? "bg-[#1E3A8A] dark:bg-[#7E57C2] text-white border-none"
                        : "bg-[#e2e8ec] dark:bg-[#252736] text-[#636160] dark:text-[#e2e8ec] border-[#c6d2dd] dark:border-[#3A3B4E] hover:bg-[#aabbcd] dark:hover:bg-[#3A3B4E] hover:text-white dark:hover:text-white"
                      }`}
                  >
                    <input
                      type="radio"
                      value={type}
                      checked={transactionType === type}
                      onChange={(e) => setTransactionType(e.target.value)}
                      className="hidden"
                    />
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </label>
                ))}
              </div>
            </div>

            {/* Date */}
            <div>
              <label className="block text-sm font-medium mb-1">
                <Calendar className="w-4 h-4 inline mr-1" />
                Date
              </label>
              <input
                {...register("date", { required: "Date is required" })}
                type="date"
                className="form-input bg-[#f0f2f5] dark:bg-[#252736] border border-[#c6d2dd] dark:border-[#3A3B4E] text-[#636160] dark:text-[#e2e8ec] rounded-md px-3 py-2 w-full"
              />
            </div>

            {/* Details */}
            <div>
              <label className="block text-sm font-medium mb-1">
                <FileText className="w-4 h-4 inline mr-1" />
                Description
              </label>
              <input
                {...register("details", { required: "Description is required" })}
                type="text"
                placeholder="Enter description"
                className="form-input bg-[#f0f2f5] dark:bg-[#252736] border border-[#c6d2dd] dark:border-[#3A3B4E] text-[#636160] dark:text-[#e2e8ec] rounded-md px-3 py-2 w-full"
              />

            </div>

            {/* Amount + Currency */}
            <div className="grid grid-cols-3 gap-3">
              <div className="col-span-2">
                <label className="block text-sm font-medium mb-1">
                  <DollarSign className="w-4 h-4 inline mr-1" />
                  Amount
                </label>
                <input
                  {...register("amount", { required: "Amount is required" })}
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  className="form-input bg-[#f0f2f5] dark:bg-[#252736] border border-[#c6d2dd] dark:border-[#3A3B4E] text-[#636160] dark:text-[#e2e8ec] rounded-md px-3 py-2 w-full"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Currency</label>
                <select
                  {...register("currency")}
                  className="form-select bg-[#f0f2f5] dark:bg-[#252736] border border-[#c6d2dd] dark:border-[#3A3B4E] text-[#636160] dark:text-[#e2e8ec] rounded-md px-3 py-2 w-full"
                >
                  <option value="USD">USD</option>
                  <option value="KHR">KHR</option>
                </select>
              </div>
            </div>

            {/* Category */}
            <div>
              <label className="block text-sm font-medium mb-1">
                <Tag className="w-4 h-4 inline mr-1" />
                Category
              </label>
              <select
                {...register("category_id")}
                className="form-select bg-[#f0f2f5] dark:bg-[#252736] border border-[#c6d2dd] dark:border-[#3A3B4E] text-[#636160] dark:text-[#e2e8ec] rounded-md px-3 py-2 w-full"
              >
                <option value="">Select category</option>
                {filteredCategories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Notes */}
            <div>
              <label className="block text-sm font-medium mb-1">
                Notes (Optional)
              </label>
              <textarea
                {...register("notes")}
                rows={3}
                placeholder="Additional notes"
                className="form-textarea bg-[#f0f2f5] dark:bg-[#252736] border border-[#c6d2dd] dark:border-[#3A3B4E] text-[#636160] dark:text-[#e2e8ec] rounded-md px-3 py-2 w-full"
              />
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 rounded-md border bg-[#e2e8ec] dark:bg-[#252736] text-[#636160] dark:text-[#e2e8ec] hover:bg-[#c6d2dd] dark:hover:bg-[#3A3B4E] transition"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 rounded-md bg-[#1E3A8A] dark:bg-[#7E57C2] hover:bg-[#5C66B3] text-white transition"
              >
                {transaction ? "Update Transaction" : "Create Transaction"}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Receipt Scanner */}
      <ReceiptScanner
        isOpen={showScanner}
        onClose={() => setShowScanner(false)}
        onTransactionExtracted={handleOCRData}
      />
    </div>
  );
};

export default TransactionForm;
