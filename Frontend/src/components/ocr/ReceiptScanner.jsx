import React, { useState, useRef } from "react";
import { useDropzone } from "react-dropzone";
import {
  Camera,
  Upload,
  X,
  CheckCircle,
  AlertCircle,
  Loader,
  Image as ImageIcon,
  FileText,
  DollarSign,
  Calendar,
} from "lucide-react";
import useOCRStore from "../../store/ocrStore";
import useTransactionStore from "../../store/transactionStore";

const ReceiptScanner = ({ isOpen, onClose, onTransactionExtracted }) => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const fileInputRef = useRef(null);

  const {
    scanReceipt,
    isScanning,
    scanResult,
    error,
    clearScanResult,
    clearError,
  } = useOCRStore();

  const { createTransaction } = useTransactionStore();

  const onDrop = (acceptedFiles) => {
    const file = acceptedFiles[0];
    if (file) {
      setSelectedImage(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".jpeg", ".jpg", ".png", ".webp"],
    },
    maxFiles: 1,
    maxSize: 10 * 1024 * 1024, // 10MB
  });

  const handleScan = async () => {
    if (!selectedImage) return;

    const result = await scanReceipt(selectedImage);
    if (result.success && onTransactionExtracted) {
      onTransactionExtracted(result.data.suggested_transaction);
    }
  };

  const handleCreateTransaction = async () => {
    if (!scanResult?.suggested_transaction) return;

    const result = await createTransaction(scanResult.suggested_transaction);
    if (result.success) {
      handleClose();
    }
  };

  const handleClose = () => {
    setSelectedImage(null);
    setImagePreview(null);
    clearScanResult();
    clearError();
    onClose();
  };

  const handleCameraCapture = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      setSelectedImage(file);

      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
          onClick={handleClose}
        ></div>

        {/* Modal */}
        <div className="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900">Scan Receipt</h3>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Content */}
          <div className="space-y-6">
            {/* Image Upload/Preview */}
            {!imagePreview ? (
              <div className="space-y-4">
                {/* Dropzone */}
                <div
                  {...getRootProps()}
                  className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                    isDragActive
                      ? "border-primary-500 bg-primary-50"
                      : "border-gray-300 hover:border-primary-400"
                  }`}
                >
                  <input {...getInputProps()} />
                  <Upload className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-lg font-medium text-gray-900 mb-2">
                    {isDragActive
                      ? "Drop the receipt here"
                      : "Upload Receipt Image"}
                  </p>
                  <p className="text-sm text-gray-500 mb-4">
                    Drag and drop your receipt image, or click to browse
                  </p>
                  <p className="text-xs text-gray-400">
                    Supports JPEG, PNG, WebP (max 10MB)
                  </p>
                </div>

                {/* Camera Option */}
                <div className="text-center">
                  <p className="text-sm text-gray-500 mb-2">Or</p>
                  <button
                    onClick={handleCameraCapture}
                    className="btn btn-outline inline-flex items-center"
                  >
                    <Camera className="w-4 h-4 mr-2" />
                    Take Photo
                  </button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    capture="environment"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Image Preview */}
                <div className="relative">
                  <img
                    src={imagePreview}
                    alt="Receipt preview"
                    className="w-full max-h-64 object-contain rounded-lg border"
                  />
                  <button
                    onClick={() => {
                      setSelectedImage(null);
                      setImagePreview(null);
                      clearScanResult();
                    }}
                    className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>

                {/* Scan Button */}
                {!scanResult && !isScanning && (
                  <div className="text-center">
                    <button
                      onClick={handleScan}
                      className="btn btn-primary"
                      disabled={isScanning}
                    >
                      <FileText className="w-4 h-4 mr-2" />
                      Scan Receipt
                    </button>
                  </div>
                )}

                {/* Loading State */}
                {isScanning && (
                  <div className="text-center py-8">
                    <Loader className="w-8 h-8 animate-spin mx-auto text-primary-600 mb-4" />
                    <p className="text-gray-600">Scanning receipt...</p>
                    <p className="text-sm text-gray-500">
                      This may take a few seconds
                    </p>
                  </div>
                )}

                {/* Error State */}
                {error && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="flex items-center">
                      <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
                      <p className="text-red-800">{error}</p>
                    </div>
                  </div>
                )}

                {/* Scan Results */}
                {scanResult && (
                  <div className="space-y-4">
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <div className="flex items-center mb-3">
                        <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                        <h4 className="font-medium text-green-800">
                          Receipt Scanned Successfully
                        </h4>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div className="space-y-2">
                          <div className="flex items-center">
                            <FileText className="w-4 h-4 text-gray-500 mr-2" />
                            <span className="font-medium">Merchant:</span>
                            <span className="ml-2">
                              {scanResult.description_extraction
                                ?.merchant_name ||
                                scanResult.suggested_transaction
                                  ?.merchant_name ||
                                "Not detected"}
                            </span>
                          </div>

                          <div className="flex items-center">
                            <Calendar className="w-4 h-4 text-gray-500 mr-2" />
                            <span className="font-medium">Date:</span>
                            <span className="ml-2">
                              {scanResult.date_extraction?.date ||
                                scanResult.suggested_transaction?.date ||
                                "Not detected"}
                            </span>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center">
                            <DollarSign className="w-4 h-4 text-gray-500 mr-2" />
                            <span className="font-medium">Amount:</span>
                            <span className="ml-2 font-bold text-green-600">
                              {scanResult.total_detection?.amount_text ||
                                `${
                                  scanResult.suggested_transaction?.currency ||
                                  "$"
                                } ${
                                  scanResult.suggested_transaction?.money_out ||
                                  "0.00"
                                }`}
                            </span>
                          </div>

                          <div className="flex items-center">
                            <span className="font-medium">Confidence:</span>
                            <span className="ml-2">
                              {scanResult.total_detection?.confidence
                                ? `${Math.round(
                                    scanResult.total_detection.confidence
                                  )}%`
                                : scanResult.suggested_transaction
                                    ?.ocr_confidence?.total
                                ? `${Math.round(
                                    scanResult.suggested_transaction
                                      .ocr_confidence.total
                                  )}%`
                                : "N/A"}
                            </span>
                          </div>
                        </div>
                      </div>

                      {scanResult.description_extraction?.business_type && (
                        <div className="mt-3 pt-3 border-t border-green-200">
                          <p className="font-medium text-green-800 mb-2">
                            Business Type:
                          </p>
                          <p className="text-sm text-green-700 capitalize">
                            {scanResult.description_extraction.business_type}
                          </p>
                        </div>
                      )}

                      {scanResult.debug_info && (
                        <div className="mt-3 pt-3 border-t border-green-200">
                          <p className="font-medium text-green-800 mb-2">
                            Processing Info:
                          </p>
                          <div className="text-xs text-green-600 grid grid-cols-2 gap-2">
                            <span>
                              Tokens: {scanResult.debug_info.raw_tokens}
                            </span>
                            <span>
                              Lines: {scanResult.debug_info.lines_detected}
                            </span>
                            <span>
                              Keywords: {scanResult.debug_info.keyword_anchors}
                            </span>
                            <span>
                              Amounts: {scanResult.debug_info.amount_candidates}
                            </span>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex justify-end space-x-3">
                      <button
                        onClick={() => {
                          if (onTransactionExtracted) {
                            onTransactionExtracted(
                              scanResult.suggested_transaction
                            );
                          }
                          handleClose();
                        }}
                        className="btn btn-outline"
                      >
                        Use in Form
                      </button>
                      <button
                        onClick={handleCreateTransaction}
                        className="btn btn-primary"
                      >
                        Create Transaction
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReceiptScanner;
