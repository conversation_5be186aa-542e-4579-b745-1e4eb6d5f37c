const express = require('express');
const router = express.Router();
const { body, param, query } = require('express-validator');
const {
  getNotifications,
  getUnreadCount,
  markAsRead,
  markAllAsRead,
  createNotification,
  deleteNotification,
  getNotificationStats
} = require('../controllers/notificationController');
const { authenticateToken } = require('../middleware/auth');

// Apply authentication to all routes
router.use(authenticateToken);

// Validation middleware
const validateNotificationId = [
  param('notificationId')
    .isUUID()
    .withMessage('Valid notification ID is required')
];

const validateCreateNotification = [
  body('userId')
    .isUUID()
    .withMessage('Valid user ID is required'),
  body('type')
    .isIn([
      'payment_success',
      'payment_failed', 
      'subscription_expiring',
      'subscription_expired',
      'usage_limit_reached',
      'usage_limit_warning',
      'system_maintenance',
      'security_alert',
      'feature_update',
      'welcome',
      'goal_achieved',
      'budget_exceeded',
      'monthly_report'
    ])
    .withMessage('Valid notification type is required'),
  body('title')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Title must be between 1 and 255 characters'),
  body('message')
    .trim()
    .isLength({ min: 1, max: 2000 })
    .withMessage('Message must be between 1 and 2000 characters'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Priority must be low, medium, high, or urgent'),
  body('expiresAt')
    .optional()
    .isISO8601()
    .withMessage('Expires at must be a valid date'),
  body('actionUrl')
    .optional()
    .isURL()
    .withMessage('Action URL must be a valid URL'),
  body('actionText')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Action text must be 100 characters or less')
];

const validateGetNotifications = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('includeRead')
    .optional()
    .isBoolean()
    .withMessage('Include read must be a boolean'),
  query('type')
    .optional()
    .isIn([
      'payment_success',
      'payment_failed', 
      'subscription_expiring',
      'subscription_expired',
      'usage_limit_reached',
      'usage_limit_warning',
      'system_maintenance',
      'security_alert',
      'feature_update',
      'welcome',
      'goal_achieved',
      'budget_exceeded',
      'monthly_report'
    ])
    .withMessage('Invalid notification type')
];

/**
 * @route   GET /api/notifications
 * @desc    Get user notifications with pagination
 * @access  Private
 * @query   page: number (optional, default: 1)
 * @query   limit: number (optional, default: 20, max: 100)
 * @query   includeRead: boolean (optional, default: true)
 * @query   type: string (optional, filter by notification type)
 */
router.get('/', validateGetNotifications, getNotifications);

/**
 * @route   GET /api/notifications/unread-count
 * @desc    Get count of unread notifications
 * @access  Private
 */
router.get('/unread-count', getUnreadCount);

/**
 * @route   GET /api/notifications/stats
 * @desc    Get notification statistics
 * @access  Private
 */
router.get('/stats', getNotificationStats);

/**
 * @route   PUT /api/notifications/:notificationId/read
 * @desc    Mark specific notification as read
 * @access  Private
 */
router.put('/:notificationId/read', validateNotificationId, markAsRead);

/**
 * @route   PUT /api/notifications/mark-all-read
 * @desc    Mark all notifications as read
 * @access  Private
 */
router.put('/mark-all-read', markAllAsRead);

/**
 * @route   POST /api/notifications
 * @desc    Create a new notification (admin/system use)
 * @access  Private
 * @body    userId: string (required)
 * @body    type: string (required)
 * @body    title: string (required)
 * @body    message: string (required)
 * @body    data: object (optional)
 * @body    priority: string (optional, default: medium)
 * @body    expiresAt: string (optional, ISO date)
 * @body    actionUrl: string (optional)
 * @body    actionText: string (optional)
 */
router.post('/', validateCreateNotification, createNotification);

/**
 * @route   DELETE /api/notifications/:notificationId
 * @desc    Delete a notification
 * @access  Private
 */
router.delete('/:notificationId', validateNotificationId, deleteNotification);

module.exports = router;
