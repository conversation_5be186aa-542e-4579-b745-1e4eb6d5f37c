const express = require('express');
const { body } = require('express-validator');
const {
  facebookAuth,
  linkFacebookAccount,
  unlinkFacebookAccount,
  getFacebookUserInfo,
  verifyFacebookToken
} = require('../controllers/facebookAuthController');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Validation middleware
const validateAccessToken = [
  body('accessToken')
    .notEmpty()
    .withMessage('Facebook access token is required')
    .isLength({ min: 10 })
    .withMessage('Invalid access token format')
];

// Routes

/**
 * @route   POST /api/auth/facebook
 * @desc    Login or Register with Facebook
 * @access  Public
 */
router.post('/facebook', [
  ...validateAccessToken
], facebookAuth);

/**
 * @route   POST /api/auth/facebook/link
 * @desc    Link Facebook account to existing user
 * @access  Private
 */
router.post('/facebook/link', [
  authenticateToken,
  ...validateAccessToken
], linkFacebookAccount);

/**
 * @route   DELETE /api/auth/facebook/unlink
 * @desc    Unlink Facebook account from user
 * @access  Private
 */
router.delete('/facebook/unlink', [
  authenticateToken
], unlinkFacebookAccount);

/**
 * @route   POST /api/auth/facebook/user-info
 * @desc    Get Facebook user information
 * @access  Public
 */
router.post('/facebook/user-info', [
  ...validateAccessToken
], getFacebookUserInfo);

/**
 * @route   POST /api/auth/facebook/verify-token
 * @desc    Verify Facebook access token
 * @access  Public
 */
router.post('/facebook/verify-token', [
  ...validateAccessToken
], verifyFacebookToken);

module.exports = router;
