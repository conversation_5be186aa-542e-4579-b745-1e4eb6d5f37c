const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const crypto = require("crypto");
const { validationResult } = require("express-validator");
const {
  User,
  AuthRefreshToken,
  PasswordResetToken,
  sequelize,
} = require("../models");
const { Op } = require("sequelize");
const otpService = require("../services/otpService");
const emailService = require("../services/emailService");
const smsService = require("../services/smsService");

// Generate JWT tokens
const generateTokens = (userId) => {
  const accessToken = jwt.sign({ userId }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || "24h",
  });

  const refreshToken = jwt.sign({ userId }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || "7d",
  });

  return { accessToken, refreshToken };
};

// @desc    Register a new user
// @route   POST /api/auth/register
// @access  Public
const register = async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        errors: errors.array(),
      });
    }

    const {
      name,
      email,
      phone,
      password,
      preferred_language,
      preferred_currency,
    } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({
      where: {
        [Op.or]: [{ email }, ...(phone ? [{ phone }] : [])],
      },
    });

    if (existingUser) {
      return res.status(409).json({
        success: false,
        error: "User already exists with this email or phone number",
      });
    }

    // Create new user
    const user = await User.create({
      name,
      email,
      phone,
      password_hash: password, // Will be hashed by the model hook
      preferred_language: preferred_language || "en",
      preferred_currency: preferred_currency || "USD",
    });

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user.id);

    // Store refresh token in database
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days

    await AuthRefreshToken.create({
      user_id: user.id,
      token_hash: refreshToken,
      expires_at: expiresAt,
      ip_address: req.ip,
      user_agent: req.get("User-Agent"),
    });

    res.status(201).json({
      success: true,
      message: "User registered successfully",
      data: {
        user: user.toJSON(),
        tokens: {
          accessToken,
          refreshToken,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

// Helper utilities for identifier normalization
const isEmailIdentifier = (val = "") => /@/.test(val);
const canonicalizePhone = (raw) => {
  if (!raw) return raw;
  // digits only
  let digits = raw.replace(/\D/g, "");
  if (digits.startsWith("855")) {
    // remove any accidental leading zero after country code
    const after = digits.slice(3);
    if (after.startsWith("0")) digits = "855" + after.slice(1);
  } else if (digits.startsWith("0")) {
    digits = "855" + digits.slice(1);
  } else if (!digits.startsWith("855")) {
    digits = "855" + digits;
  }
  return "+" + digits; // E.164 style
};

const buildPhoneVariants = (raw) => {
  const variants = new Set();
  if (!raw) return [];
  const cleaned = raw.replace(/\s+/g, "");
  variants.add(cleaned);
  const canonical = canonicalizePhone(raw); // +855...
  variants.add(canonical);
  variants.add(canonical.replace(/^\+/, "")); // 855...
  // Local style starting with 0
  const digits = raw.replace(/\D/g, "");
  let local = digits;
  if (digits.startsWith("855")) {
    local = "0" + digits.slice(3);
  } else if (!digits.startsWith("0")) {
    // If user typed 969..., local with 0 prefix
    local = "0" + digits;
  }
  variants.add(local);
  return Array.from(variants);
};

// @desc    Login user
// @route   POST /api/auth/login
// @access  Public
const login = async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        errors: errors.array(),
      });
    }

    let rawIdentifier = req.body.identifier || req.body.email || req.body.phone;
    if (!rawIdentifier) {
      return res.status(400).json({ success: false, error: "Identifier is required" });
    }
    rawIdentifier = rawIdentifier.trim();
    const password = req.body.password;

    // Determine search field & normalize phones
    let user;
    if (isEmailIdentifier(rawIdentifier)) {
      user = await User.findOne({ where: { email: rawIdentifier.toLowerCase() } });
    } else {
      const variants = buildPhoneVariants(rawIdentifier);
      user = await User.findOne({ where: { [Op.or]: variants.map((v) => ({ phone: v })) } });
    }

    if (!user || !(await user.validatePassword(password))) {
      return res.status(401).json({
        success: false,
        error: "Invalid credentials",
      });
    }

    if (!user.is_active) {
      return res.status(401).json({
        success: false,
        error: "Account is deactivated. Please contact support.",
      });
    }

    // Update last login
    await user.update({ last_login: new Date() });

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user.id);

    // Store refresh token in database
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days

    await AuthRefreshToken.create({
      user_id: user.id,
      token_hash: refreshToken,
      expires_at: expiresAt,
      ip_address: req.ip,
      user_agent: req.get("User-Agent"),
    });

    res.json({
      success: true,
      message: "Login successful",
      data: {
        user: user.toJSON(),
        tokens: {
          accessToken,
          refreshToken,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Refresh access token
// @route   POST /api/auth/refresh
// @access  Public (requires refresh token)
const refreshToken = async (req, res, next) => {
  try {
    const user = req.user;
    const oldTokenRecord = req.refreshTokenRecord;

    // Generate new tokens
    const { accessToken, refreshToken: newRefreshToken } = generateTokens(
      user.id
    );

    // Revoke old refresh token
    await oldTokenRecord.update({ is_revoked: true });

    // Store new refresh token
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days

    await AuthRefreshToken.create({
      user_id: user.id,
      token_hash: newRefreshToken,
      expires_at: expiresAt,
      ip_address: req.ip,
      user_agent: req.get("User-Agent"),
    });

    res.json({
      success: true,
      message: "Token refreshed successfully",
      data: {
        tokens: {
          accessToken,
          refreshToken: newRefreshToken,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Logout user
// @route   POST /api/auth/logout
// @access  Public
const logout = async (req, res, next) => {
  try {
    const { refreshToken } = req.body;

    if (refreshToken) {
      // Revoke the refresh token
      await AuthRefreshToken.update(
        { is_revoked: true },
        { where: { token_hash: refreshToken } }
      );
    }

    res.json({
      success: true,
      message: "Logout successful",
    });
  } catch (error) {
    next(error);
  }
};

// Enhanced forgot password - Send BOTH OTP and direct reset link in one email/SMS
// Security note: Providing both reduces friction but slightly lowers security vs two-step flow.
// If you want stricter flow, set process.env.PASSWORD_RESET_TWO_STEP='true' to fallback to old behavior.
const forgotPassword = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    let identifier = req.body.identifier || req.body.email || req.body.phone;
    if (!identifier) {
      return res.status(400).json({ success: false, error: "Identifier (email or phone) is required" });
    }
    identifier = identifier.trim();
    const isEmail = isEmailIdentifier(identifier);
    // Normalize phone for lookup & OTP key consistency
    let canonicalIdentifier = identifier;
    let phoneVariants = [];
    if (!isEmail) {
      canonicalIdentifier = canonicalizePhone(identifier);
      phoneVariants = buildPhoneVariants(identifier);
    }
    // Check if user exists using variants for phone
    const user = await User.findOne({
      where: isEmail
        ? { email: identifier.toLowerCase() }
        : { [Op.or]: phoneVariants.map((v) => ({ phone: v })) },
    });
    if (!user) {
      return res.status(404).json({
        success: false,
        error: "User not found",
      });
    }

    const twoStep = process.env.PASSWORD_RESET_TWO_STEP === 'true';

    if (twoStep) {
      console.log("� Sending OTP (two-step mode) to:", identifier);
      const otpResult = await otpService.sendOTP(
        identifier,
        "password_reset",
        isEmail ? "email" : "sms"
      );
      if (!otpResult.success) throw new Error(otpResult.error);
      console.log("✅ OTP sent successfully to:", identifier);
      return res.json({
        success: true,
        message: "OTP sent. Please enter the OTP to continue.",
        data: {
          method: isEmail ? "email" : "sms",
          identifier,
          expiresIn: parseInt(process.env.OTP_EXPIRY_MINUTES) * 60 || 120,
          mode: "two_step",
        },
      });
    }

  console.log("🔐 Generating OTP + reset token for:", isEmail ? identifier : canonicalIdentifier);

    // Single-step enhanced flow: create OTP and reset token together
    // 1. Generate OTP and store manually (without sending separate OTP email)
  const otp = otpService.generateOTP();
  // Use canonicalIdentifier for phone to ensure consistent key
  const otpKeyIdentifier = isEmail ? identifier : canonicalIdentifier;
  const key = otpService.generateOTPKey(otpKeyIdentifier, "password_reset");
    const expiresAt = Date.now() + otpService.otpExpiry;
    otpService.otpStorage.set(key, {
  otp,
      expiresAt,
      attempts: 0,
      maxAttempts: otpService.maxAttempts,
  type: "password_reset",
  identifier: otpKeyIdentifier,
  method: isEmail ? "email" : "sms",
      createdAt: Date.now()
    });

    // 2. Create reset token and store hashed in DB (reuse logic from verifyPasswordResetOTP)
    const resetToken = crypto.randomBytes(32).toString("hex");
    const tokenHash = crypto.createHash("sha256").update(resetToken).digest("hex");
    const resetExpiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour reset link validity

    // Remove existing tokens
    await PasswordResetToken.destroy({ where: { user_id: user.id } });

    await PasswordResetToken.create({
      user_id: user.id,
      token_hash: tokenHash,
      expires_at: resetExpiresAt,
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    // 3. Send combined email (OTP + reset link)
    if (isEmail) {
      await emailService.sendPasswordResetEmail(identifier.toLowerCase(), otp, resetToken);
      console.log(
        "✅ Combined password reset email sent (OTP + Link) to:",
        identifier
      );
    } else if (process.env.ENABLE_SMS === "true") {
      const frontendUrl = process.env.FRONTEND_URL || "http://localhost:5173";
      const resetLink = `${frontendUrl}/reset-password?token=${resetToken}`;
      const normalizedPhone = canonicalIdentifier;
      await smsService.sendOTP(normalizedPhone, otp, "password_reset", {
        combined: true,
        resetLink,
      });
      console.log(
        "✅ Combined password reset SMS sent (OTP + Link) to:",
        normalizedPhone
      );
    }

    res.json({
      success: true,
      message:
        "We sent you a reset link and verification code (OTP). Use either method to continue.",
      data: {
        method: isEmail ? "email" : "sms",
        identifier,
        otpExpiresIn: Math.floor(otpService.otpExpiry / 1000),
        resetLinkExpiresIn: 3600,
        mode: "combined",
        // For security we DO NOT return resetToken here; user obtains it via email link
      }
    });
  } catch (error) {
    console.error("Forgot password error:", error);
    res.status(500).json({
      success: false,
      error: error.message || "Failed to send OTP",
    });
  }
};

// Step 2: Verify OTP and generate reset token
const verifyPasswordResetOTP = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    let identifier = req.body.identifier || req.body.email || req.body.phone;
    if (!identifier) {
      return res.status(400).json({ success: false, error: "Identifier (email or phone) is required" });
    }
    identifier = identifier.trim();
    const isEmail = isEmailIdentifier(identifier);
    const canonicalIdentifier = isEmail ? identifier.toLowerCase() : canonicalizePhone(identifier);
    const otp = req.body.otp;
    console.log("🔍 Verifying OTP for:", canonicalIdentifier, "OTP:", otp);

    const otpResult = await otpService.verifyOTP(
      canonicalIdentifier,
      otp,
      "password_reset"
    );

    if (!otpResult.success) {
      console.log("❌ OTP verification failed:", otpResult.error);
      return res.status(400).json({
        success: false,
        error: otpResult.error || "Invalid or expired OTP",
      });
    }

    // Find user by identifier
    let user;
    if (isEmail) {
      user = await User.findOne({ where: { email: canonicalIdentifier } });
    } else {
      const variants = buildPhoneVariants(identifier);
      user = await User.findOne({ where: { [Op.or]: variants.map((v) => ({ phone: v })) } });
    }
    if (!user) {
      return res.status(404).json({
        success: false,
        error: "User not found",
      });
    }

    // Generate a temporary reset token for this verified session
    const resetToken = crypto.randomBytes(32).toString("hex");
    const tokenHash = crypto
      .createHash("sha256")
      .update(resetToken)
      .digest("hex");

    // Store reset token in database (short expiry - 10 minutes)
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Remove any existing reset tokens for this user
    await PasswordResetToken.destroy({
      where: {
        user_id: user.id,
      },
    });

    // Create new reset token
    await PasswordResetToken.create({
      user_id: user.id,
      token_hash: tokenHash,
      expires_at: expiresAt,
      ip_address: req.ip,
      user_agent: req.get("User-Agent"),
    });

    console.log("✅ OTP verified successfully, reset token generated");

    res.json({
      success: true,
      message: "OTP verified successfully. You can now reset your password.",
      data: {
        resetToken,
        expiresIn: 600, // 10 minutes in seconds
      },
    });
  } catch (error) {
    console.error("Verify OTP error:", error);
    res.status(500).json({
      success: false,
      error: error.message || "Failed to verify OTP",
    });
  }
};

// Step 3: Reset password with verified token
const resetPassword = async (req, res, next) => {
  try {
    console.log("📝 Reset password request:", {
      hasToken: !!req.body.token,
      body: req.body,
    });

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log("❌ Validation errors:", errors.array());
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { password, token } = req.body;

    if (!password) {
      return res.status(400).json({
        success: false,
        error: "New password is required",
      });
    }

    if (!token) {
      return res.status(400).json({
        success: false,
        error: "Reset token is required. Please verify OTP first.",
      });
    }

    // Verify reset token
    console.log("🔑 Using token-based reset");
    const tokenHash = crypto.createHash("sha256").update(token).digest("hex");
    console.log(
      "🔍 Looking for token hash:",
      tokenHash.substring(0, 8) + "..."
    );

    const resetTokenRecord = await PasswordResetToken.findOne({
      where: {
        token_hash: tokenHash,
        used: false,
        expires_at: { [Op.gt]: new Date() },
      },
      include: [
        {
          model: User,
          as: "user",
        },
      ],
    });

    console.log("🔍 Found reset token:", !!resetTokenRecord);
    if (resetTokenRecord) {
      console.log("🔍 Token expires at:", resetTokenRecord.expires_at);
      console.log("🔍 Current time:", new Date());
      console.log("🔍 Token used:", resetTokenRecord.used);
    }

    if (!resetTokenRecord) {
      console.log("❌ Invalid or expired reset token");
      return res.status(400).json({
        success: false,
        error: "Invalid or expired reset token. Please request a new OTP.",
      });
    }

    const user = resetTokenRecord.user;
    console.log("✅ Found user via token:", user.email);

    // Mark token as used
    await resetTokenRecord.update({
      used: true,
      used_at: new Date(),
    });

    // Hash new password
    console.log("🔐 Hashing new password...");
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Update user password using raw SQL to avoid hooks
    console.log("💾 Updating user password...");
    await sequelize.query(
      "UPDATE users SET password_hash = ?, updated_at = ? WHERE id = ?",
      {
        replacements: [hashedPassword, new Date(), user.id],
        type: sequelize.QueryTypes.UPDATE,
      }
    );

    // Revoke all existing refresh tokens for security
    console.log("🔒 Revoking existing refresh tokens...");
    await AuthRefreshToken.update(
      { is_revoked: true, updated_at: new Date() },
      { where: { user_id: user.id, is_revoked: false } }
    );

    // Clean up any remaining reset tokens for this user
    console.log("🧹 Cleaning up reset tokens...");
    await PasswordResetToken.update(
      { used: true, used_at: new Date() },
      { where: { user_id: user.id, used: false } }
    );

    console.log("✅ Password reset successfully completed");
    console.log("💳 Payment/Reset Details:");
    console.log(`   User: ${user.email} (ID: ${user.id})`);
    console.log(`   Phone: ${user.phone || 'N/A'}`);
    console.log(`   Reset Time: ${new Date().toISOString()}`);
    console.log(`   IP Address: ${req.ip}`);
    console.log(`   User Agent: ${req.get('User-Agent') || 'Unknown'}`);
    console.log("🔄 All security tokens revoked and password updated successfully");
    
    res.json({
      success: true,
      message:
        "Password reset successfully. Please login with your new password.",
      data: {
        resetMethod: "token",
      },
    });
  } catch (error) {
    console.error("Reset password error:", error);
    res.status(500).json({
      success: false,
      error: error.message || "Failed to reset password",
    });
  }
};

const verifyEmail = async (req, res, next) => {
  res.json({ success: true, message: "Verify email endpoint - coming soon" });
};

const resendVerification = async (req, res, next) => {
  res.json({
    success: true,
    message: "Resend verification endpoint - coming soon",
  });
};

const googleCallback = async (req, res, next) => {
  try {
    // At this point, passport has authenticated the user and attached it to req.user
    const user = req.user;

    if (!user) {
      console.error("Google OAuth: No user found in request");
      const frontendUrl = process.env.FRONTEND_URL || "http://localhost:5173";
      return res.redirect(`${frontendUrl}/login?error=authentication_failed`);
    }

    // Generate JWT tokens
    const { accessToken, refreshToken } = generateTokens(user.id);

    // Store refresh token in database
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days

    await AuthRefreshToken.create({
      user_id: user.id,
      token_hash: refreshToken,
      expires_at: expiresAt,
      ip_address: req.ip,
      user_agent: req.get("User-Agent"),
    });

    // Update last login
    user.last_login = new Date();
    await user.save();

    // Redirect to frontend with tokens (use port 5173)
    const frontendUrl = process.env.FRONTEND_URL || "http://localhost:5173";
    const redirectUrl = `${frontendUrl}/auth/callback?token=${accessToken}&refresh=${refreshToken}`;
    console.log("Google OAuth success: Redirecting to", redirectUrl);
    res.redirect(redirectUrl);
  } catch (error) {
    console.error("Google OAuth callback error:", error);
    const frontendUrl = process.env.FRONTEND_URL || "http://localhost:5173";
    res.redirect(`${frontendUrl}/login?error=oauth_callback_failed`);
  }
};

const facebookAuth = async (req, res, next) => {
  res.json({ success: true, message: "Facebook OAuth endpoint - coming soon" });
};

const facebookCallback = async (req, res, next) => {
  res.json({
    success: true,
    message: "Facebook OAuth callback endpoint - coming soon",
  });
};

/**
 * Get user profile (alias for users controller)
 */
const getProfile = async (req, res) => {
  try {
    const userId = req.user.id;

    const user = await User.findByPk(userId, {
      attributes: [
        'id', 'name', 'email', 'phone', 'preferred_language',
        'preferred_currency', 'profile_picture', 'role', 'created_at',
        'bio', 'location', 'website', 'telegram_id', 'telegram_username'
      ]
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          phone: user.phone,
          preferred_language: user.preferred_language || 'en',
          preferred_currency: user.preferred_currency || 'USD',
          profile_picture: user.profile_picture,
          role: user.role,
          bio: user.bio,
          location: user.location,
          website: user.website,
          telegram_id: user.telegram_id,
          telegram_username: user.telegram_username,
          created_at: user.created_at
        }
      }
    });

  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get user profile'
    });
  }
};

/**
 * Update user profile (alias for users controller)
 */
const updateProfile = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      name,
      phone,
      preferred_language,
      preferred_currency,
      bio,
      location,
      website,
      profile_picture
    } = req.body;

    // Validate input
    if (!name || name.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Name is required'
      });
    }

    // Update user profile
    const [updatedRows] = await User.update({
      name: name.trim(),
      phone: phone || null,
      preferred_language: preferred_language || 'en',
      preferred_currency: preferred_currency || 'USD',
      bio: bio || null,
      location: location || null,
      website: website || null,
      profile_picture: profile_picture || null
    }, {
      where: { id: userId }
    });

    if (updatedRows === 0) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Fetch updated user data
    const updatedUser = await User.findByPk(userId, {
      attributes: [
        'id', 'name', 'email', 'phone', 'preferred_language',
        'preferred_currency', 'profile_picture', 'role', 'created_at',
        'bio', 'location', 'website', 'telegram_id', 'telegram_username'
      ]
    });

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: {
        user: {
          id: updatedUser.id,
          name: updatedUser.name,
          email: updatedUser.email,
          phone: updatedUser.phone,
          preferred_language: updatedUser.preferred_language,
          preferred_currency: updatedUser.preferred_currency,
          profile_picture: updatedUser.profile_picture,
          role: updatedUser.role,
          bio: updatedUser.bio,
          location: updatedUser.location,
          website: updatedUser.website,
          telegram_id: updatedUser.telegram_id,
          telegram_username: updatedUser.telegram_username,
          created_at: updatedUser.created_at
        }
      }
    });

  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update user profile'
    });
  }
};

module.exports = {
  register,
  login,
  refreshToken,
  logout,
  forgotPassword,
  verifyPasswordResetOTP,
  resetPassword,
  verifyEmail,
  resendVerificationEmail: resendVerification,
  googleCallback,
  facebookAuth,
  facebookCallback,
  getProfile,
  updateProfile,
};
