# CI/CD Pipeline Setup Guide

This guide explains how to set up automated deployment pipelines for your Finwise application using GitHub Actions.

## 🚀 Overview

Your CI/CD pipeline includes:
- **Continuous Integration**: Automated testing and code quality checks
- **Frontend Deployment**: Automated deployment to Netlify
- **Backend Deployment**: Automated deployment to Digital Ocean
- **Quality Assurance**: Lighthouse performance testing and security audits

## 📋 Required GitHub Secrets

### For Frontend Deployment (Netlify)
```
NETLIFY_AUTH_TOKEN=your_netlify_auth_token
NETLIFY_SITE_ID=your_netlify_site_id
VITE_API_URL=https://your-backend-url.com/api
VITE_API_BASE_URL=https://your-backend-url.com
VITE_GOOGLE_OAUTH_URL=https://your-backend-url.com/api/auth/google
VITE_FACEBOOK_OAUTH_URL=https://your-backend-url.com/api/auth/facebook
```

### For Backend Deployment (Digital Ocean)
```
DOCKER_USERNAME=your_docker_hub_username
DOCKER_PASSWORD=your_docker_hub_password
DO_HOST=your_digital_ocean_droplet_ip
DO_USERNAME=your_server_username
DO_SSH_KEY=your_private_ssh_key
DO_PORT=22
```

## 🔧 Setup Instructions

### Step 1: Configure GitHub Secrets

1. Go to your GitHub repository
2. Navigate to Settings → Secrets and variables → Actions
3. Add all the required secrets listed above

### Step 2: Get Netlify Credentials

1. **Netlify Auth Token**:
   - Go to Netlify Dashboard → User settings → Applications
   - Generate a new personal access token
   - Copy the token to `NETLIFY_AUTH_TOKEN` secret

2. **Netlify Site ID**:
   - Go to your site in Netlify Dashboard
   - Navigate to Site settings → General
   - Copy the Site ID to `NETLIFY_SITE_ID` secret

### Step 3: Setup Docker Hub

1. Create account at [hub.docker.com](https://hub.docker.com)
2. Create a repository named `finwise-backend`
3. Generate access token in Account Settings → Security
4. Add credentials to GitHub secrets

### Step 4: Configure Digital Ocean Access

1. **SSH Key Setup**:
   ```bash
   # Generate SSH key pair (if you don't have one)
   ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
   
   # Copy public key to your Digital Ocean droplet
   ssh-copy-id username@your-droplet-ip
   
   # Copy private key content to DO_SSH_KEY secret
   cat ~/.ssh/id_rsa
   ```

2. **Prepare Digital Ocean Droplet**:
   ```bash
   # On your droplet, create application directory
   sudo mkdir -p /opt/finwise
   sudo chown $USER:$USER /opt/finwise
   
   # Clone your repository
   cd /opt/finwise
   git clone https://github.com/your-username/finwise.git .
   
   # Setup environment file
   cp Backend/.env.production Backend/.env
   # Edit Backend/.env with your production values
   ```

## 🔄 Workflow Triggers

### Continuous Integration (`ci.yml`)
- **Triggers**: Push to main/develop, Pull requests
- **Actions**: Run tests, linting, security audits, Docker build test

### Frontend Deployment (`deploy-frontend.yml`)
- **Triggers**: Push to main (Frontend changes only)
- **Actions**: Build React app, deploy to Netlify, run Lighthouse tests

### Backend Deployment (`deploy-backend.yml`)
- **Triggers**: Push to main (Backend changes only)
- **Actions**: Run tests, build Docker image, deploy to Digital Ocean

## 📊 Pipeline Features

### Quality Assurance
- ✅ Automated testing (frontend & backend)
- ✅ Code linting and formatting checks
- ✅ Security vulnerability scanning
- ✅ Performance testing with Lighthouse
- ✅ Docker image testing

### Deployment Features
- ✅ Zero-downtime deployments
- ✅ Automatic rollback on failure
- ✅ Health checks after deployment
- ✅ Deployment notifications
- ✅ Environment-specific configurations

### Monitoring & Logging
- ✅ Deployment status notifications
- ✅ Build logs and artifacts
- ✅ Performance metrics
- ✅ Error tracking and alerts

## 🚨 Troubleshooting

### Common Issues

1. **Deployment Fails**:
   - Check GitHub Actions logs
   - Verify all secrets are set correctly
   - Ensure Digital Ocean droplet is accessible

2. **Tests Fail**:
   - Check test database configuration
   - Verify environment variables
   - Review test logs in Actions tab

3. **Docker Build Fails**:
   - Check Dockerfile syntax
   - Verify all dependencies are listed
   - Check for missing files

### Debug Commands

```bash
# Check deployment status on Digital Ocean
ssh username@your-droplet-ip
docker ps
docker logs finwise-backend

# Test health endpoint
curl http://your-droplet-ip:5003/api/health

# View GitHub Actions logs
# Go to GitHub → Actions tab → Select failed workflow
```

## 📈 Monitoring Your Deployments

### Health Checks
- Frontend: Automatic via Netlify
- Backend: `https://your-backend-url/api/health`

### Performance Monitoring
- Lighthouse scores in GitHub Actions
- Netlify Analytics (if enabled)
- Custom monitoring scripts in `/scripts/`

### Log Management
- GitHub Actions logs (30 days retention)
- Docker container logs on Digital Ocean
- Application logs in `/opt/finwise/logs/`

## 🔄 Manual Deployment

If you need to deploy manually:

```bash
# Frontend (from local machine)
cd Frontend
npm run build
# Deploy dist/ folder to Netlify manually

# Backend (on Digital Ocean droplet)
cd /opt/finwise
./update-digital-ocean.sh
```

## 🎯 Next Steps

1. **Test the Pipeline**: Make a small change and push to main
2. **Monitor Deployments**: Check GitHub Actions for successful runs
3. **Setup Monitoring**: Configure alerts for failed deployments
4. **Optimize Performance**: Review Lighthouse reports and optimize
5. **Security**: Regularly update dependencies and rotate secrets

Your CI/CD pipeline is now ready! 🚀
