const { Goal } = require('../models');
const { validationResult } = require('express-validator');
const { Op } = require('sequelize');

// Get all goals for a user
const getGoals = async (req, res) => {
  try {
    const userId = req.user.id;
    const { active = 'true', goal_type, completed } = req.query;

    const whereClause = { user_id: userId };
    
    if (active !== 'all') {
      whereClause.is_active = active === 'true';
    }
    
    if (goal_type) {
      whereClause.goal_type = goal_type;
    }

    if (completed !== undefined) {
      whereClause.is_completed = completed === 'true';
    }

    const goals = await Goal.findAll({
      where: whereClause,
      order: [['priority', 'DESC'], ['created_at', 'DESC']]
    });

    // Calculate progress for each goal
    const goalsWithProgress = goals.map(goal => {
      const goalData = goal.toJSON();
      
      goalData.progress_percentage = Math.min(
        (parseFloat(goal.current_amount) / parseFloat(goal.target_amount)) * 100,
        100
      );
      goalData.remaining_amount = parseFloat(goal.target_amount) - parseFloat(goal.current_amount);
      goalData.is_achieved = parseFloat(goal.current_amount) >= parseFloat(goal.target_amount);
      
      // Calculate estimated completion date based on monthly contribution
      if (goal.monthly_contribution && goal.monthly_contribution > 0 && goalData.remaining_amount > 0) {
        const monthsToComplete = Math.ceil(goalData.remaining_amount / parseFloat(goal.monthly_contribution));
        const estimatedDate = new Date();
        estimatedDate.setMonth(estimatedDate.getMonth() + monthsToComplete);
        goalData.estimated_completion_date = estimatedDate.toISOString().split('T')[0];
      }

      return goalData;
    });

    res.json({
      success: true,
      data: goalsWithProgress
    });
  } catch (error) {
    console.error('Get goals error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch goals'
    });
  }
};

// Get a single goal
const getGoal = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    const goal = await Goal.findOne({
      where: { id, user_id: userId }
    });

    if (!goal) {
      return res.status(404).json({
        success: false,
        error: 'Goal not found'
      });
    }

    const goalData = goal.toJSON();
    
    goalData.progress_percentage = Math.min(
      (parseFloat(goal.current_amount) / parseFloat(goal.target_amount)) * 100,
      100
    );
    goalData.remaining_amount = parseFloat(goal.target_amount) - parseFloat(goal.current_amount);
    goalData.is_achieved = parseFloat(goal.current_amount) >= parseFloat(goal.target_amount);
    
    // Calculate estimated completion date
    if (goal.monthly_contribution && goal.monthly_contribution > 0 && goalData.remaining_amount > 0) {
      const monthsToComplete = Math.ceil(goalData.remaining_amount / parseFloat(goal.monthly_contribution));
      const estimatedDate = new Date();
      estimatedDate.setMonth(estimatedDate.getMonth() + monthsToComplete);
      goalData.estimated_completion_date = estimatedDate.toISOString().split('T')[0];
    }

    res.json({
      success: true,
      data: goalData
    });
  } catch (error) {
    console.error('Get goal error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch goal'
    });
  }
};

// Create a new goal
const createGoal = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const userId = req.user.id;
    const {
      name,
      name_km,
      description,
      description_km,
      target_amount,
      current_amount = 0,
      currency = 'USD',
      goal_type = 'saving',
      target_date,
      start_date,
      monthly_contribution,
      priority = 'medium',
      icon,
      color,
      notes,
      milestones,
      alert_settings
    } = req.body;

    const goal = await Goal.create({
      user_id: userId,
      name,
      name_km,
      description,
      description_km,
      target_amount,
      current_amount,
      currency,
      goal_type,
      target_date,
      start_date: start_date || new Date().toISOString().split('T')[0],
      monthly_contribution,
      priority,
      is_active: true,
      is_completed: false,
      completed_at: null,
      icon,
      color,
      notes,
      milestones,
      alert_settings
    });

    res.status(201).json({
      success: true,
      message: 'Goal created successfully',
      data: goal
    });
  } catch (error) {
    console.error('Create goal error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create goal'
    });
  }
};

// Update a goal
const updateGoal = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const userId = req.user.id;
    const { id } = req.params;

    const goal = await Goal.findOne({
      where: { id, user_id: userId }
    });

    if (!goal) {
      return res.status(404).json({
        success: false,
        error: 'Goal not found'
      });
    }

    const updateData = req.body;
    
    // Check if goal is being marked as completed
    if (updateData.current_amount && parseFloat(updateData.current_amount) >= parseFloat(goal.target_amount)) {
      updateData.is_completed = true;
      updateData.completed_at = new Date();
    }

    await goal.update(updateData);

    res.json({
      success: true,
      message: 'Goal updated successfully',
      data: goal
    });
  } catch (error) {
    console.error('Update goal error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update goal'
    });
  }
};

// Update goal progress (add/subtract amount)
const updateGoalProgress = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const userId = req.user.id;
    const { id } = req.params;
    const { amount, operation = 'add', notes } = req.body;

    const goal = await Goal.findOne({
      where: { id, user_id: userId }
    });

    if (!goal) {
      return res.status(404).json({
        success: false,
        error: 'Goal not found'
      });
    }

    let newCurrentAmount = parseFloat(goal.current_amount);
    
    if (operation === 'add') {
      newCurrentAmount += parseFloat(amount);
    } else if (operation === 'subtract') {
      newCurrentAmount = Math.max(0, newCurrentAmount - parseFloat(amount));
    } else {
      return res.status(400).json({
        success: false,
        error: 'Invalid operation. Use "add" or "subtract"'
      });
    }

    const updateData = {
      current_amount: newCurrentAmount,
      notes: notes || goal.notes
    };

    // Check if goal is achieved
    if (newCurrentAmount >= parseFloat(goal.target_amount) && !goal.is_completed) {
      updateData.is_completed = true;
      updateData.completed_at = new Date();
    }

    await goal.update(updateData);

    const goalData = goal.toJSON();
    goalData.progress_percentage = Math.min(
      (newCurrentAmount / parseFloat(goal.target_amount)) * 100,
      100
    );
    goalData.remaining_amount = parseFloat(goal.target_amount) - newCurrentAmount;
    goalData.is_achieved = newCurrentAmount >= parseFloat(goal.target_amount);

    res.json({
      success: true,
      message: 'Goal progress updated successfully',
      data: goalData
    });
  } catch (error) {
    console.error('Update goal progress error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update goal progress'
    });
  }
};

// Delete a goal
const deleteGoal = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    const goal = await Goal.findOne({
      where: { id, user_id: userId }
    });

    if (!goal) {
      return res.status(404).json({
        success: false,
        error: 'Goal not found'
      });
    }

    await goal.destroy();

    res.json({
      success: true,
      message: 'Goal deleted successfully'
    });
  } catch (error) {
    console.error('Delete goal error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete goal'
    });
  }
};

module.exports = {
  getGoals,
  getGoal,
  createGoal,
  updateGoal,
  updateGoalProgress,
  deleteGoal
};
