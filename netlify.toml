[build]
  # Build is executed from the Frontend subdirectory. When a base directory is set,
  # the publish path must be relative to that base. Previously this was set to
  # "Frontend/dist" which made Netlify look for Frontend/Frontend/dist.
  base = "Frontend"
  publish = "dist"
  command = "npm install --legacy-peer-deps && npm run build"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "9"
  CI = "false"

# API proxy redirects
[[redirects]]
  from = "/api/*"
  to = "https://seal-app-mvbaj.ondigitalocean.app/api/:splat"
  status = 200
  force = true

# SPA fallback - this should catch all non-API routes
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[context.production.environment]
  VITE_API_URL = "https://seal-app-mvbaj.ondigitalocean.app/api"
  VITE_API_BASE_URL = "https://seal-app-mvbaj.ondigitalocean.app"
  VITE_NODE_ENV = "production"

[context.deploy-preview.environment]
  VITE_API_URL = "https://seal-app-mvbaj.ondigitalocean.app/api"
  VITE_API_BASE_URL = "https://seal-app-mvbaj.ondigitalocean.app"
  VITE_NODE_ENV = "staging"

[context.branch-deploy.environment]
  VITE_API_URL = "https://seal-app-mvbaj.ondigitalocean.app/api"
  VITE_API_BASE_URL = "https://seal-app-mvbaj.ondigitalocean.app"
  VITE_NODE_ENV = "development"

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
  # CSP updated to allow Google Fonts (styles + font files) and Facebook SDK, plus backend API
  # Consider tightening 'unsafe-inline' later by using hashed / nonce scripts and styles.
  Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://connect.facebook.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' data: https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://seal-app-mvbaj.ondigitalocean.app https://graph.facebook.com; frame-src https://www.facebook.com;"

# Cache static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Cache fonts
[[headers]]
  for = "/fonts/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
