const { DocumentProcessorServiceClient } = require("@google-cloud/documentai");
const path = require("path");

class DocumentAIService {
  constructor() {
    // Initialize Document AI client using environment variables
    this.client = new DocumentProcessorServiceClient({
      keyFilename:
        process.env.GOOGLE_APPLICATION_CREDENTIALS ||
        path.join(__dirname, "../finwise-471010-0dfebced9164.json"),
      projectId: process.env.DOCUMENT_AI_PROJECT_ID || "finwise-471010",
    });

    // Document AI processor configuration from environment variables
    this.processorConfig = {
      projectId: process.env.DOCUMENT_AI_PROJECT_ID || "finwise-471010",
      location: process.env.DOCUMENT_AI_LOCATION || "eu",
      processorId: process.env.DOCUMENT_AI_PROCESSOR_ID || "a895696dbaeb663f", //Need to include processor ID for finwise-471010
    };

    // Enhanced patterns for receipt parsing
    this.patterns = {
      total: [
        /(?:grand\s+)?total[\s:]*\$?([0-9,]+\.?[0-9]*)/gi,
        /amount\s+due[\s:]*\$?([0-9,]+\.?[0-9]*)/gi,
        /balance[\s:]*\$?([0-9,]+\.?[0-9]*)/gi,
        /sum[\s:]*\$?([0-9,]+\.?[0-9]*)/gi,
        /final\s+amount[\s:]*\$?([0-9,]+\.?[0-9]*)/gi,
      ],
      date: [
        /(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})/g,
        /(\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2})/g,
        /(\d{1,2}\s+(?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\s+\d{2,4})/gi,
        /date[\s:]*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})/gi,
      ],
      merchant: [/^([A-Z\s&]{3,50})$/gm, /^([A-Za-z\s&.,'-]{3,40})$/gm],
      items: [
        /^(.+?)\s+(\$?[0-9,]+\.?[0-9]*)$/gm,
        /(\d+)\s*x\s*(.+?)\s+(\$?[0-9,]+\.?[0-9]*)$/gm,
      ],
      tax: [
        /tax[\s:]*\$?([0-9,]+\.?[0-9]*)/gi,
        /vat[\s:]*\$?([0-9,]+\.?[0-9]*)/gi,
      ],
      subtotal: [/sub[\s\-]?total[\s:]*\$?([0-9,]+\.?[0-9]*)/gi],
    };

    this.currencies = {
      symbols: ["$", "₫", "៛", "RM", "THB", "€", "Kr", "¥"],
      codes: [
        "USD",
        "VND",
        "KHR",
        "MYR",
        "THB",
        "EUR",
        "SEK",
        "NOK",
        "DKK",
        "JPY",
      ],
    };
  }

  /**
   * Process receipt image using Document AI
   * @param {Buffer} imageBuffer - Image buffer
   * @param {string} mimeType - Image MIME type
   * @returns {Object} Processed receipt data
   */
  async processReceipt(imageBuffer, mimeType = "image/jpeg") {
    try {
      console.log("🤖 Starting Document AI processing...");

      // Configure the processor name
      const name = `projects/${this.processorConfig.projectId}/locations/${this.processorConfig.location}/processors/${this.processorConfig.processorId}`;

      // Prepare the request
      const request = {
        name,
        rawDocument: {
          content: imageBuffer.toString("base64"),
          mimeType: mimeType,
        },
      };

      // Process the document
      const [result] = await this.client.processDocument(request);
      const { document } = result;

      if (!document) {
        throw new Error("No document returned from Document AI");
      }

      console.log("✅ Document AI processing completed");

      // Extract structured data
      const extractedData = this.extractReceiptData(document);

      return {
        success: true,
        data: extractedData,
        raw: document,
      };
    } catch (error) {
      console.error("❌ Document AI processing error:", error);

      // Handle specific error cases with detailed messages
      if (error.code === 5) {
        throw new Error(
          `Document AI processor not found. Please check your processor ID: ${this.processorConfig.processorId}`
        );
      } else if (error.code === 7) {
        if (error.details && error.details.includes("billing")) {
          throw new Error(
            `Document AI billing not enabled. Please enable billing for project ${this.processorConfig.projectId} at: https://console.developers.google.com/billing/enable?project=${this.processorConfig.projectId}`
          );
        } else {
          throw new Error(
            `Document AI API not enabled. Please enable the Document AI API for project ${this.processorConfig.projectId}`
          );
        }
      } else if (error.code === 16) {
        throw new Error(
          `Invalid credentials for Document AI. Please check your service account key file: ${process.env.GOOGLE_APPLICATION_CREDENTIALS}`
        );
      } else if (error.code === 3) {
        throw new Error(
          "Invalid request to Document AI. Please check your image format and size."
        );
      }

      throw error;
    }
  }

  /**
   * Extract structured receipt data from Document AI response
   * @param {Object} document - Document AI document object
   * @returns {Object} Structured receipt data
   */
  extractReceiptData(document) {
    const result = {
      merchant_name: "",
      date: "",
      total_amount: 0,
      subtotal: 0,
      tax_amount: 0,
      currency: "USD",
      items: [],
      confidence: 0,
      raw_text: "",
      structured_data: {},
    };

    // Extract text from document
    if (document.text) {
      result.raw_text = document.text;
    }

    // Process entities (Document AI's structured extraction)
    if (document.entities && document.entities.length > 0) {
      console.log("📊 Processing Document AI entities...");

      for (const entity of document.entities) {
        const entityType = entity.type;
        const entityValue = this.getEntityText(entity, document.text);
        const confidence = entity.confidence || 0;

        console.log(
          `Entity: ${entityType} = ${entityValue} (confidence: ${confidence})`
        );

        switch (entityType) {
          case "supplier_name":
          case "merchant_name":
            if (confidence > 0.5) {
              result.merchant_name = entityValue;
            }
            break;

          case "invoice_date":
          case "receipt_date":
          case "date":
            if (confidence > 0.5) {
              result.date = this.formatDate(entityValue);
            }
            break;

          case "total_amount":
          case "net_amount":
            if (confidence > 0.5) {
              result.total_amount = this.parseAmount(entityValue);
            }
            break;

          case "subtotal_amount":
            if (confidence > 0.5) {
              result.subtotal = this.parseAmount(entityValue);
            }
            break;

          case "total_tax_amount":
          case "tax_amount":
            if (confidence > 0.5) {
              result.tax_amount = this.parseAmount(entityValue);
            }
            break;

          case "currency":
            if (confidence > 0.5) {
              result.currency = entityValue.toUpperCase();
            }
            break;

          case "line_item":
            // Process line items
            this.processLineItem(entity, document.text, result.items);
            break;
        }
      }
    }

    // Fallback: Use pattern matching if entities didn't provide good results
    if (!result.merchant_name || !result.date || !result.total_amount) {
      console.log("🔍 Falling back to pattern matching...");
      const patternResults = this.extractWithPatterns(result.raw_text);

      if (!result.merchant_name && patternResults.merchant_name) {
        result.merchant_name = patternResults.merchant_name;
      }
      if (!result.date && patternResults.date) {
        result.date = patternResults.date;
      }
      if (!result.total_amount && patternResults.total_amount) {
        result.total_amount = patternResults.total_amount;
      }
      if (!result.currency && patternResults.currency) {
        result.currency = patternResults.currency;
      }
    }

    // Calculate overall confidence
    result.confidence = this.calculateConfidence(result);

    // Detect currency from text if not found
    if (!result.currency || result.currency === "USD") {
      result.currency = this.detectCurrency(result.raw_text);
    }

    console.log("📋 Extraction Summary:", {
      merchant: result.merchant_name,
      date: result.date,
      total: result.total_amount,
      currency: result.currency,
      confidence: result.confidence,
    });

    return result;
  }

  /**
   * Extract text content from entity
   */
  getEntityText(entity, documentText) {
    if (entity.mentionText) {
      return entity.mentionText;
    }

    if (entity.textAnchor && entity.textAnchor.textSegments) {
      const segment = entity.textAnchor.textSegments[0];
      if (segment && documentText) {
        const startIndex = parseInt(segment.startIndex) || 0;
        const endIndex = parseInt(segment.endIndex) || documentText.length;
        return documentText.substring(startIndex, endIndex);
      }
    }

    return entity.normalizedValue ? entity.normalizedValue.text : "";
  }

  /**
   * Process line item entity
   */
  processLineItem(entity, documentText, items) {
    const item = {
      description: "",
      quantity: 1,
      unit_price: 0,
      total_price: 0,
    };

    // Extract line item properties
    if (entity.properties) {
      for (const property of entity.properties) {
        const propertyValue = this.getEntityText(property, documentText);

        switch (property.type) {
          case "line_item/description":
            item.description = propertyValue;
            break;
          case "line_item/quantity":
            item.quantity = parseInt(propertyValue) || 1;
            break;
          case "line_item/unit_price":
            item.unit_price = this.parseAmount(propertyValue);
            break;
          case "line_item/amount":
            item.total_price = this.parseAmount(propertyValue);
            break;
        }
      }
    }

    if (item.description) {
      items.push(item);
    }
  }

  /**
   * Extract data using pattern matching (fallback)
   */
  extractWithPatterns(text) {
    const result = {
      merchant_name: "",
      date: "",
      total_amount: 0,
      currency: "USD",
    };

    const lines = text
      .split("\n")
      .map((line) => line.trim())
      .filter((line) => line.length > 0);

    // Extract merchant name (usually first meaningful line)
    for (let i = 0; i < Math.min(5, lines.length); i++) {
      const line = lines[i];
      if (
        line.length > 2 &&
        line.length < 50 &&
        /^[A-Za-z\s&.,'-]+$/.test(line)
      ) {
        result.merchant_name = line;
        break;
      }
    }

    // Extract total amount
    for (const pattern of this.patterns.total) {
      const match = text.match(pattern);
      if (match && match[1]) {
        result.total_amount = this.parseAmount(match[1]);
        break;
      }
    }

    // Extract date
    for (const pattern of this.patterns.date) {
      const match = text.match(pattern);
      if (match && match[1]) {
        result.date = this.formatDate(match[1]);
        break;
      }
    }

    // Detect currency
    result.currency = this.detectCurrency(text);

    return result;
  }

  /**
   * Parse amount from string
   */
  parseAmount(amountStr) {
    if (!amountStr) return 0;

    // Remove currency symbols and clean up
    const cleaned = amountStr
      .toString()
      .replace(/[$₫៛RM€Kr¥]/g, "")
      .replace(/[,\s]/g, "")
      .replace(/[^\d.]/g, "");

    const amount = parseFloat(cleaned);
    return isNaN(amount) ? 0 : amount;
  }

  /**
   * Format date string
   */
  formatDate(dateStr) {
    if (!dateStr) return "";

    try {
      // Handle different date formats
      let parsedDate;

      // Try parsing various formats
      if (dateStr.includes("/")) {
        // Handle MM/DD/YYYY or DD/MM/YYYY
        const parts = dateStr.split("/");
        if (parts.length === 3) {
          // Assume MM/DD/YYYY for US format
          parsedDate = new Date(parts[2], parts[0] - 1, parts[1]);
        }
      } else if (dateStr.includes("-")) {
        // Handle YYYY-MM-DD
        parsedDate = new Date(dateStr);
      } else if (dateStr.match(/\d{1,2}\s+\w+\s+\d{4}/)) {
        // Handle "DD MMM YYYY" format
        parsedDate = new Date(dateStr);
      } else {
        // Try direct parsing
        parsedDate = new Date(dateStr);
      }

      if (!isNaN(parsedDate.getTime())) {
        return parsedDate.toISOString().split("T")[0];
      }
    } catch (error) {
      console.log("Date parsing error:", error);
    }

    return "";
  }

  /**
   * Detect currency from text
   */
  detectCurrency(text) {
    const lowerText = text.toLowerCase();

    // Check for currency indicators
    if (
      text.includes("₫") ||
      lowerText.includes("vnd") ||
      lowerText.includes("dong")
    ) {
      return "VND";
    }
    if (
      text.includes("៛") ||
      lowerText.includes("khr") ||
      lowerText.includes("riel")
    ) {
      return "KHR";
    }
    if (
      text.includes("RM") ||
      lowerText.includes("myr") ||
      lowerText.includes("ringgit")
    ) {
      return "MYR";
    }
    if (lowerText.includes("thb") || lowerText.includes("baht")) {
      return "THB";
    }
    if (
      text.includes("€") ||
      lowerText.includes("eur") ||
      lowerText.includes("euro")
    ) {
      return "EUR";
    }
    if (
      text.includes("Kr") ||
      lowerText.includes("sek") ||
      lowerText.includes("nok") ||
      lowerText.includes("dkk")
    ) {
      return "SEK"; // Default to SEK for Scandinavian currencies
    }
    if (
      text.includes("¥") ||
      lowerText.includes("jpy") ||
      lowerText.includes("yen")
    ) {
      return "JPY";
    }

    return "USD"; // Default
  }

  /**
   * Calculate overall confidence score
   */
  calculateConfidence(result) {
    let score = 0;
    let factors = 0;

    if (result.merchant_name) {
      score += 0.25;
      factors++;
    }
    if (result.date) {
      score += 0.25;
      factors++;
    }
    if (result.total_amount > 0) {
      score += 0.4;
      factors++;
    }
    if (result.currency) {
      score += 0.1;
      factors++;
    }

    return factors > 0 ? Math.round((score / factors) * 100) / 100 : 0;
  }

  /**
   * Create a mock response for testing when Document AI is not available
   */
  createMockResponse(imageBuffer) {
    return {
      success: true,
      data: {
        merchant_name: "Sample Store (Mock)",
        date: new Date().toISOString().split("T")[0],
        total_amount: 25.99,
        subtotal: 23.85,
        tax_amount: 2.14,
        currency: "USD",
        items: [
          {
            description: "Coffee",
            quantity: 1,
            unit_price: 4.5,
            total_price: 4.5,
          },
          {
            description: "Sandwich",
            quantity: 1,
            unit_price: 8.95,
            total_price: 8.95,
          },
          {
            description: "Pastry",
            quantity: 2,
            unit_price: 5.2,
            total_price: 10.4,
          },
        ],
        confidence: 0.95,
        raw_text: `Sample Store
123 Main Street
Date: ${new Date().toLocaleDateString()}

1x Coffee         $4.50
1x Sandwich       $8.95
2x Pastry         $10.40

Subtotal:         $23.85
Tax:              $2.14
Total:            $25.99

Thank you!`,
        structured_data: {
          mock: true,
          processor_version: "mock-v1.0",
        },
      },
    };
  }
}

module.exports = new DocumentAIService();
