const { User, Subscription, Payment, UsageTracking } = require('../models');
const BakongPaymentService = require('../services/bakongPaymentService');
const { getUserUsageStats, canUseFeature } = require('../middleware/usageLimits');

// Initialize Bakong payment service
const bakongService = new BakongPaymentService();

/**
 * Get user's subscription and billing information
 */
const getBillingInfo = async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user with subscription info
    const user = await User.findByPk(userId, {
      include: [
        {
          model: Subscription,
          as: 'subscriptions',
          order: [['created_at', 'DESC']],
          limit: 5
        }
      ]
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Get current subscription
    const currentSubscription = await Subscription.getActiveSubscription(userId);

    // Get usage statistics
    const usageStats = await getUserUsageStats(userId);

    // Get recent payments
    const recentPayments = await Payment.findAll({
      where: { user_id: userId },
      order: [['created_at', 'DESC']],
      limit: 10
    });

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          subscription_expires_at: user.subscription_expires_at,
          isPremium: user.isPremium()
        },
        currentSubscription,
        usageStats,
        recentPayments,
        subscriptionHistory: user.subscriptions
      }
    });

  } catch (error) {
    console.error('Get billing info error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get billing information'
    });
  }
};

/**
 * Create premium subscription payment
 */
const createPremiumPayment = async (req, res) => {
  try {
    const userId = req.user.id;
    const { sessionId } = req.body;

    // Check if user already has an active premium subscription
    const user = await User.findByPk(userId);
    if (user.isPremium()) {
      return res.status(400).json({
        success: false,
        error: 'User already has an active premium subscription'
      });
    }

    // Check for pending payments
    const pendingPayment = await Payment.findOne({
      where: {
        user_id: userId,
        status: 'pending'
      },
      order: [['created_at', 'DESC']]
    });

    if (pendingPayment) {
      // Return existing pending payment instead of creating new one
      const paymentStatus = await bakongService.checkPaymentStatus(pendingPayment.id);
      
      // Reconstruct image path for existing payment
      const imagePath = `/qr/qr_${pendingPayment.bill_number}.png`;
      
      return res.json({
        success: true,
        message: 'Existing pending payment found',
        data: {
          paymentId: pendingPayment.id,
          amount: pendingPayment.amount,
          currency: pendingPayment.currency,
          billNumber: pendingPayment.bill_number,
          qrCode: pendingPayment.qr_code,
          md5Hash: pendingPayment.qr_md5_hash,
          imagePath: imagePath,
          status: paymentStatus.status,
          existing: true
        }
      });
    }

    // Create new premium payment
    const result = await bakongService.createPremiumPayment(userId, sessionId);

    res.json({
      success: true,
      message: 'Premium payment created successfully',
      data: result.paymentData
    });

  } catch (error) {
    console.error('Create premium payment error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to create premium payment'
    });
  }
};

/**
 * Check payment status
 */
const checkPaymentStatus = async (req, res) => {
  try {
    const { paymentId } = req.params;
    const userId = req.user.id;

    // Verify payment belongs to user
    const payment = await Payment.findOne({
      where: {
        id: paymentId,
        user_id: userId
      }
    });

    if (!payment) {
      return res.status(404).json({
        success: false,
        error: 'Payment not found'
      });
    }

    const status = await bakongService.checkPaymentStatus(paymentId);

    res.json({
      success: true,
      data: status
    });

  } catch (error) {
    console.error('Check payment status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check payment status'
    });
  }
};

/**
 * Get usage statistics
 */
const getUsageStats = async (req, res) => {
  try {
    const userId = req.user.id;
    const stats = await getUserUsageStats(userId);

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Get usage stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get usage statistics'
    });
  }
};

/**
 * Cancel subscription
 */
const cancelSubscription = async (req, res) => {
  try {
    const userId = req.user.id;
    const { reason } = req.body;

    const subscription = await Subscription.getActiveSubscription(userId);
    
    if (!subscription) {
      return res.status(404).json({
        success: false,
        error: 'No active subscription found'
      });
    }

    // Cancel subscription
    await subscription.update({
      status: 'cancelled',
      cancelled_at: new Date(),
      cancellation_reason: reason || 'User requested cancellation'
    });

    // Downgrade user to Freemium
    const user = await User.findByPk(userId);
    await user.downgradeToFreemium();

    res.json({
      success: true,
      message: 'Subscription cancelled successfully'
    });

  } catch (error) {
    console.error('Cancel subscription error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cancel subscription'
    });
  }
};

/**
 * Get payment history
 */
const getPaymentHistory = async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 20 } = req.query;

    const offset = (page - 1) * limit;

    const payments = await Payment.findAndCountAll({
      where: { user_id: userId },
      include: [
        {
          model: Subscription,
          as: 'subscription'
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        payments: payments.rows,
        pagination: {
          total: payments.count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(payments.count / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get payment history error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get payment history'
    });
  }
};

/**
 * Get monitoring status (admin only)
 */
const getMonitoringStatus = async (req, res) => {
  try {
    const status = bakongService.getMonitoringStatus();

    res.json({
      success: true,
      data: status
    });

  } catch (error) {
    console.error('Get monitoring status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get monitoring status'
    });
  }
};

module.exports = {
  getBillingInfo,
  createPremiumPayment,
  checkPaymentStatus,
  getUsageStats,
  cancelSubscription,
  getPaymentHistory,
  getMonitoringStatus
};
