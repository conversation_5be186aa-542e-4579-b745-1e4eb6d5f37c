{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "close": "Close", "submit": "Submit", "reset": "Reset", "back": "Back", "next": "Next", "previous": "Previous", "yes": "Yes", "no": "No", "ok": "OK", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information"}, "navigation": {"dashboard": "Dashboard", "transactions": "Transactions", "import": "Import", "budgets": "Budgets", "goals": "Goals", "reports": "Reports", "profile": "Profile", "settings": "Settings", "logout": "Logout"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "name": "Full Name", "phone": "Phone Number", "rememberMe": "Remember me", "loginButton": "Sign In", "registerButton": "Create Account", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "signInHere": "Sign in here", "signUpHere": "Sign up here"}, "dashboard": {"title": "Dashboard", "totalBalance": "Total Balance", "monthlyIncome": "Monthly Income", "monthlyExpenses": "Monthly Expenses", "budgetUsed": "Budget Used", "recentTransactions": "Recent Transactions", "financialInsights": "Financial Insights", "quickActions": "Quick Actions", "addTransaction": "Add Transaction", "viewAllTransactions": "View All Transactions", "createBudget": "Create Budget", "setGoal": "Set Goal", "activeGoals": "Active Goals", "completed": "Completed", "noRecentTransactions": "No recent transactions", "noGoalsSet": "No goals set yet"}, "transactions": {"title": "Transactions", "addTransaction": "Add Transaction", "editTransaction": "Edit Transaction", "deleteTransaction": "Delete Transaction", "date": "Date", "details": "Details", "category": "Category", "amount": "Amount", "type": "Type", "income": "Income", "expense": "Expense", "currency": "<PERSON><PERSON><PERSON><PERSON>", "notes": "Notes", "referenceNumber": "Reference Number", "scanReceipt": "Scan Receipt", "noTransactions": "No transactions found", "totalIncome": "Total Income", "totalExpenses": "Total Expenses", "netAmount": "Net Amount"}, "import": {"title": "Import", "uploadFile": "Upload File", "templates": "Templates", "history": "Import History", "dragDropFile": "Drag and drop your Excel, CSV, or PDF file here, or click to browse", "noFileError": "No file to import. Please upload a file first.", "fileTypeError": "Please upload an Excel (.xlsx, .xls), CSV (.csv), or PDF (.pdf) file", "fileSizeError": "File size must be less than 10MB", "fileUploaded": "File uploaded successfully! Review the preview below.", "importFailed": "Import failed", "supportedFormats": "Supported formats: Excel, CSV, PDF bank statements", "previewImport": "Preview Import", "executeImport": "Execute Import", "downloadTemplate": "Download Template", "acledaTemplate": "ACLEDA Template", "abaTemplate": "ABA Template", "importHistory": "Import History", "fileName": "File Name", "importDate": "Import Date", "status": "Status", "totalRows": "Total Rows", "imported": "Imported", "duplicates": "Duplicates", "errors": "Errors"}, "budgets": {"title": "Budgets", "createBudget": "Create Budget", "editBudget": "Edit Budget", "budgetName": "Budget Name", "amount": "Amount", "period": "Period", "startDate": "Start Date", "endDate": "End Date", "spent": "Spent", "remaining": "Remaining", "progress": "Progress", "noBudgets": "No budgets created yet"}, "goals": {"title": "Goals", "createGoal": "Create Goal", "editGoal": "Edit Goal", "goalName": "Goal Name", "targetAmount": "Target Amount", "currentAmount": "Current Amount", "targetDate": "Target Date", "description": "Description", "progress": "Progress", "achieved": "Achieved", "inProgress": "In Progress", "noGoals": "No goals set yet"}, "reports": {"title": "Reports", "generateReport": "Generate Report", "exportReport": "Export Report", "filterBy": "Filter by", "week": "Week", "month": "Month", "year": "Year", "customRange": "Custom Range", "exportAs": "Export as", "pdf": "PDF", "excel": "Excel", "incomeExpenseReport": "Income & Expense Report", "categoryBreakdown": "Category Breakdown", "monthlyTrends": "Monthly Trends", "budgetPerformance": "Budget Performance", "goalProgress": "Goal Progress", "totalIncome": "Total Income", "totalExpenses": "Total Expenses", "netIncome": "Net Income", "averageDaily": "Average Daily", "topCategories": "Top Categories", "noDataAvailable": "No data available for the selected period"}, "profile": {"title": "Profile", "personalInformation": "Personal Information", "preferences": "Preferences", "language": "Language", "currency": "<PERSON><PERSON><PERSON><PERSON>", "updateProfile": "Update Profile", "profileUpdated": "Profile updated successfully"}, "settings": {"title": "Settings", "appearance": "Appearance", "theme": "Theme", "light": "Light", "dark": "Dark", "system": "System", "notifications": "Notifications", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications", "security": "Security", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password"}, "categories": {"food": "Food & Dining", "transportation": "Transportation", "shopping": "Shopping", "entertainment": "Entertainment", "bills": "Bills & Utilities", "healthcare": "Healthcare", "education": "Education", "travel": "Travel", "income": "Income", "other": "Other"}, "currencies": {"USD": "US Dollar", "KHR": "Cambodian Riel", "VND": "Vietnamese Dong", "EUR": "Euro", "SEK": "Swedish Krona"}, "billing": {"title": "Billing & Subscription", "currentPlan": "Current Plan", "freemium": "Freemium Plan", "premium": "Premium Plan", "monthlyPrice": "Monthly Price", "nextBilling": "Next Billing", "usageOverview": "Usage Overview", "paymentHistory": "Payment History", "features": "Features", "upgradeToPremium": "Upgrade to Premium", "unlimited": "Unlimited", "remaining": "remaining this month", "ocrScans": "OCR Scans", "excelImports": "Excel Imports", "basicTransactionManagement": "Basic transaction management", "budgetTracking": "Budget tracking", "unlimitedReceiptScans": "Unlimited receipt scans", "unlimitedExcelImports": "Unlimited Excel imports", "advancedAnalytics": "Advanced analytics", "prioritySupport": "Priority support", "exportMultipleFormats": "Export to multiple formats", "upgradeNow": "Upgrade Now", "paymentModal": {"title": "Upgrade to Premium", "whatYouGet": "What you'll get:", "generatePaymentQR": "Generate Payment QR", "creatingPayment": "Creating Payment...", "timeRemaining": "Time remaining", "scanQRCode": "Scan the QR code with your Bakong app to complete payment", "amount": "Amount", "billNumber": "<PERSON>", "checkPaymentStatus": "Check Payment Status", "checkingPayment": "Checking payment status...", "paymentSuccessful": "Payment Successful!", "accountUpgraded": "Your account has been upgraded to Premium. You now have unlimited access to all features.", "paymentFailed": "Payment Failed", "paymentExpired": "The payment session has expired or failed. Please try again.", "tryAgain": "Try Again", "continue": "Continue"}}, "feedback": {"title": "Share Your Feedback", "helpImprove": "Help us improve Finwise! Your feedback is valuable and helps us make the app better for everyone.", "rateExperience": "How would you rate your experience? (Optional)", "feedbackCategory": "Feedback Category", "categories": {"general": "General <PERSON>", "featureRequest": "Feature Request", "bugReport": "Bug Report", "improvement": "Improvement Suggestion", "other": "Other"}, "yourFeedback": "Your Feedback", "feedbackPlaceholder": "Tell us what you think about <PERSON><PERSON>. What features do you love? What could be improved?", "submitAnonymously": "Submit anonymously", "submitFeedback": "Submit <PERSON>", "submitting": "Submitting...", "skip": "<PERSON><PERSON>", "monthlyPopup": "This popup appears once per month. You can always provide feedback from the settings page.", "thankYou": "Thank you for your feedback!"}, "notifications": {"title": "Notifications", "markAllRead": "Mark all as read", "noNotifications": "No notifications", "types": {"payment": "Payment", "subscription": "Subscription", "usage": "Usage Limit", "system": "System", "security": "Security"}}, "messages": {"loginSuccess": "Login successful!", "loginError": "<PERSON><PERSON> failed. Please check your credentials.", "registerSuccess": "Account created successfully!", "registerError": "Registration failed. Please try again.", "transactionAdded": "Transaction added successfully!", "transactionUpdated": "Transaction updated successfully!", "transactionDeleted": "Transaction deleted successfully!", "budgetCreated": "Budget created successfully!", "budgetUpdated": "Budget updated successfully!", "goalCreated": "Goal created successfully!", "goalUpdated": "Goal updated successfully!", "importSuccess": "Import completed successfully!", "importError": "Import failed. Please check your file.", "profileUpdated": "Profile updated successfully!", "settingsUpdated": "Settings updated successfully!", "passwordChanged": "Password changed successfully!", "networkError": "Network error. Please check your connection.", "serverError": "Server error. Please try again later.", "usageLimitReached": "Usage limit reached for this month. Upgrade to Premium for unlimited access.", "subscriptionExpiring": "Your Premium subscription expires soon. Renew to continue unlimited access.", "subscriptionExpired": "Your Premium subscription has expired. You have been downgraded to Freemium.", "paymentSuccessful": "Payment successful! Your account has been upgraded to Premium.", "reportGenerated": "Report generated successfully!", "reportExported": "Report exported successfully!"}}