import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const useThemeStore = create(
  persist(
    (set, get) => ({
      // State
      theme: 'light', // 'light', 'dark', 'system'
      isDark: false,

      // Actions
      setTheme: (theme) => {
        set({ theme });
        get().applyTheme(theme);
      },

      applyTheme: (theme) => {
        const root = window.document.documentElement;
        
        if (theme === 'system') {
          const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
          root.classList.toggle('dark', systemTheme === 'dark');
          set({ isDark: systemTheme === 'dark' });
        } else {
          root.classList.toggle('dark', theme === 'dark');
          set({ isDark: theme === 'dark' });
        }
      },

      initializeTheme: () => {
        const { theme, applyTheme } = get();
        applyTheme(theme);

        // Listen for system theme changes
        if (theme === 'system') {
          const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
          const handleChange = () => {
            if (get().theme === 'system') {
              applyTheme('system');
            }
          };
          
          mediaQuery.addEventListener('change', handleChange);
          
          // Return cleanup function
          return () => mediaQuery.removeEventListener('change', handleChange);
        }
      },

      toggleTheme: () => {
        const { theme, setTheme } = get();
        if (theme === 'light') {
          setTheme('dark');
        } else if (theme === 'dark') {
          setTheme('system');
        } else {
          setTheme('light');
        }
      }
    }),
    {
      name: 'finwise-theme',
      partialize: (state) => ({ theme: state.theme }),
    }
  )
);

export default useThemeStore;
