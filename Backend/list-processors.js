/**
 * List available Document AI processors
 */

require("dotenv").config();
const path = require("path");

async function listProcessors() {
  try {
    console.log("📋 Listing Document AI processors...");

    const { DocumentProcessorServiceClient } =
      require("@google-cloud/documentai").v1;

    const credentialsPath = path.join(
      __dirname,
      "finwise-471010-0dfebced9164.json"
    );
    const client = new DocumentProcessorServiceClient({
      keyFilename: credentialsPath,
    });

    const projectId = "finwise-471010";

    // Try different locations
    const locations = ["us", "eu", "us-central1", "us-east1", "europe-west3"];

    for (const location of locations) {
      try {
        console.log(`\n🌍 Checking location: ${location}`);

        const parent = `projects/${projectId}/locations/${location}`;
        const [processors] = await client.listProcessors({ parent });

        if (processors.length > 0) {
          console.log(
            `✅ Found ${processors.length} processors in ${location}:`
          );

          processors.forEach((processor, index) => {
            console.log(`  ${index + 1}. Name: ${processor.name}`);
            console.log(`     Display Name: ${processor.displayName}`);
            console.log(`     Type: ${processor.type}`);
            console.log(`     State: ${processor.state}`);

            // Extract processor ID from name
            const processorId = processor.name.split("/").pop();
            console.log(`     Processor ID: ${processorId}`);
            console.log(`     ---`);
          });
        } else {
          console.log(`❌ No processors found in ${location}`);
        }
      } catch (locationError) {
        console.log(`❌ Error checking ${location}:`, locationError.message);
      }
    }
  } catch (error) {
    console.log("❌ Failed to list processors:", error.message);
    console.log("Full error:", error);
  }
}

listProcessors();
