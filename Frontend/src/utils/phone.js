export const normalizeCambodianPhone = (input) => {
  if (!input) return '';
  let digits = input.replace(/\D/g, '');
  if (digits.startsWith('8550')) digits = '855' + digits.slice(4);
  if (digits.startsWith('0')) digits = '855' + digits.slice(1);
  else if (!digits.startsWith('855')) digits = '855' + digits; // assume local missing country code
  return '+' + digits;
};

export const formatCambodianPhonePretty = (input) => {
  const norm = normalizeCambodianPhone(input);
  const m = norm.match(/^\+855(\d{2})(\d{3})(\d{3,4})$/);
  if (m) return `+855 ${m[1]} ${m[2]} ${m[3]}`;
  return norm;
};

export const isLikelyPhone = (val) => /^[+]?[-()\d\s]{6,}$/.test(val) && !/@/.test(val);
