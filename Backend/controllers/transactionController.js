const { validationResult } = require('express-validator');
const { Transaction, Category, User } = require('../models');
const { Op } = require('sequelize');
const moment = require('moment');

// @desc    Get all transactions for a user
// @route   GET /api/transactions
// @access  Private
const getTransactions = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const {
      page = 1,
      limit = 20,
      category,
      type,
      startDate,
      endDate,
      search,
      sortBy = 'date',
      sortOrder = 'DESC'
    } = req.query;

    // Build where clause
    const whereClause = { user_id: userId };

    // Filter by category
    if (category) {
      whereClause.category_id = category;
    }

    // Filter by type (income/expense)
    if (type === 'income') {
      whereClause.money_in = { [Op.gt]: 0 };
    } else if (type === 'expense') {
      whereClause.money_out = { [Op.gt]: 0 };
    }

    // Filter by date range
    if (startDate || endDate) {
      whereClause.date = {};
      if (startDate) {
        whereClause.date[Op.gte] = moment(startDate).format('YYYY-MM-DD');
      }
      if (endDate) {
        whereClause.date[Op.lte] = moment(endDate).format('YYYY-MM-DD');
      }
    }

    // Search in details
    if (search) {
      whereClause.details = {
        [Op.like]: `%${search}%`
      };
    }

    // Calculate offset
    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Get transactions with pagination
    const { count, rows: transactions } = await Transaction.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'name_km', 'type', 'icon', 'color']
        }
      ],
      order: [[sortBy, sortOrder.toUpperCase()]],
      limit: parseInt(limit),
      offset: offset
    });

    // Calculate summary statistics
    const summary = await Transaction.findAll({
      where: whereClause,
      attributes: [
        [Transaction.sequelize.fn('SUM', Transaction.sequelize.col('money_in')), 'totalIncome'],
        [Transaction.sequelize.fn('SUM', Transaction.sequelize.col('money_out')), 'totalExpense'],
        [Transaction.sequelize.fn('COUNT', Transaction.sequelize.col('id')), 'totalCount']
      ],
      raw: true
    });

    const totalIncome = parseFloat(summary[0].totalIncome) || 0;
    const totalExpense = parseFloat(summary[0].totalExpense) || 0;
    const balance = totalIncome - totalExpense;

    res.json({
      success: true,
      data: {
        transactions,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / parseInt(limit)),
          totalItems: count,
          itemsPerPage: parseInt(limit)
        },
        summary: {
          totalIncome,
          totalExpense,
          balance,
          totalTransactions: count
        }
      }
    });

  } catch (error) {
    next(error);
  }
};

// @desc    Get single transaction
// @route   GET /api/transactions/:id
// @access  Private
const getTransaction = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const transaction = await Transaction.findOne({
      where: { id, user_id: userId },
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'name_km', 'type', 'icon', 'color']
        }
      ]
    });

    if (!transaction) {
      return res.status(404).json({
        success: false,
        error: 'Transaction not found'
      });
    }

    res.json({
      success: true,
      data: transaction
    });

  } catch (error) {
    next(error);
  }
};

// @desc    Create new transaction
// @route   POST /api/transactions
// @access  Private
const createTransaction = async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const {
      date,
      details,
      money_in,
      money_out,
      currency = 'USD',
      category_id,
      notes,
      tags,
      location,
      receipt_image_url,
      reference_number
    } = req.body;

    // Validate that either money_in or money_out is provided
    if ((!money_in || money_in <= 0) && (!money_out || money_out <= 0)) {
      return res.status(400).json({
        success: false,
        error: 'Either money_in or money_out must be greater than 0'
      });
    }

    // Validate that both money_in and money_out are not provided
    if (money_in > 0 && money_out > 0) {
      return res.status(400).json({
        success: false,
        error: 'Cannot have both money_in and money_out in the same transaction'
      });
    }

    // Validate category exists if provided
    if (category_id) {
      const category = await Category.findByPk(category_id);
      if (!category) {
        return res.status(400).json({
          success: false,
          error: 'Invalid category'
        });
      }
    }

    // Create transaction
    const transaction = await Transaction.create({
      user_id: userId,
      date: moment(date).format('YYYY-MM-DD'),
      details,
      money_in: money_in || 0,
      money_out: money_out || 0,
      currency: currency.toUpperCase(),
      category_id,
      notes,
      tags,
      location,
      receipt_image_url,
      reference_number,
      imported_from: 'manual'
    });

    // Fetch the created transaction with category
    const createdTransaction = await Transaction.findByPk(transaction.id, {
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'name_km', 'type', 'icon', 'color']
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Transaction created successfully',
      data: createdTransaction
    });

  } catch (error) {
    next(error);
  }
};

// @desc    Update transaction
// @route   PUT /api/transactions/:id
// @access  Private
const updateTransaction = async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const userId = req.user.id;

    // Find transaction
    const transaction = await Transaction.findOne({
      where: { id, user_id: userId }
    });

    if (!transaction) {
      return res.status(404).json({
        success: false,
        error: 'Transaction not found'
      });
    }

    const {
      date,
      details,
      money_in,
      money_out,
      currency,
      category_id,
      notes,
      tags,
      location,
      receipt_image_url,
      reference_number
    } = req.body;

    // Validate amounts if provided
    if (money_in !== undefined && money_out !== undefined) {
      if ((!money_in || money_in <= 0) && (!money_out || money_out <= 0)) {
        return res.status(400).json({
          success: false,
          error: 'Either money_in or money_out must be greater than 0'
        });
      }

      if (money_in > 0 && money_out > 0) {
        return res.status(400).json({
          success: false,
          error: 'Cannot have both money_in and money_out in the same transaction'
        });
      }
    }

    // Validate category exists if provided
    if (category_id) {
      const category = await Category.findByPk(category_id);
      if (!category) {
        return res.status(400).json({
          success: false,
          error: 'Invalid category'
        });
      }
    }

    // Update transaction
    await transaction.update({
      ...(date && { date: moment(date).format('YYYY-MM-DD') }),
      ...(details && { details }),
      ...(money_in !== undefined && { money_in }),
      ...(money_out !== undefined && { money_out }),
      ...(currency && { currency: currency.toUpperCase() }),
      ...(category_id !== undefined && { category_id }),
      ...(notes !== undefined && { notes }),
      ...(tags !== undefined && { tags }),
      ...(location !== undefined && { location }),
      ...(receipt_image_url !== undefined && { receipt_image_url }),
      ...(reference_number !== undefined && { reference_number })
    });

    // Fetch updated transaction with category
    const updatedTransaction = await Transaction.findByPk(transaction.id, {
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'name_km', 'type', 'icon', 'color']
        }
      ]
    });

    res.json({
      success: true,
      message: 'Transaction updated successfully',
      data: updatedTransaction
    });

  } catch (error) {
    next(error);
  }
};

// @desc    Delete transaction
// @route   DELETE /api/transactions/:id
// @access  Private
const deleteTransaction = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const transaction = await Transaction.findOne({
      where: { id, user_id: userId }
    });

    if (!transaction) {
      return res.status(404).json({
        success: false,
        error: 'Transaction not found'
      });
    }

    await transaction.destroy();

    res.json({
      success: true,
      message: 'Transaction deleted successfully'
    });

  } catch (error) {
    next(error);
  }
};

// @desc    Bulk delete transactions
// @route   DELETE /api/transactions/bulk
// @access  Private
const bulkDeleteTransactions = async (req, res, next) => {
  try {
    const { ids } = req.body;
    const userId = req.user.id;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Transaction IDs are required'
      });
    }

    const deletedCount = await Transaction.destroy({
      where: {
        id: { [Op.in]: ids },
        user_id: userId
      }
    });

    res.json({
      success: true,
      message: `${deletedCount} transactions deleted successfully`,
      data: { deletedCount }
    });

  } catch (error) {
    next(error);
  }
};

module.exports = {
  getTransactions,
  getTransaction,
  createTransaction,
  updateTransaction,
  deleteTransaction,
  bulkDeleteTransactions
};
