const express = require("express");
const router = express.Router();

// Import controllers and middleware
const categoryController = require("../controllers/categoryController");
const { authenticateToken } = require("../middleware/auth");

// Apply authentication middleware to all routes
router.use(authenticateToken);

// @route   GET /api/categories
// @desc    Get all categories
// @access  Private
router.get("/", categoryController.getCategories);

// @route   GET /api/categories/:id
// @desc    Get single category
// @access  Private
router.get("/:id", categoryController.getCategory);

// @route   GET /api/categories/:id/stats
// @desc    Get category statistics
// @access  Private
router.get("/:id/stats", categoryController.getCategoryStats);

// @route   POST /api/categories
// @desc    Create new category
// @access  Private
router.post("/", categoryController.createCategory);

// @route   PUT /api/categories/:id
// @desc    Update category
// @access  Private
router.put("/:id", categoryController.updateCategory);

// @route   DELETE /api/categories/:id
// @desc    Delete category
// @access  Private
router.delete("/:id", categoryController.deleteCategory);

module.exports = router;
