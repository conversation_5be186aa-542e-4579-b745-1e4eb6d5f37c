import { create } from 'zustand';
import { feedbackAPI } from '../services/billingAPI';
import toast from 'react-hot-toast';

const useFeedbackStore = create((set, get) => ({
  // State
  canProvideFeedback: false,
  hasProvidedThisMonth: false,
  feedbackHistory: [],
  isLoading: false,
  error: null,
  showFeedbackPopup: false,

  // Actions
  checkFeedbackEligibility: async () => {
    try {
      const response = await feedbackAPI.canProvideFeedback();
      const data = response.data.data;

      set({
        canProvideFeedback: data.canProvideFeedback,
        hasProvidedThisMonth: data.hasProvidedThisMonth,
        showFeedbackPopup: data.canProvideFeedback // Show popup if user can provide feedback
      });

      return { success: true, data };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to check feedback eligibility';
      console.error('Check feedback eligibility error:', error);
      return { success: false, error: errorMessage };
    }
  },

  submitFeedback: async (feedbackData) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await feedbackAPI.submitFeedback(feedbackData);

      set({
        isLoading: false,
        canProvideFeedback: false,
        hasProvidedThisMonth: true,
        showFeedbackPopup: false
      });

      toast.success('Thank you for your feedback!');
      return { success: true, data: response.data };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to submit feedback';
      
      set({
        isLoading: false,
        error: errorMessage
      });

      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  skipFeedback: async () => {
    try {
      const response = await feedbackAPI.skipFeedback();

      set({
        canProvideFeedback: false,
        hasProvidedThisMonth: true,
        showFeedbackPopup: false
      });

      return { success: true, data: response.data };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to skip feedback';
      console.error('Skip feedback error:', error);
      return { success: false, error: errorMessage };
    }
  },

  fetchFeedbackHistory: async (page = 1, limit = 20) => {
    try {
      const response = await feedbackAPI.getFeedbackHistory({ page, limit });
      const historyData = response.data.data;

      set({ feedbackHistory: historyData.feedback });

      return { success: true, data: historyData };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to fetch feedback history';
      console.error('Fetch feedback history error:', error);
      return { success: false, error: errorMessage };
    }
  },

  // Utility actions
  hideFeedbackPopup: () => {
    set({ showFeedbackPopup: false });
  },

  showFeedbackPopupManually: () => {
    set({ showFeedbackPopup: true });
  },

  clearError: () => {
    set({ error: null });
  }
}));

export default useFeedbackStore;
