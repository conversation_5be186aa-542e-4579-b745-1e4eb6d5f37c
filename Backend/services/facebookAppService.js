const axios = require("axios");

class FacebookAppService {
  constructor() {
    this.appId = process.env.FACEBOOK_APP_ID;
    this.appSecret = process.env.FACEBOOK_APP_SECRET;
    this.graphApiVersion = "v18.0";
  }

  // Check if Facebook app is active and properly configured
  async checkAppStatus() {
    try {
      if (!this.appId || !this.appSecret) {
        throw new Error("Facebook App ID or App Secret not configured");
      }

      // Get app access token
      const appAccessToken = await this.getAppAccessToken();

      // Check app details
      const appInfo = await this.getAppInfo(appAccessToken);

      return {
        isActive: true,
        appId: this.appId,
        appName: appInfo.name,
        status: appInfo.app_status || "unknown",
        domains: appInfo.app_domains || [],
        message: "Facebook app is properly configured",
      };
    } catch (error) {
      console.error("❌ Facebook app status check failed:", error.message);
      return {
        isActive: false,
        error: error.message,
        suggestions: this.getConfigurationSuggestions(),
      };
    }
  }

  // Get app access token
  async getAppAccessToken() {
    try {
      const response = await axios.get(
        `https://graph.facebook.com/oauth/access_token`,
        {
          params: {
            client_id: this.appId,
            client_secret: this.appSecret,
            grant_type: "client_credentials",
          },
        }
      );

      return response.data.access_token;
    } catch (error) {
      throw new Error(
        `Failed to get app access token: ${
          error.response?.data?.error?.message || error.message
        }`
      );
    }
  }

  // Get app information
  async getAppInfo(accessToken) {
    try {
      const response = await axios.get(
        `https://graph.facebook.com/${this.graphApiVersion}/${this.appId}`,
        {
          params: {
            access_token: accessToken,
            fields: "name,app_domains,privacy_policy_url,terms_of_service_url",
          },
        }
      );

      return response.data;
    } catch (error) {
      throw new Error(
        `Failed to get app info: ${
          error.response?.data?.error?.message || error.message
        }`
      );
    }
  }

  // Validate user access token
  async validateUserToken(userAccessToken) {
    try {
      const appAccessToken = await this.getAppAccessToken();

      const response = await axios.get(
        `https://graph.facebook.com/${this.graphApiVersion}/debug_token`,
        {
          params: {
            input_token: userAccessToken,
            access_token: appAccessToken,
          },
        }
      );

      const tokenInfo = response.data.data;

      if (!tokenInfo.is_valid) {
        throw new Error("Invalid user access token");
      }

      if (tokenInfo.app_id !== this.appId) {
        throw new Error("Token was not issued for this app");
      }

      return {
        isValid: true,
        userId: tokenInfo.user_id,
        appId: tokenInfo.app_id,
        expiresAt: tokenInfo.expires_at,
        scopes: tokenInfo.scopes || [],
      };
    } catch (error) {
      throw new Error(`Token validation failed: ${error.message}`);
    }
  }

  // Get configuration suggestions for fixing "App not active" issue
  getConfigurationSuggestions() {
    return [
      {
        issue: "App not active",
        solution:
          'Go to Facebook Developers Console and make sure your app is in "Live" mode',
        url: "https://developers.facebook.com/apps/",
      },
      {
        issue: "Missing app domains",
        solution:
          "Add your domain (localhost:5173 for development) to App Domains in Facebook App Settings",
        steps: [
          "Go to Facebook App Settings > Basic",
          'Add "localhost" to App Domains',
          'Add "http://localhost:5173" to Valid OAuth Redirect URIs',
        ],
      },
      {
        issue: "App Review required",
        solution:
          "Some permissions require app review. For development, add test users.",
        steps: [
          "Go to App Review > Permissions and Features",
          "Check which permissions need review",
          "Add test users in Roles > Test Users for development",
        ],
      },
      {
        issue: "Invalid credentials",
        solution: "Verify App ID and App Secret in Facebook Developers Console",
        note: "Make sure the credentials in .env file match those in Facebook App Settings",
      },
    ];
  }

  // Create a test user for development
  async createTestUser() {
    try {
      const appAccessToken = await this.getAppAccessToken();

      const response = await axios.post(
        `https://graph.facebook.com/${this.graphApiVersion}/${this.appId}/accounts/test-users`,
        {
          installed: true,
          permissions: "email,public_profile",
          access_token: appAccessToken,
        }
      );

      return {
        success: true,
        testUser: response.data,
        message: "Test user created successfully",
      };
    } catch (error) {
      throw new Error(
        `Failed to create test user: ${
          error.response?.data?.error?.message || error.message
        }`
      );
    }
  }
}

module.exports = new FacebookAppService();
