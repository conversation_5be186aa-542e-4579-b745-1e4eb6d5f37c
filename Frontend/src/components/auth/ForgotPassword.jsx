import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import { ArrowLeft } from 'lucide-react';
import OTPInput from './OTPInput';
import { useTranslation } from '../../hooks/useTranslation';
import { API_ENDPOINTS } from '../../config/api';
import { normalizeCambodianPhone, isLikelyPhone, formatCambodianPhonePretty } from '../../utils/phone';

const ForgotPassword = ({ onBack, onSuccess }) => {
  const { t } = useTranslation();
  const [step, setStep] = useState('identifier'); // 'identifier', 'otp', 'newPassword'
  const [identifier, setIdentifier] = useState('');
  const [resetToken, setResetToken] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const isEmail = (value) => /@/.test(value);
  const isPhone = (value) => /^[+]?[-()\d\s]{6,}$/.test(value);

  // Step 1: Send OTP to identifier
  const handleSendOTP = async () => {
    if (!identifier.trim()) {
      toast.error('Please enter your email or phone');
      return;
    }
  const id = identifier.trim();
    if (!isEmail(id) && !isPhone(id)) {
      toast.error('Enter a valid email or phone number');
      return;
    }

    setLoading(true);
    try {
      const payloadIdentifier = isEmail(id) ? id : normalizeCambodianPhone(id);
      const response = await fetch(API_ENDPOINTS.AUTH.FORGOT_PASSWORD, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ identifier: payloadIdentifier }),
      });
      const data = await response.json();
      if (data.success) {
        toast.success('OTP sent');
        setStep('otp');
      } else {
        throw new Error(data.error);
      }
    } catch (error) {
      console.error('Send OTP error:', error);
      toast.error(error.message || 'Failed to send OTP');
    } finally {
      setLoading(false);
    }
  };

  // Step 2: Verify OTP and get reset token
  const handleVerifyOTP = async (otp) => {
    setLoading(true);
    try {
      const payloadIdentifier = isEmail(identifier) ? identifier.trim() : normalizeCambodianPhone(identifier);
      const response = await fetch(API_ENDPOINTS.AUTH.VERIFY_PASSWORD_RESET_OTP, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ identifier: payloadIdentifier, otp }),
      });
      const data = await response.json();
      if (data.success) {
        setResetToken(data.data.resetToken);
        setStep('newPassword');
        toast.success('OTP verified successfully. You can now reset your password.');
      } else {
        throw new Error(data.error);
      }
    } catch (error) {
      console.error('Verify OTP error:', error);
      toast.error(error.message || 'Invalid OTP');
    } finally {
      setLoading(false);
    }
  };

  // Step 3: Reset password using the token
  const handleResetPassword = async () => {
    if (!newPassword || !confirmPassword) {
      toast.error('Please fill in all fields');
      return;
    }
    if (newPassword !== confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }
    if (newPassword.length < 8) {
      toast.error('Password must be at least 8 characters');
      return;
    }
    setLoading(true);
    try {
      const response = await fetch(API_ENDPOINTS.AUTH.RESET_PASSWORD, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token: resetToken, password: newPassword, confirmPassword }),
      });
      const data = await response.json();
      if (data.success) {
        toast.success('Password reset successfully! Please login with your new password.');
        onSuccess?.();
      } else {
        throw new Error(data.error);
      }
    } catch (error) {
      console.error('Reset password error:', error);
      toast.error(error.message || 'Failed to reset password');
    } finally {
      setLoading(false);
    }
  };

  const renderIdentifierStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">{t('auth.forgotPasswordTitle')}</h2>
        <p className="mt-2 text-gray-600 dark:text-gray-400">{t('auth.enterEmailOrPhone')}</p>
      </div>
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{t('auth.emailOrPhone')}</label>
          <input
            type="text"
            value={identifier}
            onChange={(e) => {
              let val = e.target.value;
              if (isLikelyPhone(val)) {
                const pretty = formatCambodianPhonePretty(val);
                if (pretty !== val) val = pretty;
              }
              setIdentifier(val);
            }}
            placeholder="Enter email or phone"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-primary-400 dark:focus:ring-primary-800"
          />
        </div>
      </div>
      <button
        onClick={handleSendOTP}
        disabled={loading}
        className="w-full px-4 py-2 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white font-medium rounded-lg transition-colors duration-200"
      >
        {loading ? 'Sending...' : 'Send Verification Code'}
      </button>
    </div>
  );

  const renderOTPStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Enter Verification Code</h2>
        <p className="mt-2 text-gray-600 dark:text-gray-400">We've sent a verification code to: {identifier}</p>
      </div>
      <OTPInput length={6} onComplete={handleVerifyOTP} identifier={identifier} type="password_reset" loading={loading} />
    </div>
  );

  const renderNewPasswordStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Set New Password</h2>
        <p className="mt-2 text-gray-600 dark:text-gray-400">Enter your new password below</p>
      </div>
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">New Password</label>
          <input
            type="password"
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
            placeholder="Enter new password"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Confirm Password</label>
          <input
            type="password"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            placeholder="Confirm new password"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
          />
        </div>
      </div>
      <button
        onClick={handleResetPassword}
        disabled={loading}
        className="w-full px-4 py-2 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white font-medium rounded-lg transition-colors duration-200"
      >
        {loading ? 'Resetting...' : 'Reset Password'}
      </button>
    </div>
  );

  return (
    <div className="max-w-md mx-auto">
      <button
        onClick={onBack}
        className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 mb-6"
      >
        <ArrowLeft className="w-4 h-4" />
        <span>Back to Login</span>
      </button>
      {step === 'identifier' && renderIdentifierStep()}
      {step === 'otp' && renderOTPStep()}
      {step === 'newPassword' && renderNewPasswordStep()}
    </div>
  );
};

export default ForgotPassword;
