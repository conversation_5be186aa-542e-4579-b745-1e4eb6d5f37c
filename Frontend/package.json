{"name": "finwise-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@tanstack/react-query": "^5.8.4", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^12.23.12", "i18next": "^23.7.6", "i18next-browser-languagedetector": "^7.2.0", "lucide-react": "^0.542.0", "react": "^18.3.1", "react-datepicker": "^8.7.0", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-i18next": "^13.5.0", "react-router-dom": "^6.20.1", "react-select": "^5.8.0", "react-toastify": "^11.0.5", "recharts": "^2.8.0", "tailwind-merge": "^2.0.0", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/js": "^9.33.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.5.1", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-basic-ssl": "^2.1.0", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.16", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^7.1.2", "vitest": "^1.0.0"}}