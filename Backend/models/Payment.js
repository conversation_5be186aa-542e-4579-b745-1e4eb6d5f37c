'use strict';

module.exports = (sequelize, DataTypes) => {
  const Payment = sequelize.define('Payment', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    subscription_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'subscriptions',
        key: 'id'
      }
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0
      },
      comment: 'Payment amount'
    },
    currency: {
      type: DataTypes.STRING(3),
      defaultValue: 'USD',
      allowNull: false,
      comment: 'Payment currency'
    },
    status: {
      type: DataTypes.ENUM('pending', 'completed', 'failed', 'cancelled', 'refunded'),
      defaultValue: 'pending',
      allowNull: false,
      comment: 'Payment status'
    },
    payment_method: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: 'Payment method (e.g., Bakong)'
    },
    payment_reference: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: 'External payment reference'
    },
    transaction_id: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: 'Payment provider transaction ID'
    },
    qr_code: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'QR code string for payment'
    },
    qr_md5_hash: {
      type: DataTypes.STRING(32),
      allowNull: true,
      comment: 'MD5 hash of QR code for tracking'
    },
    bill_number: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: 'Bill number for payment tracking'
    },
    paid_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When payment was completed'
    },
    failed_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When payment failed'
    },
    failure_reason: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Reason for payment failure'
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Additional payment metadata'
    }
  }, {
    tableName: 'payments',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['subscription_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['payment_reference']
      },
      {
        fields: ['qr_md5_hash']
      },
      {
        fields: ['bill_number']
      },
      {
        fields: ['paid_at']
      }
    ]
  });

  Payment.associate = function(models) {
    Payment.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
    Payment.belongsTo(models.Subscription, {
      foreignKey: 'subscription_id',
      as: 'subscription'
    });
  };

  // Instance methods
  Payment.prototype.markAsPaid = async function(transactionId = null, metadata = null) {
    this.status = 'completed';
    this.paid_at = new Date();
    if (transactionId) this.transaction_id = transactionId;
    if (metadata) this.metadata = metadata;
    return await this.save();
  };

  Payment.prototype.markAsFailed = async function(reason = null) {
    this.status = 'failed';
    this.failed_at = new Date();
    if (reason) this.failure_reason = reason;
    return await this.save();
  };

  // Static methods
  Payment.findByQRHash = async function(qrMd5Hash) {
    return await this.findOne({
      where: {
        qr_md5_hash: qrMd5Hash
      },
      include: [{
        model: sequelize.models.User,
        as: 'user'
      }, {
        model: sequelize.models.Subscription,
        as: 'subscription'
      }]
    });
  };

  Payment.getPendingPayments = async function() {
    return await this.findAll({
      where: {
        status: 'pending'
      },
      include: [{
        model: sequelize.models.User,
        as: 'user'
      }]
    });
  };

  return Payment;
};
