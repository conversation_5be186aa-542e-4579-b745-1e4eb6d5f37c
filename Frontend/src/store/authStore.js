import { create } from "zustand";
import { persist } from "zustand/middleware";
import axios from "axios";
import { authAPI, userAPI, setTokens, clearTokens } from "../services/api";
import toast from "react-hot-toast";

const useAuthStore = create(
  persist(
    (set, get) => ({
      // State
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials) => {
        set({ isLoading: true, error: null });
        try {
          const response = await authAPI.login(credentials);
          const { user, tokens } = response.data.data;

          // Store tokens
          setTokens(tokens.accessToken, tokens.refreshToken);

          // Update state
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          toast.success("Login successful!");
          return { success: true, user };
        } catch (error) {
          let errorMessage = "Login failed";

          if (error.response?.data?.error) {
            errorMessage = error.response.data.error;
          } else if (error.response?.data?.errors) {
            // Handle validation errors
            errorMessage = error.response.data.errors
              .map((err) => err.msg)
              .join(", ");
          } else if (error.code === "ECONNREFUSED") {
            errorMessage =
              "Unable to connect to server. Please check if the backend is running.";
          } else if (error.message) {
            errorMessage = error.message;
          }

          set({
            isLoading: false,
            error: errorMessage,
            isAuthenticated: false,
            user: null,
          });
          return { success: false, error: errorMessage };
        }
      },

      register: async (userData) => {
        set({ isLoading: true, error: null });
        try {
          const response = await authAPI.register(userData);
          const { user, tokens } = response.data.data;

          // Store tokens
          setTokens(tokens.accessToken, tokens.refreshToken);

          // Update state
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          toast.success("Registration successful!");
          return { success: true, user };
        } catch (error) {
          let errorMessage = "Registration failed";

          if (error.response?.data?.error) {
            errorMessage = error.response.data.error;
          } else if (error.response?.data?.errors) {
            // Handle validation errors
            errorMessage = error.response.data.errors
              .map((err) => err.msg)
              .join(", ");
          } else if (error.code === "ECONNREFUSED") {
            errorMessage =
              "Unable to connect to server. Please check if the backend is running.";
          } else if (error.message) {
            errorMessage = error.message;
          }

          set({
            isLoading: false,
            error: errorMessage,
            isAuthenticated: false,
            user: null,
          });
          return { success: false, error: errorMessage };
        }
      },

      logout: async () => {
        set({ isLoading: true });
        try {
          const refreshToken = localStorage.getItem("refreshToken");
          if (refreshToken) {
            await authAPI.logout(refreshToken);
          }
        } catch (error) {
          console.error("Logout error:", error);
        } finally {
          // Clear tokens and state regardless of API call success
          clearTokens();
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
          toast.success("Logged out successfully");
        }
      },

    forgotPassword: async (email, phone) => {
        set({ isLoading: true, error: null });
        try {
      const resp = await authAPI.forgotPassword({ email, phone });
          const data = resp.data?.data || {};
          set({ isLoading: false });
          if (data.mode === 'combined') {
            toast.success('Email sent with OTP + reset link');
          } else {
            toast.success('OTP sent to your email');
          }
          return { success: true, ...data };
        } catch (error) {
          const errorMessage = error.response?.data?.error || 'Failed to start password reset';
          set({ isLoading: false, error: errorMessage });
          return { success: false, error: errorMessage };
        }
      },

      resetPassword: async (token, password, confirmPassword) => {
        set({ isLoading: true, error: null });
        try {
          await authAPI.resetPassword(token, password, confirmPassword);
          set({ isLoading: false });
          toast.success("Password reset successful!");
          return { success: true };
        } catch (error) {
          const errorMessage =
            error.response?.data?.error || "Password reset failed";
          set({ isLoading: false, error: errorMessage });
          return { success: false, error: errorMessage };
        }
      },

      verifyPasswordResetOTP: async (email, otp) => {
        set({ isLoading: true, error: null });
        try {
          const resp = await authAPI.verifyPasswordResetOTP(email, otp);
          const data = resp.data?.data || {};
            set({ isLoading: false });
            toast.success('OTP verified. You can now reset password.');
            return { success: true, ...data };
        } catch (error) {
          const errorMessage = error.response?.data?.error || 'OTP verification failed';
          set({ isLoading: false, error: errorMessage });
          return { success: false, error: errorMessage };
        }
      },

      updateProfile: async (userData) => {
        set({ isLoading: true, error: null });
        try {
          const response = await userAPI.updateProfile(userData);
          const updatedUser = response.data.data;

          set({
            user: updatedUser,
            isLoading: false,
            error: null,
          });

          toast.success("Profile updated successfully!");
          return { success: true, user: updatedUser };
        } catch (error) {
          const errorMessage =
            error.response?.data?.error || "Profile update failed";
          set({ isLoading: false, error: errorMessage });
          return { success: false, error: errorMessage };
        }
      },

      changePassword: async (passwordData) => {
        set({ isLoading: true, error: null });
        try {
          await userAPI.changePassword(passwordData);
          set({ isLoading: false });
          toast.success("Password changed successfully!");
          return { success: true };
        } catch (error) {
          const errorMessage =
            error.response?.data?.error || "Password change failed";
          set({ isLoading: false, error: errorMessage });
          return { success: false, error: errorMessage };
        }
      },

      verifyEmail: async (token) => {
        set({ isLoading: true, error: null });
        try {
          await authAPI.verifyEmail(token);
          set((state) => ({
            user: state.user ? { ...state.user, email_verified: true } : null,
            isLoading: false,
          }));
          toast.success("Email verified successfully!");
          return { success: true };
        } catch (error) {
          const errorMessage =
            error.response?.data?.error || "Email verification failed";
          set({ isLoading: false, error: errorMessage });
          return { success: false, error: errorMessage };
        }
      },

      resendVerification: async (email) => {
        set({ isLoading: true, error: null });
        try {
          await authAPI.resendVerification(email);
          set({ isLoading: false });
          toast.success("Verification email sent!");
          return { success: true };
        } catch (error) {
          const errorMessage =
            error.response?.data?.error || "Failed to send verification email";
          set({ isLoading: false, error: errorMessage });
          return { success: false, error: errorMessage };
        }
      },

      // OAuth login (for handling callback with tokens)
      oauthLogin: async (accessToken, refreshToken) => {
        console.log("oauthLogin - Starting with tokens:", {
          accessToken: accessToken?.substring(0, 20) + "...",
          refreshToken: refreshToken?.substring(0, 20) + "...",
        });
        set({ isLoading: true, error: null });
        try {
          // Store tokens first
          setTokens(accessToken, refreshToken);
          console.log("oauthLogin - Tokens stored in localStorage");

          // Create a new axios instance with the token to avoid timing issues
          const authenticatedAPI = axios.create({
            baseURL: import.meta.env.VITE_API_URL || "/api",
            headers: {
              Authorization: `Bearer ${accessToken}`,
              "Content-Type": "application/json",
            },
          });

          console.log("oauthLogin - Making API call to /users/profile");
          // Get user data using the access token
          const userResponse = await authenticatedAPI.get("/users/profile");
          console.log("oauthLogin - API response:", userResponse.data);
          const user = userResponse.data.data;

          // Update state
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          console.log("oauthLogin - Success, user:", user.name);
          toast.success("Login successful!");
          return { success: true, user };
        } catch (error) {
          console.error("oauthLogin - Error:", error);
          console.error("oauthLogin - Error response:", error.response?.data);
          const errorMessage =
            error.response?.data?.error || "OAuth login failed";
          set({ isLoading: false, error: errorMessage });
          clearTokens();
          return { success: false, error: errorMessage };
        }
      },

      // Initialize auth state from stored tokens
      initializeAuth: async () => {
        // Don't initialize if we're already in a loading state (OAuth in progress)
        const currentState = get();
        if (currentState.isLoading) {
          console.log("initializeAuth - Skipping, OAuth in progress");
          return;
        }

        const token = localStorage.getItem("accessToken");
        const refreshToken = localStorage.getItem("refreshToken");
        const user = currentState.user;

        console.log(
          "initializeAuth - token exists:",
          !!token,
          "user exists:",
          !!user
        );

        if (token && user) {
          // Verify token is still valid by making a quick API call
          try {
            const authenticatedAPI = axios.create({
              baseURL: import.meta.env.VITE_API_URL || "/api",
              headers: {
                Authorization: `Bearer ${token}`,
                "Content-Type": "application/json",
              },
            });

            await authenticatedAPI.get("/users/profile");
            console.log(
              "initializeAuth - Token is valid, setting authenticated"
            );
            set({ isAuthenticated: true });
          } catch (error) {
            console.log("initializeAuth - Token is invalid, clearing auth");
            clearTokens();
            set({
              user: null,
              isAuthenticated: false,
              error: null,
            });
          }
        } else if (!token && !user) {
          // Only clear if both token and user are missing
          console.log("initializeAuth - No token and no user, clearing auth");
          clearTokens();
          set({
            user: null,
            isAuthenticated: false,
            error: null,
          });
        } else {
          console.log(
            "initializeAuth - Partial state, waiting for OAuth completion"
          );
          // Don't clear anything if we have partial state (might be in OAuth flow)
        }
      },

      // Clear error
      clearError: () => set({ error: null }),

      // Set loading state
      setLoading: (loading) => set({ isLoading: loading }),
    }),
    {
      name: "auth-storage",
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

export default useAuthStore;
