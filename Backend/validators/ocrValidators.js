const { body } = require('express-validator');

// Validate OCR scan request
const validateOCRScan = [
  // File validation is handled by multer middleware
  // Additional validation can be added here if needed
  
  body('enhanceImage')
    .optional()
    .isBoolean()
    .withMessage('Enhance image must be a boolean value'),

  body('detectLanguage')
    .optional()
    .isIn(['en', 'km', 'auto'])
    .withMessage('Detect language must be one of: en, km, auto'),

  body('extractItems')
    .optional()
    .isBoolean()
    .withMessage('Extract items must be a boolean value'),

  body('confidenceThreshold')
    .optional()
    .isFloat({ min: 0, max: 1 })
    .withMessage('Confidence threshold must be between 0 and 1')
];

module.exports = {
  validateOCRScan
};
