'use strict';

module.exports = (sequelize, DataTypes) => {
  const Notification = sequelize.define('Notification', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    type: {
      type: DataTypes.ENUM(
        'payment_success',
        'payment_failed', 
        'subscription_expiring',
        'subscription_expired',
        'usage_limit_reached',
        'usage_limit_warning',
        'system_maintenance',
        'security_alert',
        'feature_update',
        'welcome',
        'goal_achieved',
        'budget_exceeded',
        'monthly_report'
      ),
      allowNull: false,
      comment: 'Type of notification'
    },
    title: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: 'Notification title'
    },
    message: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: 'Notification message content'
    },
    data: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Additional data related to the notification'
    },
    priority: {
      type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
      defaultValue: 'medium',
      allowNull: false,
      comment: 'Notification priority level'
    },
    is_read: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
      comment: 'Whether the notification has been read'
    },
    read_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When the notification was read'
    },
    expires_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When the notification expires (optional)'
    },
    action_url: {
      type: DataTypes.STRING(500),
      allowNull: true,
      comment: 'URL to navigate to when notification is clicked'
    },
    action_text: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: 'Text for the action button'
    },
    sent_via: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Channels through which notification was sent (email, push, etc.)'
    }
  }, {
    tableName: 'notifications',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['type']
      },
      {
        fields: ['is_read']
      },
      {
        fields: ['priority']
      },
      {
        fields: ['created_at']
      },
      {
        fields: ['expires_at']
      }
    ]
  });

  Notification.associate = function(models) {
    Notification.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
  };

  // Class methods
  Notification.createNotification = async function(userId, type, title, message, options = {}) {
    try {
      const notification = await this.create({
        user_id: userId,
        type,
        title,
        message,
        data: options.data || null,
        priority: options.priority || 'medium',
        expires_at: options.expiresAt || null,
        action_url: options.actionUrl || null,
        action_text: options.actionText || null,
        sent_via: options.sentVia || null
      });

      // Telegram notifications are now handled by the standalone bot service

      return notification;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  };

  Notification.getUnreadCount = async function(userId) {
    try {
      const count = await this.count({
        where: {
          user_id: userId,
          is_read: false,
          expires_at: {
            [sequelize.Sequelize.Op.or]: [
              null,
              { [sequelize.Sequelize.Op.gt]: new Date() }
            ]
          }
        }
      });
      return count;
    } catch (error) {
      console.error('Error getting unread count:', error);
      return 0;
    }
  };

  Notification.getUserNotifications = async function(userId, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        includeRead = true,
        type = null
      } = options;

      const whereClause = {
        user_id: userId,
        expires_at: {
          [sequelize.Sequelize.Op.or]: [
            null,
            { [sequelize.Sequelize.Op.gt]: new Date() }
          ]
        }
      };

      if (!includeRead) {
        whereClause.is_read = false;
      }

      if (type) {
        whereClause.type = type;
      }

      const notifications = await this.findAndCountAll({
        where: whereClause,
        order: [['created_at', 'DESC']],
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit)
      });

      return {
        notifications: notifications.rows,
        total: notifications.count,
        page: parseInt(page),
        totalPages: Math.ceil(notifications.count / parseInt(limit))
      };
    } catch (error) {
      console.error('Error getting user notifications:', error);
      throw error;
    }
  };

  Notification.markAsRead = async function(notificationId, userId) {
    try {
      const [updatedRows] = await this.update(
        {
          is_read: true,
          read_at: new Date()
        },
        {
          where: {
            id: notificationId,
            user_id: userId
          }
        }
      );

      return updatedRows > 0;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  };

  Notification.markAllAsRead = async function(userId) {
    try {
      const [updatedRows] = await this.update(
        {
          is_read: true,
          read_at: new Date()
        },
        {
          where: {
            user_id: userId,
            is_read: false
          }
        }
      );

      return updatedRows;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  };

  // Instance methods
  Notification.prototype.markAsRead = async function() {
    this.is_read = true;
    this.read_at = new Date();
    await this.save();
    return this;
  };

  Notification.prototype.isExpired = function() {
    return this.expires_at && new Date() > this.expires_at;
  };

  return Notification;
};
