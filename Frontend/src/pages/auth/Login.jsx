import React, { useState } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { useForm } from "react-hook-form";
import { Helmet } from "react-helmet-async";
import { Eye, EyeOff, Mail, Phone, Lock, LogIn } from "lucide-react";
import { normalizeCambodianPhone, isLikelyPhone, formatCambodianPhonePretty } from "../../utils/phone";
import useAuthStore from "../../store/authStore";
import FacebookLogin from "../../components/auth/FacebookLogin";
import ForgotPassword from "../../components/auth/ForgotPassword";
import { useTranslation } from "../../hooks/useTranslation";
import { API_ENDPOINTS } from "../../config/api";
import GoogleLogin from "../../components/auth/GoogleLogin";


const Login = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const { t } = useTranslation();
  const { login, isLoading } = useAuthStore();
  const navigate = useNavigate();
  const location = useLocation();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm();

  const onSubmit = async (data) => {
    const result = await login(data);
    if (result.success) {
      const from = location.state?.from?.pathname || "/dashboard";
      navigate(from, { replace: true });
    } else {
      setError("root", { message: result.error });
    }
  };

  const handleFacebookSuccess = (data) => {
    const from = location.state?.from?.pathname || "/dashboard";
    navigate(from, { replace: true });
  };

  const handleFacebookError = (error) => {
    console.error('Facebook login error:', error);
  };

  const handleForgotPasswordSuccess = () => {
    setShowForgotPassword(false);
    // Optionally show a success message or redirect
  };

  return (
    <>
      <Helmet>
        <title>Login - Finwise</title>
        <meta name="description" content="Login to your Finwise account" />
      </Helmet>

      <div className="min-h-screen flex items-center justify-center bg-[#e2e8ec] py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          {showForgotPassword ? (
            <ForgotPassword
              onBack={() => setShowForgotPassword(false)}
              onSuccess={handleForgotPasswordSuccess}
            />
          ) : (
            <>
          <div>
            <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-[#8da5bd]/20">
              <LogIn className="h-6 w-6 text-[#8da5bd]" />
            </div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
              Sign in to your account
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              Or{" "}
              <Link
                to="/register"
                className="font-medium text-[#476f95] hover:text-[#7593af]"
              >
                create a new account
              </Link>
            </p>
          </div>

          <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
            <div className="space-y-4">
              {/* Identifier Field (Email or Phone) */}
              <div>
                <label htmlFor="identifier" className="block text-sm font-medium text-gray-700">
                  Email or Phone
                </label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-5 w-5 text-gray-400 hidden identifier-email-icon" />
                    <Phone className="h-5 w-5 text-gray-400 hidden identifier-phone-icon" />
                  </div>
                  <input
                    {...register("identifier", {
                      required: "Email or phone is required",
                      validate: (value) => {
                        const isEmail = /@/.test(value);
                        const isPhone = /^[+]?[-()\d\s]{6,}$/.test(value);
                        return (
                          isEmail || isPhone || "Enter a valid email or phone number"
                        );
                      },
                    })}
                    type="text"
                    className="form-input pl-10"
                    placeholder="Enter email or phone"
                    onChange={(e) => {
                      let val = e.target.value;
                      if (isLikelyPhone(val)) {
                        const pretty = formatCambodianPhonePretty(val);
                        if (pretty !== val) {
                          val = pretty;
                          e.target.value = pretty;
                        }
                      }
                      // Simple dynamic icon swap (optional, classes inserted above)
                      const emailIcon = document.querySelector('.identifier-email-icon');
                      const phoneIcon = document.querySelector('.identifier-phone-icon');
                      if (emailIcon && phoneIcon) {
                        if (/@/.test(val)) {
                          emailIcon.classList.remove('hidden');
                          phoneIcon.classList.add('hidden');
                        } else if (/\d/.test(val)) {
                          phoneIcon.classList.remove('hidden');
                          emailIcon.classList.add('hidden');
                        } else {
                          emailIcon.classList.add('hidden');
                          phoneIcon.classList.add('hidden');
                        }
                      }
                    }}
                  />
                </div>
                {errors.identifier && (
                  <p className="mt-1 text-sm text-red-600">{errors.identifier.message}</p>
                )}
              </div>

              {/* Password Field */}
              <div>
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700"
                >
                  Password
                </label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    {...register("password", {
                      required: "Password is required",
                      minLength: {
                        value: 8,
                        message: "Password must be at least 8 characters",
                      },
                    })}
                    type={showPassword ? "text" : "password"}
                    className="form-input pl-10 pr-10"
                    placeholder="Enter your password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.password.message}
                  </p>
                )}
              </div>
            </div>

            {/* Remember me and Forgot password */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label
                  htmlFor="remember-me"
                  className="ml-2 block text-sm text-gray-900"
                >
                  Remember me
                </label>
              </div>

              <div className="text-sm">
                <Link
                  to="/forgot-password"
                  className="font-medium text-[#476f95] hover:text-[#7593af]"
                >
                  Forgot your password?
                </Link>
              </div>
            </div>

            {/* Error message */}
            {errors.root && (
              <div className="rounded-md bg-red-50 p-4">
                <p className="text-sm text-red-800">{errors.root.message}</p>
              </div>
            )}

            {/* Submit button */}
            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="w-full py-2 px-4 rounded-md text-white bg-[#8da5bd] hover:bg-[#7a91a7] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#8da5bd] transition-colors"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Signing in...
                  </div>
                ) : (
                  "Sign in"
                )}
              </button>
            </div>

            {/* Social login */}
            <div className="mt-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300 dark:border-gray-600" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-[#e2e8ec] dark:bg-gray-900 text-gray-500 dark:text-gray-400">
                    Or continue with
                  </span>
                </div>
              </div>

              <div className="mt-6 grid grid-cols-2 gap-3">
                <GoogleLogin />
                <div>
                  <FacebookLogin
                    onSuccess={handleFacebookSuccess}
                    onError={handleFacebookError}
                  />
                </div>
              </div>
            </div>
          </form>
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default Login;
