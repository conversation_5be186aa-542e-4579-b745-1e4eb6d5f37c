#!/bin/bash

# Deployment Monitoring and Maintenance Script
# This script helps monitor and maintain your Finwise deployment

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check service health
check_health() {
    local service_url=$1
    local service_name=$2
    
    print_status "Checking $service_name health..."
    
    if curl -f -s "$service_url" > /dev/null; then
        print_success "$service_name is healthy"
        return 0
    else
        print_error "$service_name is not responding"
        return 1
    fi
}

# Function to check Docker container status
check_container() {
    local container_name=$1
    
    if docker ps | grep -q "$container_name"; then
        print_success "Container $container_name is running"
        
        # Check container health
        local health_status=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "no-health-check")
        
        if [ "$health_status" = "healthy" ]; then
            print_success "Container $container_name is healthy"
        elif [ "$health_status" = "unhealthy" ]; then
            print_error "Container $container_name is unhealthy"
        else
            print_warning "Container $container_name has no health check configured"
        fi
        
        return 0
    else
        print_error "Container $container_name is not running"
        return 1
    fi
}

# Function to show system resources
show_resources() {
    print_status "System Resources:"
    echo "=================="
    
    # Memory usage
    echo "Memory Usage:"
    free -h
    echo ""
    
    # Disk usage
    echo "Disk Usage:"
    df -h
    echo ""
    
    # Docker stats
    if command -v docker &> /dev/null; then
        echo "Docker Container Stats:"
        docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
    fi
}

# Function to backup logs
backup_logs() {
    local backup_dir="backups/logs-$(date +%Y%m%d-%H%M%S)"
    
    print_status "Creating log backup..."
    mkdir -p "$backup_dir"
    
    # Backup Docker logs
    if docker ps | grep -q finwise-backend; then
        docker logs finwise-backend > "$backup_dir/backend.log" 2>&1
        print_success "Backend logs backed up"
    fi
    
    # Backup application logs
    if [ -d "logs" ]; then
        cp -r logs/* "$backup_dir/" 2>/dev/null || true
        print_success "Application logs backed up"
    fi
    
    # Compress backup
    tar -czf "$backup_dir.tar.gz" -C backups "$(basename "$backup_dir")"
    rm -rf "$backup_dir"
    
    print_success "Log backup created: $backup_dir.tar.gz"
}

# Function to update deployment
update_deployment() {
    print_status "Updating deployment..."
    
    # Pull latest code
    if [ -d ".git" ]; then
        git pull origin main
        print_success "Code updated"
    fi
    
    # Rebuild and restart backend
    if [ -f "docker-compose.yml" ]; then
        docker-compose down
        docker-compose build
        docker-compose up -d
        print_success "Backend updated and restarted"
    elif docker ps | grep -q finwise-backend; then
        docker stop finwise-backend
        docker rm finwise-backend
        docker build -t finwise-backend:latest .
        
        # Run with environment file
        docker run -d \
            --name finwise-backend \
            -p 5003:5003 \
            --env-file Backend/.env \
            -v "$(pwd)/Backend/uploads:/app/uploads" \
            --restart unless-stopped \
            finwise-backend:latest
        
        print_success "Backend container updated and restarted"
    fi
    
    # Wait for service to be ready
    sleep 10
    check_health "http://localhost:5003/api/health" "Backend"
}

# Main menu
echo "🔍 Finwise - Deployment Monitor & Maintenance"
echo "============================================="
echo ""
echo "What would you like to do?"
echo "1. Check service health"
echo "2. View system resources"
echo "3. View logs"
echo "4. Backup logs"
echo "5. Update deployment"
echo "6. Restart services"
echo "7. Full system check"
echo "8. Exit"
echo ""

read -p "Enter your choice (1-8): " choice

case $choice in
    1)
        print_status "Checking service health..."
        
        # Check backend health
        check_health "http://localhost:5003/api/health" "Backend API"
        
        # Check container status
        check_container "finwise-backend"
        
        # Check database connectivity (if possible)
        if docker exec finwise-backend node -e "
            const mysql = require('mysql2/promise');
            const connection = mysql.createConnection({
                host: process.env.DB_HOST,
                port: process.env.DB_PORT,
                user: process.env.DB_USER,
                password: process.env.DB_PASSWORD,
                database: process.env.DB_NAME
            });
            connection.connect().then(() => {
                console.log('Database connection successful');
                process.exit(0);
            }).catch(err => {
                console.error('Database connection failed:', err.message);
                process.exit(1);
            });
        " 2>/dev/null; then
            print_success "Database connection is healthy"
        else
            print_error "Database connection failed"
        fi
        ;;
        
    2)
        show_resources
        ;;
        
    3)
        echo "Recent Backend Logs:"
        echo "==================="
        if docker ps | grep -q finwise-backend; then
            docker logs --tail 50 finwise-backend
        else
            print_error "Backend container is not running"
        fi
        ;;
        
    4)
        backup_logs
        ;;
        
    5)
        update_deployment
        ;;
        
    6)
        print_status "Restarting services..."
        
        if docker ps | grep -q finwise-backend; then
            docker restart finwise-backend
            print_success "Backend container restarted"
            
            # Wait and check health
            sleep 10
            check_health "http://localhost:5003/api/health" "Backend"
        else
            print_error "Backend container is not running"
        fi
        ;;
        
    7)
        print_status "Performing full system check..."
        echo ""
        
        # Check services
        check_health "http://localhost:5003/api/health" "Backend API"
        check_container "finwise-backend"
        
        echo ""
        show_resources
        
        echo ""
        print_status "Recent errors in logs:"
        if docker ps | grep -q finwise-backend; then
            docker logs --tail 20 finwise-backend 2>&1 | grep -i error || print_success "No recent errors found"
        fi
        ;;
        
    8)
        print_status "Exiting..."
        exit 0
        ;;
        
    *)
        print_error "Invalid choice"
        exit 1
        ;;
esac

print_success "Operation completed!"
