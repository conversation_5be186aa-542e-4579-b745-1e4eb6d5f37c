/**
 * Configuration for Finwise Telegram Bot
 */

require('dotenv').config();

const config = {
  // Bot Configuration
  telegram: {
    botToken: process.env.TELEGRAM_BOT_TOKEN,
    webhookUrl: process.env.TELEGRAM_WEBHOOK_URL,
    webhookPath: process.env.TELEGRAM_WEBHOOK_PATH || '/telegram-webhook',
    adminChatId: process.env.TELEGRAM_ADMIN_CHAT_ID
  },

  // Server Configuration
  server: {
    port: process.env.BOT_PORT || 3002,
    host: process.env.BOT_HOST || '0.0.0.0',
    environment: process.env.NODE_ENV || 'development'
  },

  // Backend API Configuration
  api: {
    baseUrl: process.env.BACKEND_API_URL || 'http://localhost:3001',
    timeout: parseInt(process.env.API_TIMEOUT) || 30000,
    maxRetries: parseInt(process.env.API_MAX_RETRIES) || 3
  },

  // Rate Limiting
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 60000, // 1 minute
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 30,
    skipSuccessfulRequests: false,
    skipFailedRequests: false
  },

  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    maxFileSize: '5MB',
    maxFiles: 5
  },

  // Security
  security: {
    corsOrigins: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'],
    trustProxy: process.env.TRUST_PROXY === 'true'
  },

  // Database (if bot needs direct database access)
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 5432,
    database: process.env.DB_NAME || 'finwise_bot',
    username: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',
    dialect: 'postgres',
    logging: process.env.NODE_ENV === 'development'
  }
};

// Validation
const validateConfig = () => {
  const required = [
    'TELEGRAM_BOT_TOKEN'
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
};

// Validate configuration on load
validateConfig();

module.exports = config;
