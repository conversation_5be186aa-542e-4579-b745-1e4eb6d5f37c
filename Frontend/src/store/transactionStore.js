import { create } from 'zustand';
import { transactionAPI, categoryAPI } from '../services/api';
import toast from 'react-hot-toast';

const useTransactionStore = create((set, get) => ({
  // State
  transactions: [],
  categories: [],
  currentTransaction: null,
  isLoading: false,
  error: null,
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20
  },
  summary: {
    totalIncome: 0,
    totalExpense: 0,
    balance: 0,
    totalTransactions: 0
  },
  filters: {
    page: 1,
    limit: 20,
    category: '',
    type: '',
    startDate: '',
    endDate: '',
    search: '',
    sortBy: 'date',
    sortOrder: 'DESC'
  },

  // Actions
  setFilters: (newFilters) => {
    set((state) => ({
      filters: { ...state.filters, ...newFilters, page: 1 }
    }));
  },

  clearFilters: () => {
    set({
      filters: {
        page: 1,
        limit: 20,
        category: '',
        type: '',
        startDate: '',
        endDate: '',
        search: '',
        sortBy: 'date',
        sortOrder: 'DESC'
      }
    });
  },

  fetchTransactions: async (customFilters = {}) => {
    set({ isLoading: true, error: null });
    try {
      const filters = { ...get().filters, ...customFilters };
      const response = await transactionAPI.getTransactions(filters);
      const { transactions, pagination, summary } = response.data.data;

      set({
        transactions,
        pagination,
        summary,
        filters,
        isLoading: false,
        error: null
      });

      return { success: true, data: response.data.data };
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to fetch transactions';
      set({
        isLoading: false,
        error: errorMessage,
        transactions: [],
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalItems: 0,
          itemsPerPage: 20
        },
        summary: {
          totalIncome: 0,
          totalExpense: 0,
          balance: 0,
          totalTransactions: 0
        }
      });
      return { success: false, error: errorMessage };
    }
  },

  fetchTransaction: async (id) => {
    set({ isLoading: true, error: null });
    try {
      const response = await transactionAPI.getTransaction(id);
      const transaction = response.data.data;

      set({
        currentTransaction: transaction,
        isLoading: false,
        error: null
      });

      return { success: true, data: transaction };
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to fetch transaction';
      set({
        isLoading: false,
        error: errorMessage,
        currentTransaction: null
      });
      return { success: false, error: errorMessage };
    }
  },

  createTransaction: async (transactionData) => {
    set({ isLoading: true, error: null });
    try {
      const response = await transactionAPI.createTransaction(transactionData);
      const newTransaction = response.data.data;

      // Add to the beginning of transactions list
      set((state) => ({
        transactions: [newTransaction, ...state.transactions],
        isLoading: false,
        error: null
      }));

      // Refresh transactions to get updated summary
      get().fetchTransactions();

      toast.success('Transaction created successfully!');
      return { success: true, data: newTransaction };
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to create transaction';
      set({
        isLoading: false,
        error: errorMessage
      });
      return { success: false, error: errorMessage };
    }
  },

  updateTransaction: async (id, transactionData) => {
    set({ isLoading: true, error: null });
    try {
      const response = await transactionAPI.updateTransaction(id, transactionData);
      const updatedTransaction = response.data.data;

      // Update in transactions list
      set((state) => ({
        transactions: state.transactions.map(t => 
          t.id === id ? updatedTransaction : t
        ),
        currentTransaction: updatedTransaction,
        isLoading: false,
        error: null
      }));

      // Refresh transactions to get updated summary
      get().fetchTransactions();

      toast.success('Transaction updated successfully!');
      return { success: true, data: updatedTransaction };
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to update transaction';
      set({
        isLoading: false,
        error: errorMessage
      });
      return { success: false, error: errorMessage };
    }
  },

  deleteTransaction: async (id) => {
    set({ isLoading: true, error: null });
    try {
      await transactionAPI.deleteTransaction(id);

      // Remove from transactions list
      set((state) => ({
        transactions: state.transactions.filter(t => t.id !== id),
        isLoading: false,
        error: null
      }));

      // Refresh transactions to get updated summary
      get().fetchTransactions();

      toast.success('Transaction deleted successfully!');
      return { success: true };
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to delete transaction';
      set({
        isLoading: false,
        error: errorMessage
      });
      return { success: false, error: errorMessage };
    }
  },

  bulkDeleteTransactions: async (ids) => {
    set({ isLoading: true, error: null });
    try {
      const response = await transactionAPI.bulkDelete(ids);

      // Remove from transactions list
      set((state) => ({
        transactions: state.transactions.filter(t => !ids.includes(t.id)),
        isLoading: false,
        error: null
      }));

      // Refresh transactions to get updated summary
      get().fetchTransactions();

      toast.success(`${response.data.data.deletedCount} transactions deleted successfully!`);
      return { success: true, data: response.data.data };
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to delete transactions';
      set({
        isLoading: false,
        error: errorMessage
      });
      return { success: false, error: errorMessage };
    }
  },

  fetchCategories: async () => {
    try {
      const response = await categoryAPI.getCategories({ active: 'true' });
      const categories = response.data.data;

      set({ categories });
      return { success: true, data: categories };
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to fetch categories';
      console.error('Failed to fetch categories:', errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  // Utility actions
  clearCurrentTransaction: () => {
    set({ currentTransaction: null });
  },

  clearError: () => {
    set({ error: null });
  },

  setLoading: (loading) => {
    set({ isLoading: loading });
  }
}));



export default useTransactionStore;
