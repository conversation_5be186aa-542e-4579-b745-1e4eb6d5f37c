const nodemailer = require('nodemailer');

class EmailService {
  constructor() {
    this.transporter = null;
    this.initializeTransporter();
  }

  initializeTransporter() {
    try {
      // Priority order:
      // 1. Explicit SMTP_* variables
      // 2. Gmail service with GMAIL_USER + GMAIL_APP_PASSWORD
      // 3. Mock mode if MOCK_EMAIL=true
      const hasGenericSMTP =
        process.env.SMTP_HOST &&
        process.env.SMTP_PORT &&
        process.env.SMTP_USER &&
        process.env.SMTP_PASS;

      const hasGmail = process.env.GMAIL_USER && process.env.GMAIL_APP_PASSWORD;

      if (process.env.MOCK_EMAIL === 'true') {
        console.warn('📨 Email service running in MOCK mode (no real emails sent)');
        return; // transporter not needed; send methods will short‑circuit
      }

      if (hasGenericSMTP) {
        this.transporter = nodemailer.createTransport({
          host: process.env.SMTP_HOST,
            // Convert to number if possible
          port: parseInt(process.env.SMTP_PORT, 10) || 587,
          secure: process.env.SMTP_SECURE === 'true' || parseInt(process.env.SMTP_PORT, 10) === 465,
          auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASS,
          },
        });
        console.log('✅ Email service initialized (Generic SMTP)');
      } else if (hasGmail) {
        this.transporter = nodemailer.createTransport({
          service: 'gmail',
          auth: {
            user: process.env.GMAIL_USER,
            pass: process.env.GMAIL_APP_PASSWORD,
          },
        });
        console.log('✅ Email service initialized (Gmail)');
      } else {
        console.error(
          '❌ Email credentials missing. Set either SMTP_HOST/SMTP_PORT/SMTP_USER/SMTP_PASS or GMAIL_USER/GMAIL_APP_PASSWORD. Temporarily enabling MOCK_EMAIL=true.'
        );
        process.env.MOCK_EMAIL = 'true';
      }
    } catch (error) {
      console.error('❌ Failed to initialize email service:', error);
      process.env.MOCK_EMAIL = 'true';
    }
  }

  async sendOTP(email, otp, type = 'password_reset') {
    try {
  if (process.env.MOCK_EMAIL === 'true') {
        console.log(`📧 Mock Email - OTP for ${email}: ${otp}`);
        return { success: true, messageId: 'mock-message-id' };
      }

      if (!this.transporter) {
        throw new Error('Email transporter not initialized');
      }

      const subject = this.getSubject(type);
      const htmlContent = this.getHTMLContent(otp, type);

      const mailOptions = {
        from: `"Finwise" <${process.env.GMAIL_USER}>`,
        to: email,
        subject: subject,
        html: htmlContent
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log(`✅ OTP email sent successfully to ${email}`);
      
      return {
        success: true,
        messageId: result.messageId
      };
    } catch (error) {
      console.error('❌ Failed to send OTP email:', error);
      throw new Error('Failed to send OTP email');
    }
  }

  async sendPasswordResetEmail(email, otp, resetToken) {
    try {
  if (process.env.MOCK_EMAIL === 'true') {
        console.log(`📧 Mock Email - Password Reset for ${email}: OTP: ${otp}, Token: ${resetToken}`);
        return { success: true, messageId: 'mock-message-id' };
      }

      if (!this.transporter) {
        throw new Error('Email transporter not initialized');
      }

      const subject = 'Finwise - Password Reset Request';
      const htmlContent = this.getPasswordResetHTML(otp, resetToken);

      const mailOptions = {
        from: `"Finwise" <${process.env.GMAIL_USER}>`,
        to: email,
        subject: subject,
        html: htmlContent
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log(`✅ Password reset email sent successfully to ${email}`);
      
      return {
        success: true,
        messageId: result.messageId
      };
    } catch (error) {
      console.error('❌ Failed to send password reset email:', error);
      throw new Error('Failed to send password reset email');
    }
  }

  getSubject(type) {
    switch (type) {
      case 'password_reset':
        return 'Finwise - Password Reset OTP';
      case 'email_verification':
        return 'Finwise - Email Verification OTP';
      case 'login_verification':
        return 'Finwise - Login Verification OTP';
      default:
        return 'Finwise - Verification Code';
    }
  }

  getHTMLContent(otp, type) {
    const title = this.getTitle(type);
    const message = this.getMessage(type);

    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${title}</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .otp-box { background: white; border: 2px dashed #667eea; padding: 20px; text-align: center; margin: 20px 0; border-radius: 10px; }
            .otp-code { font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 5px; }
            .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🏦 Finwise</h1>
                <h2>${title}</h2>
            </div>
            <div class="content">
                <p>Hello,</p>
                <p>${message}</p>
                
                <div class="otp-box">
                    <p>Your verification code is:</p>
                    <div class="otp-code">${otp}</div>
                </div>
                
                <div class="warning">
                    <strong>⚠️ Security Notice:</strong>
                    <ul>
                        <li>This code will expire in 10 minutes</li>
                        <li>Never share this code with anyone</li>
                        <li>Finwise will never ask for this code via phone or email</li>
                    </ul>
                </div>
                
                <p>If you didn't request this code, please ignore this email or contact our support team.</p>
                
                <p>Best regards,<br>The Finwise Team</p>
            </div>
            <div class="footer">
                <p>© 2025 Finwise. All rights reserved.</p>
                <p>This is an automated message, please do not reply to this email.</p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  getPasswordResetHTML(otp, resetToken) {
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
    const resetLink = `${frontendUrl}/reset-password?token=${resetToken}`;

    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Finwise - Password Reset Request</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .option-box { background: white; border: 2px solid #667eea; padding: 20px; margin: 20px 0; border-radius: 10px; }
            .otp-box { background: white; border: 2px dashed #667eea; padding: 20px; text-align: center; margin: 20px 0; border-radius: 10px; }
            .otp-code { font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 5px; }
            .reset-button { display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; margin: 10px 0; }
            .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
            .divider { text-align: center; margin: 30px 0; font-weight: bold; color: #667eea; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🏦 Finwise</h1>
                <h2>Password Reset Request</h2>
            </div>
            <div class="content">
                <p>Hello,</p>
                <p>We received a request to reset your password. You can choose between two convenient options to reset your password:</p>
                
                <div class="option-box">
                    <h3>🔗 Option 1: One-Click Reset Link</h3>
                    <p>Click the button below to reset your password directly in your browser:</p>
                    <div style="text-align: center;">
                        <a href="${resetLink}" class="reset-button">Reset Password Now</a>
                    </div>
                    <p><small>This link will expire in 1 hour and can only be used once.</small></p>
                </div>
                
                <div class="divider">── OR ──</div>
                
                <div class="option-box">
                    <h3>🔢 Option 2: Verification Code</h3>
                    <p>Enter this verification code on the password reset page:</p>
                    <div class="otp-box">
                        <div class="otp-code">${otp}</div>
                    </div>
                    <p><small>This code will expire in 10 minutes.</small></p>
                </div>
                
                <div class="warning">
                    <strong>⚠️ Security Notice:</strong>
                    <ul>
                        <li>Both the link and code will expire soon for your security</li>
                        <li>Never share these credentials with anyone</li>
                        <li>Finwise will never ask for these via phone or email</li>
                        <li>If you didn't request this reset, please ignore this email</li>
                    </ul>
                </div>
                
                <p>If you need help, please contact our support team.</p>
                
                <p>Best regards,<br>The Finwise Team</p>
            </div>
            <div class="footer">
                <p>© 2025 Finwise. All rights reserved.</p>
                <p>This is an automated message, please do not reply to this email.</p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  getTitle(type) {
    switch (type) {
      case 'password_reset':
        return 'Password Reset Request';
      case 'email_verification':
        return 'Email Verification';
      case 'login_verification':
        return 'Login Verification';
      default:
        return 'Verification Code';
    }
  }

  getMessage(type) {
    switch (type) {
      case 'password_reset':
        return 'We received a request to reset your password. Please use the verification code below to proceed with resetting your password.';
      case 'email_verification':
        return 'Thank you for signing up with Finwise! Please use the verification code below to verify your email address.';
      case 'login_verification':
        return 'We detected a login attempt to your account. Please use the verification code below to complete the login process.';
      default:
        return 'Please use the verification code below to complete your request.';
    }
  }

  async verifyConnection() {
    try {
      if (!this.transporter) {
        throw new Error('Email transporter not initialized');
      }
      
      await this.transporter.verify();
      console.log('✅ Email service connection verified');
      return true;
    } catch (error) {
      console.error('❌ Email service connection failed:', error);
      return false;
    }
  }
}

module.exports = new EmailService();
