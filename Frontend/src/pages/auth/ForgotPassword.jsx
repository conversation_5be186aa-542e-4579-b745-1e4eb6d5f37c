import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { Helmet } from 'react-helmet-async';
import { Mail, ArrowLeft, Key, Phone, User } from 'lucide-react';
import useAuthStore from '../../store/authStore';
import { useTranslation } from 'react-i18next';

const ForgotPassword = () => {
  const { forgotPassword, verifyPasswordResetOTP, isLoading } = useAuthStore();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [requestInfo, setRequestInfo] = useState(null); // holds mode, identifier
  const [otp, setOtp] = useState('');
  const [verifying, setVerifying] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [inputType, setInputType] = useState(''); // 'email' or 'phone'

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    setValue,
    clearErrors,
  } = useForm();

  // Auto-detect input type
  const detectInputType = (value) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;

    if (emailRegex.test(value)) {
      return 'email';
    } else if (phoneRegex.test(value.replace(/[\s\-\(\)]/g, ''))) {
      return 'phone';
    }
    return '';
  };

  const handleInputChange = (e) => {
    const value = e.target.value;
    setInputValue(value);

    const type = detectInputType(value);
    setInputType(type);

    // Clear previous errors when user starts typing
    if (errors.root) {
      clearErrors('root');
    }

    // Set the appropriate field value
    if (type === 'email') {
      setValue('email', value);
      setValue('phone', '');
    } else if (type === 'phone') {
      setValue('phone', value);
      setValue('email', '');
    }
  };

  const onSubmit = async (data) => {
    const email = data.email?.trim();
    const phone = data.phone?.trim();

    if (!email && !phone) {
      setError('root', { message: 'Please enter your email address or phone number' });
      return;
    }

    // Validate input format
    if (email && !detectInputType(email)) {
      setError('root', { message: 'Please enter a valid email address' });
      return;
    }

    if (phone && detectInputType(phone) !== 'phone') {
      setError('root', { message: 'Please enter a valid phone number' });
      return;
    }

    const result = await forgotPassword(email, phone);
    if (!result.success) {
      setError('root', { message: result.error });
    } else {
      setRequestInfo(result);
    }
  };

  const handleVerifyOTP = async () => {
    if (!requestInfo?.identifier) return;
    setVerifying(true);
    const result = await verifyPasswordResetOTP(requestInfo.identifier, otp);
    setVerifying(false);
    if (result.success && result.resetToken) {
      navigate(`/reset-password?token=${result.resetToken}`);
    } else if (!result.success) {
      setError('root', { message: result.error });
    }
  };

  return (
    <>
      <Helmet>
        <title>Forgot Password - Finwise</title>
        <meta name="description" content="Reset your Finwise password" />
      </Helmet>

      <div className="min-h-screen flex items-center justify-center bg-[#e2e8ec] py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div>
            <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-[#8da5bd]/20">
              <Key className="h-6 w-6 text-[#8da5bd]" />
            </div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
              Forgot your password?
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
              Enter your email address or phone number and we'll send you a reset code.
            </p>
          </div>

          <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email address (or use phone below)
              </label>
              <div className="mt-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  {...register('email', {
                    validate: (val, formValues) => {
                      if (!val && !formValues.phone) return 'Email or phone required';
                      if (val && !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(val)) return 'Invalid email address';
                      return true;
                    },
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Invalid email address',
                    },
                  })}
                  type="email"
                  className="form-input pl-10"
                  placeholder="Enter your email"
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
              )}
              <div className="mt-4">
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700">Phone (optional)</label>
                <input
                  {...register('phone', {
                    validate: (val, formValues) => {
                      if (!val && !formValues.email) return 'Email or phone required';
                      if (val && !/^\+?[0-9\s\-()]{6,}$/.test(val)) return 'Invalid phone format';
                      return true;
                    }
                  })}
                  type="text"
                  className="form-input"
                  placeholder="+85512345678"
                />
                {errors.phone && <p className="mt-1 text-sm text-red-600">{errors.phone.message}</p>}
              </div>
            </div>

            {/* Error message */}
            {errors.root && (
              <div className="rounded-md bg-red-50 p-4">
                <p className="text-sm text-red-800">{errors.root.message}</p>
              </div>
            )}

            <div className="space-y-4">
              <button
                type="submit"
                disabled={isLoading}
                className="w-full py-2 px-4 rounded-md text-white bg-[#8da5bd] hover:bg-[#7a91a7] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#8da5bd] transition-colors"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Sending...
                  </div>
                ) : (
                  requestInfo ? 'Resend email' : 'Send email'
                )}
              </button>

              {requestInfo && (
                <div className="p-4 border rounded-md bg-gray-50 space-y-3">
                  <p className="text-sm text-gray-700">
                    Email sent. You can click the link inside the email OR enter the OTP below to continue.
                  </p>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Enter OTP</label>
                    <input
                      value={otp}
                      onChange={e => setOtp(e.target.value.replace(/\D/g, '').slice(0,6))}
                      placeholder="6-digit code"
                      className="form-input mt-1"
                    />
                  </div>
                  <button
                    type="button"
                    onClick={handleVerifyOTP}
                    disabled={verifying || otp.length !== 6}
                    className="btn btn-secondary w-full"
                  >
                    {verifying ? 'Verifying...' : 'Verify OTP'}
                  </button>
                  <p className="text-xs text-gray-500">
                    OTP expires in about {Math.ceil((requestInfo.otpExpiresIn || 600)/60)} minutes.
                  </p>
                </div>
              )}
            </div>

            <div className="text-center">
              <Link
                to="/login"
                className="inline-flex items-center text-sm font-medium text-[#476f95] hover:text-[#7593af]"
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to sign in
              </Link>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default ForgotPassword;
