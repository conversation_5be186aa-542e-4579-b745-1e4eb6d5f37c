const axios = require("axios");
const fs = require("fs");
const path = require("path");
const FormData = require("form-data");

// Test configuration
const baseURL = "http://localhost:5003";
const testImagePath = path.join(__dirname, "test-receipt.png");

console.log("🧪 Starting Comprehensive OCR Receipt Testing...\n");

// Test 1: Check if Document AI credentials are properly loaded
async function testDocumentAICredentials() {
  console.log("📋 TEST 1: Document AI Credentials Check");
  console.log("----------------------------------------");

  try {
    const response = await axios.get(`${baseURL}/api/health`);
    console.log("✅ Server is running");

    // Check environment variables
    console.log("Environment variables:");
    console.log(
      "- GOOGLE_APPLICATION_CREDENTIALS:",
      process.env.GOOGLE_APPLICATION_CREDENTIALS
    );
    console.log(
      "- DOCUMENT_AI_PROJECT_ID:",
      process.env.DOCUMENT_AI_PROJECT_ID
    );
    console.log("- DOCUMENT_AI_LOCATION:", process.env.DOCUMENT_AI_LOCATION);
    console.log(
      "- DOCUMENT_AI_PROCESSOR_ID:",
      process.env.DOCUMENT_AI_PROCESSOR_ID
    );

    // Check if JSON file exists
    const credentialsPath =
      process.env.GOOGLE_APPLICATION_CREDENTIALS ||
      path.join(__dirname, "finwise-471010-0dfebced9164.json");

    if (fs.existsSync(credentialsPath)) {
      console.log("✅ Credentials file exists:", credentialsPath);

      // Verify JSON structure
      const credentials = JSON.parse(fs.readFileSync(credentialsPath, "utf8"));
      console.log("✅ Credentials file structure:");
      console.log("  - type:", credentials.type);
      console.log("  - project_id:", credentials.project_id);
      console.log("  - client_email:", credentials.client_email);
    } else {
      console.log("❌ Credentials file not found:", credentialsPath);
    }
  } catch (error) {
    console.log("❌ Server connection failed:", error.message);
  }
  console.log("");
}

// Test 2: Create a simple test receipt image programmatically
async function createTestReceiptImage() {
  console.log("📋 TEST 2: Create Test Receipt Image");
  console.log("------------------------------------");

  // Create a simple test receipt text that we can convert to image
  const receiptText = `
FINWISE TEST STORE
123 Main Street
City, State 12345

Date: ${new Date().toLocaleDateString()}
Time: ${new Date().toLocaleTimeString()}

RECEIPT #12345

Coffee                 $4.50
Sandwich              $8.95
Tax                   $1.35
------------------------
TOTAL                $14.80

Thank you for shopping!
`;

  // For testing purposes, we'll create a simple text file that simulates receipt data
  const testReceiptData = Buffer.from(receiptText, "utf8");

  try {
    // We'll use a simple approach - create a dummy image buffer
    // In real scenario, you'd have an actual image file
    console.log("✅ Test receipt data created");
    console.log("Receipt content preview:");
    console.log(receiptText);
    return testReceiptData;
  } catch (error) {
    console.log("❌ Failed to create test receipt:", error.message);
    return null;
  }
}

// Test 3: Test OCR endpoint with different scenarios
async function testOCREndpoint() {
  console.log("📋 TEST 3: OCR Endpoint Testing");
  console.log("-------------------------------");

  const testCases = [
    {
      name: "No file uploaded",
      data: null,
      expectedError: true,
    },
    {
      name: "Valid image upload",
      data: "mock-image-data",
      expectedError: false,
    },
  ];

  for (const testCase of testCases) {
    console.log(`Testing: ${testCase.name}`);

    try {
      const formData = new FormData();

      if (testCase.data) {
        // Create a mock image buffer
        const mockImageBuffer = Buffer.from("fake-image-data");
        formData.append("receipt", mockImageBuffer, {
          filename: "test-receipt.png",
          contentType: "image/png",
        });
      }

      const response = await axios.post(
        `${baseURL}/api/ocr/process-receipt`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            "Content-Type": "multipart/form-data",
          },
          timeout: 30000, // 30 second timeout
        }
      );

      if (testCase.expectedError) {
        console.log("❌ Expected error but got success");
      } else {
        console.log("✅ OCR processing successful");
        console.log("Response data:", JSON.stringify(response.data, null, 2));
      }
    } catch (error) {
      if (testCase.expectedError) {
        console.log(
          "✅ Expected error occurred:",
          error.response?.data?.error || error.message
        );
      } else {
        console.log(
          "❌ Unexpected error:",
          error.response?.data?.error || error.message
        );
        console.log(
          "Full error details:",
          error.response?.data || error.message
        );
      }
    }
    console.log("");
  }
}

// Test 4: Test Document AI Service directly
async function testDocumentAIService() {
  console.log("📋 TEST 4: Document AI Service Direct Test");
  console.log("------------------------------------------");

  try {
    // Import the service
    const documentAiService = require("./services/documentAiService");

    console.log("✅ Document AI service imported successfully");
    console.log("Processor config:", documentAiService.processorConfig);

    // Test with mock image data
    const mockImageBuffer = Buffer.from("fake-image-data-for-testing");

    try {
      const result = await documentAiService.processReceipt(mockImageBuffer);
      console.log("✅ Document AI processing completed");
      console.log("Result preview:", JSON.stringify(result, null, 2));
    } catch (error) {
      console.log("❌ Document AI processing failed:", error.message);
      console.log("Error details:", error);
    }
  } catch (error) {
    console.log("❌ Failed to import Document AI service:", error.message);
  }
  console.log("");
}

// Test 5: Test enhanced OCR workflow
async function testEnhancedOCRWorkflow() {
  console.log("📋 TEST 5: Enhanced OCR Workflow Test");
  console.log("-------------------------------------");

  try {
    const receiptOcrService = require("./services/receiptOcrService");

    console.log("✅ Receipt OCR service imported successfully");

    // Create mock receipt data
    const mockReceiptText = `
    FINWISE GROCERY STORE
    123 Main Street, City
    
    Date: 2025-09-05
    Time: 14:30:25
    
    Items:
    - Coffee beans    $12.99
    - Milk           $3.50
    - Bread          $2.25
    - Tax            $1.87
    
    Total: $20.61
    
    Thank you!
    `;

    try {
      const result = await receiptOcrService.processImage(
        Buffer.from(mockReceiptText)
      );
      console.log("✅ Enhanced OCR processing completed");
      console.log("Extracted data:", JSON.stringify(result, null, 2));
    } catch (error) {
      console.log("❌ Enhanced OCR processing failed:", error.message);
      console.log("Error details:", error);
    }
  } catch (error) {
    console.log("❌ Failed to import Receipt OCR service:", error.message);
  }
  console.log("");
}

// Main test execution
async function runAllTests() {
  console.log("🚀 COMPREHENSIVE OCR RECEIPT TESTING SUITE");
  console.log("==========================================\n");

  await testDocumentAICredentials();
  await createTestReceiptImage();
  await testOCREndpoint();
  await testDocumentAIService();
  await testEnhancedOCRWorkflow();

  console.log("🏁 All tests completed!");
  console.log("\n📝 Summary:");
  console.log("- Check the logs above for detailed results");
  console.log("- Look for ✅ (success) and ❌ (error) indicators");
  console.log("- Fix any errors found before using the OCR feature");
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  runAllTests,
  testDocumentAICredentials,
  testOCREndpoint,
  testDocumentAIService,
  testEnhancedOCRWorkflow,
};
