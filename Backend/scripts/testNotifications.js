const { User, Notification } = require('../models');
const notificationService = require('../services/notificationService');

async function testNotifications() {
  try {
    console.log('🧪 Testing Notification System...\n');

    // Find a test user (or create one)
    let testUser = await User.findOne({ where: { email: '<EMAIL>' } });
    
    if (!testUser) {
      console.log('Creating test user...');
      testUser = await User.create({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'hashedpassword',
        role: 'Freemium'
      });
    }

    console.log(`📧 Test user: ${testUser.name} (${testUser.email})`);
    console.log(`👤 User ID: ${testUser.id}\n`);

    // Test 1: Welcome notification
    console.log('1️⃣ Testing welcome notification...');
    await notificationService.createWelcomeNotification(testUser.id, testUser.name);
    console.log('✅ Welcome notification created\n');

    // Test 2: Payment success notification
    console.log('2️⃣ Testing payment success notification...');
    await notificationService.createPaymentSuccessNotification(testUser.id, {
      amount: 1.99,
      currency: 'USD',
      planType: 'Premium'
    });
    console.log('✅ Payment success notification created\n');

    // Test 3: Usage limit warning
    console.log('3️⃣ Testing usage limit warning...');
    await notificationService.createUsageLimitWarningNotification(testUser.id, 'ocr_scan', 8, 10);
    console.log('✅ Usage limit warning notification created\n');

    // Test 4: Subscription expiring
    console.log('4️⃣ Testing subscription expiring notification...');
    await notificationService.createSubscriptionExpiringNotification(testUser.id, 3);
    console.log('✅ Subscription expiring notification created\n');

    // Test 5: Goal achieved
    console.log('5️⃣ Testing goal achieved notification...');
    await notificationService.createGoalAchievedNotification(testUser.id, {
      name: 'Emergency Fund',
      targetAmount: 1000,
      currency: 'USD'
    });
    console.log('✅ Goal achieved notification created\n');

    // Get notification statistics
    console.log('📊 Getting notification statistics...');
    const stats = await Notification.findAll({
      where: { user_id: testUser.id },
      attributes: [
        'type',
        'priority',
        'is_read',
        'title',
        'created_at'
      ],
      order: [['created_at', 'DESC']]
    });

    console.log(`\n📈 Total notifications created: ${stats.length}`);
    console.log('\n📋 Notification Details:');
    stats.forEach((notif, index) => {
      console.log(`${index + 1}. [${notif.priority.toUpperCase()}] ${notif.type}: ${notif.title}`);
      console.log(`   📅 Created: ${notif.created_at.toLocaleString()}`);
      console.log(`   👁️ Read: ${notif.is_read ? 'Yes' : 'No'}\n`);
    });

    // Test unread count
    const unreadCount = await Notification.getUnreadCount(testUser.id);
    console.log(`🔔 Unread notifications: ${unreadCount}`);

    // Test marking as read
    console.log('\n📖 Testing mark as read functionality...');
    const firstNotification = await Notification.findOne({
      where: { user_id: testUser.id },
      order: [['created_at', 'DESC']]
    });

    if (firstNotification) {
      await Notification.markAsRead(firstNotification.id, testUser.id);
      console.log(`✅ Marked notification "${firstNotification.title}" as read`);

      const newUnreadCount = await Notification.getUnreadCount(testUser.id);
      console.log(`🔔 New unread count: ${newUnreadCount}`);
    }

    console.log('\n🎉 All notification tests completed successfully!');
    console.log('\n💡 You can now test the frontend notification dropdown by:');
    console.log('   1. Logging <NAME_EMAIL>');
    console.log('   2. Clicking the notification bell icon');
    console.log('   3. Viewing the notifications in the dropdown');

  } catch (error) {
    console.error('❌ Error testing notifications:', error);
  } finally {
    process.exit(0);
  }
}

// Run the test
testNotifications();
