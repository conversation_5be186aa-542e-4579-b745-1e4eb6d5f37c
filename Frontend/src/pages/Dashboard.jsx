import React, { useState, useEffect } from "react";
import { Helmet } from "react-helmet-async";
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  CreditCard,
  PiggyBank,
  Target,
  Plus,
  RefreshCw,
  AlertTriangle,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { dashboardAPI } from "../services/api";
import { useTranslation } from "../hooks/useTranslation";

const Dashboard = () => {
  const { t } = useTranslation();
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [insights, setInsights] = useState([]);

  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      const [statsResponse, insightsResponse] = await Promise.all([
        dashboardAPI.getStats(),
        dashboardAPI.getInsights(),
      ]);

      if (statsResponse.success) {
        setDashboardData(statsResponse.data);
      }

      if (insightsResponse.success) {
        setInsights(insightsResponse.data.insights);
      }
    } catch (error) {
      console.error("Dashboard data fetch error:", error);
      toast.error("Failed to load dashboard data");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center space-x-2">
          <RefreshCw className="w-6 h-6 animate-spin text-primary-600" />
          <span className="text-gray-600 dark:text-gray-400">
            {t("common.loading")}
          </span>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Failed to load dashboard
          </h3>
          <button
            onClick={fetchDashboardData}
            className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  const stats = [
    {
      name: "Total Balance",
      value: dashboardData.totalBalance.formatted,
      change: null,
      changeType:
        dashboardData.totalBalance.value >= 0 ? "positive" : "negative",
      icon: DollarSign,
    },
    {
      name: "Monthly Income",
      value: dashboardData.monthlyIncome.formatted,
      change: `${dashboardData.monthlyIncome.change >= 0 ? "+" : ""}${
        dashboardData.monthlyIncome.change
      }%`,
      changeType: dashboardData.monthlyIncome.changeType,
      icon: TrendingUp,
    },
    {
      name: "Monthly Expenses",
      value: dashboardData.monthlyExpenses.formatted,
      change: `${dashboardData.monthlyExpenses.change >= 0 ? "+" : ""}${
        dashboardData.monthlyExpenses.change
      }%`,
      changeType: dashboardData.monthlyExpenses.changeType,
      icon: TrendingDown,
    },
    {
      name: "Budget Used",
      value: `${dashboardData.budget.percentage}%`,
      change: `$${dashboardData.budget.remaining.toFixed(2)} left`,
      changeType:
        dashboardData.budget.percentage > 90 ? "negative" : "positive",
      icon: PiggyBank,
    },
  ];

  return (
    <>
      <Helmet>
        <title>{t("dashboard.title")} - Finwise</title>
        <meta
          name="description"
          content="Your financial overview and insights"
        />
      </Helmet>

      <div className="space-y-6  p-4 md:p-6 bg-[#e2e8ec] rounded-xl">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {t("dashboard.title")}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Welcome back! Here's your financial overview.
            </p>
          </div>
          <button className="flex items-center gap-2 px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700 transition shadow">
            <Plus className="w-4 h-4 mr-2" />
            {t("dashboard.addTransaction")}
          </button>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat) => (
            <div key={stat.name} className="card">
              <div className="card-content">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      {stat.name}
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {stat.value}
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center">
                    <stat.icon className="h-6 w-6 text-primary-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center">
                  <span
                    className={`text-sm font-medium ${
                      stat.changeType === "positive"
                        ? "text-green-600"
                        : "text-red-600"
                    }`}
                  >
                    {stat.change}
                  </span>
                  <span className="text-sm text-gray-500 ml-2">
                    from last month
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Transactions */}
          <div className="lg:col-span-2">
            <div className="card">
              <div className="card-header">
                <div className="flex items-center justify-between">
                  <h3 className="card-title">Recent Transactions</h3>
                  <button className="text-sm text-primary-600 hover:text-primary-500">
                    View all
                  </button>
                </div>
              </div>
              <div className="card-content">
                <div className="space-y-4">
                  {dashboardData.recentTransactions.length > 0 ? (
                    dashboardData.recentTransactions.map((transaction) => (
                      <div
                        key={transaction.id}
                        className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0"
                      >
                        <div className="flex items-center">
                          <div
                            className="h-10 w-10 rounded-lg flex items-center justify-center mr-3"
                            style={{
                              backgroundColor: transaction.category?.color
                                ? `${transaction.category.color}20`
                                : "#f3f4f6",
                              color: transaction.category?.color || "#6b7280",
                            }}
                          >
                            <CreditCard className="h-5 w-5" />
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-900">
                              {transaction.details || "Transaction"}
                            </p>
                            <p className="text-xs text-gray-500">
                              {new Date(transaction.date).toLocaleDateString()}{" "}
                              • {transaction.category?.name || "Uncategorized"}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p
                            className={`text-sm font-medium ${
                              transaction.type === "income"
                                ? "text-green-600"
                                : "text-red-600"
                            }`}
                          >
                            {transaction.type === "income" ? "+" : "-"}
                            {new Intl.NumberFormat("en-US", {
                              style: "currency",
                              currency: transaction.currency || "USD",
                            }).format(transaction.amount)}
                          </p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500">No recent transactions</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Insights & Quick Actions */}
          <div className="space-y-6">
            {/* Financial Insights */}
            {insights.length > 0 && (
              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">Financial Insights</h3>
                </div>
                <div className="card-content">
                  <div className="space-y-3">
                    {insights.map((insight, index) => (
                      <div
                        key={index}
                        className={`p-3 rounded-lg border ${
                          insight.type.includes("warning")
                            ? "bg-yellow-50 border-yellow-200"
                            : "bg-blue-50 border-blue-200"
                        }`}
                      >
                        <div className="flex items-start">
                          <div
                            className={`p-1 rounded-full mr-3 ${
                              insight.type.includes("warning")
                                ? "bg-yellow-100"
                                : "bg-blue-100"
                            }`}
                          >
                            {insight.type.includes("warning") ? (
                              <AlertTriangle className="w-4 h-4 text-yellow-600" />
                            ) : (
                              <TrendingUp className="w-4 h-4 text-blue-600" />
                            )}
                          </div>
                          <div className="flex-1">
                            <h4
                              className={`text-sm font-medium ${
                                insight.type.includes("warning")
                                  ? "text-yellow-900"
                                  : "text-blue-900"
                              }`}
                            >
                              {insight.title}
                            </h4>
                            <p
                              className={`text-xs mt-1 ${
                                insight.type.includes("warning")
                                  ? "text-yellow-700"
                                  : "text-blue-700"
                              }`}
                            >
                              {insight.message}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Quick Actions */}
            <div className="card">
              <div className="card-header">
                <h3 className="card-title">Quick Actions</h3>
              </div>
              <div className="card-content">
                <div className="space-y-3">
                  <button className="w-full btn btn-outline text-left">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Transaction
                  </button>
                  <button className="w-full btn btn-outline text-left">
                    <PiggyBank className="w-4 h-4 mr-2" />
                    Create Budget
                  </button>
                  <button className="w-full btn btn-outline text-left">
                    <Target className="w-4 h-4 mr-2" />
                    Set Goal
                  </button>
                </div>
              </div>
            </div>

            {/* Goals Summary */}
            <div className="card">
              <div className="card-header">
                <h3 className="card-title">Goals Summary</h3>
              </div>
              <div className="card-content">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary-600">
                      {dashboardData.goals.active}
                    </div>
                    <div className="text-sm text-gray-500">Active Goals</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {dashboardData.goals.completed}
                    </div>
                    <div className="text-sm text-gray-500">Completed</div>
                  </div>
                </div>
                {dashboardData.goals.total === 0 && (
                  <div className="text-center py-4">
                    <Target className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-500">No goals set yet</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Dashboard;
