const { Notification, User } = require('../models');

class NotificationService {
  constructor() {
    console.log('📢 Notification Service initialized');
  }

  /**
   * Create a payment success notification
   */
  async createPaymentSuccessNotification(userId, paymentData) {
    try {
      const { amount, currency, planType } = paymentData;
      
      await Notification.createNotification(
        userId,
        'payment_success',
        'Payment Successful! 🎉',
        `Your payment of ${amount} ${currency} has been processed successfully. Your account has been upgraded to ${planType}.`,
        {
          priority: 'high',
          actionUrl: '/billing',
          actionText: 'View Billing',
          data: paymentData
        }
      );

      console.log(`✅ Payment success notification created for user: ${userId}`);
    } catch (error) {
      console.error('Error creating payment success notification:', error);
    }
  }

  /**
   * Create a payment failed notification
   */
  async createPaymentFailedNotification(userId, paymentData) {
    try {
      const { amount, currency, reason } = paymentData;
      
      await Notification.createNotification(
        userId,
        'payment_failed',
        'Payment Failed ❌',
        `Your payment of ${amount} ${currency} could not be processed. ${reason || 'Please try again or contact support.'}`,
        {
          priority: 'high',
          actionUrl: '/billing',
          actionText: 'Try Again',
          data: paymentData
        }
      );

      console.log(`❌ Payment failed notification created for user: ${userId}`);
    } catch (error) {
      console.error('Error creating payment failed notification:', error);
    }
  }

  /**
   * Create subscription expiring notification
   */
  async createSubscriptionExpiringNotification(userId, daysUntilExpiry) {
    try {
      const title = `Subscription Expiring Soon ⏰`;
      const message = `Your Premium subscription will expire in ${daysUntilExpiry} day${daysUntilExpiry > 1 ? 's' : ''}. Renew now to continue enjoying unlimited access to all features.`;
      
      await Notification.createNotification(
        userId,
        'subscription_expiring',
        title,
        message,
        {
          priority: 'high',
          actionUrl: '/billing',
          actionText: 'Renew Now',
          data: { daysUntilExpiry }
        }
      );

      console.log(`⏰ Subscription expiring notification created for user: ${userId} (${daysUntilExpiry} days)`);
    } catch (error) {
      console.error('Error creating subscription expiring notification:', error);
    }
  }

  /**
   * Create subscription expired notification
   */
  async createSubscriptionExpiredNotification(userId) {
    try {
      await Notification.createNotification(
        userId,
        'subscription_expired',
        'Subscription Expired 📅',
        'Your Premium subscription has expired. You have been downgraded to the Freemium plan. Upgrade again to continue enjoying unlimited access to all features.',
        {
          priority: 'high',
          actionUrl: '/billing',
          actionText: 'Upgrade Now'
        }
      );

      console.log(`📅 Subscription expired notification created for user: ${userId}`);
    } catch (error) {
      console.error('Error creating subscription expired notification:', error);
    }
  }

  /**
   * Create usage limit reached notification
   */
  async createUsageLimitReachedNotification(userId, featureType, limit) {
    try {
      const featureNames = {
        'ocr_scan': 'OCR scans',
        'excel_import': 'Excel imports'
      };

      const featureName = featureNames[featureType] || featureType;
      
      await Notification.createNotification(
        userId,
        'usage_limit_reached',
        'Usage Limit Reached 🚫',
        `You have reached your monthly limit of ${limit} ${featureName}. Upgrade to Premium for unlimited access.`,
        {
          priority: 'medium',
          actionUrl: '/billing',
          actionText: 'Upgrade to Premium',
          data: { featureType, limit }
        }
      );

      console.log(`🚫 Usage limit reached notification created for user: ${userId} (${featureType})`);
    } catch (error) {
      console.error('Error creating usage limit reached notification:', error);
    }
  }

  /**
   * Create usage limit warning notification
   */
  async createUsageLimitWarningNotification(userId, featureType, currentUsage, limit) {
    try {
      const featureNames = {
        'ocr_scan': 'OCR scans',
        'excel_import': 'Excel imports'
      };

      const featureName = featureNames[featureType] || featureType;
      const remaining = limit - currentUsage;
      
      await Notification.createNotification(
        userId,
        'usage_limit_warning',
        'Usage Limit Warning ⚠️',
        `You have ${remaining} ${featureName} remaining this month. Consider upgrading to Premium for unlimited access.`,
        {
          priority: 'low',
          actionUrl: '/billing',
          actionText: 'View Plans',
          data: { featureType, currentUsage, limit, remaining }
        }
      );

      console.log(`⚠️ Usage limit warning notification created for user: ${userId} (${featureType})`);
    } catch (error) {
      console.error('Error creating usage limit warning notification:', error);
    }
  }

  /**
   * Create goal achieved notification
   */
  async createGoalAchievedNotification(userId, goalData) {
    try {
      const { name, targetAmount, currency } = goalData;
      
      await Notification.createNotification(
        userId,
        'goal_achieved',
        'Goal Achieved! 🎯',
        `Congratulations! You have successfully achieved your goal "${name}" of ${targetAmount} ${currency}.`,
        {
          priority: 'medium',
          actionUrl: '/goals',
          actionText: 'View Goals',
          data: goalData
        }
      );

      console.log(`🎯 Goal achieved notification created for user: ${userId}`);
    } catch (error) {
      console.error('Error creating goal achieved notification:', error);
    }
  }

  /**
   * Create budget exceeded notification
   */
  async createBudgetExceededNotification(userId, budgetData) {
    try {
      const { name, amount, spent, currency } = budgetData;
      const percentage = Math.round((spent / amount) * 100);
      
      await Notification.createNotification(
        userId,
        'budget_exceeded',
        'Budget Exceeded! 💸',
        `Your budget "${name}" has been exceeded. You've spent ${spent} ${currency} out of ${amount} ${currency} (${percentage}%).`,
        {
          priority: 'medium',
          actionUrl: '/budgets',
          actionText: 'View Budgets',
          data: budgetData
        }
      );

      console.log(`💸 Budget exceeded notification created for user: ${userId}`);
    } catch (error) {
      console.error('Error creating budget exceeded notification:', error);
    }
  }

  /**
   * Create welcome notification for new users
   */
  async createWelcomeNotification(userId, userName) {
    try {
      await Notification.createNotification(
        userId,
        'welcome',
        'Welcome to Finwise! 👋',
        `Hi ${userName}! Welcome to Finwise. Start by adding your first transaction or importing your bank statements to get insights into your finances.`,
        {
          priority: 'medium',
          actionUrl: '/dashboard',
          actionText: 'Get Started'
        }
      );

      console.log(`👋 Welcome notification created for user: ${userId}`);
    } catch (error) {
      console.error('Error creating welcome notification:', error);
    }
  }

  /**
   * Create system maintenance notification
   */
  async createSystemMaintenanceNotification(userId, maintenanceData) {
    try {
      const { startTime, endTime, description } = maintenanceData;
      
      await Notification.createNotification(
        userId,
        'system_maintenance',
        'Scheduled Maintenance 🔧',
        `Finwise will undergo scheduled maintenance from ${startTime} to ${endTime}. ${description || 'Some features may be temporarily unavailable.'}`,
        {
          priority: 'medium',
          data: maintenanceData
        }
      );

      console.log(`🔧 System maintenance notification created for user: ${userId}`);
    } catch (error) {
      console.error('Error creating system maintenance notification:', error);
    }
  }

  /**
   * Create security alert notification
   */
  async createSecurityAlertNotification(userId, alertData) {
    try {
      const { type, description, ipAddress, timestamp } = alertData;
      
      await Notification.createNotification(
        userId,
        'security_alert',
        'Security Alert 🔒',
        `Security alert: ${description}. If this wasn't you, please secure your account immediately.`,
        {
          priority: 'urgent',
          actionUrl: '/settings',
          actionText: 'Review Security',
          data: alertData
        }
      );

      console.log(`🔒 Security alert notification created for user: ${userId}`);
    } catch (error) {
      console.error('Error creating security alert notification:', error);
    }
  }
}

module.exports = new NotificationService();
