import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { Helmet } from 'react-helmet-async';
import { Mail, ArrowLeft, Key, Phone, User } from 'lucide-react';
import useAuthStore from '../../store/authStore';
import { useTranslation } from 'react-i18next';

const ForgotPassword = () => {
  const { forgotPassword, verifyPasswordResetOTP, isLoading } = useAuthStore();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [requestInfo, setRequestInfo] = useState(null); // holds mode, identifier
  const [otp, setOtp] = useState('');
  const [verifying, setVerifying] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [inputType, setInputType] = useState(''); // 'email' or 'phone'

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    setValue,
    clearErrors,
  } = useForm();

  // Enhanced auto-detect input type
  const detectInputType = (value) => {
    if (!value || value.length < 3) return '';

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{7,15}$/;
    const cleanValue = value.replace(/[\s\-\(\)]/g, '');

    // Check for email pattern
    if (value.includes('@') && emailRegex.test(value)) {
      return 'email';
    }

    // Check for phone pattern (more flexible)
    if (/^[\+]?[0-9]/.test(cleanValue) && phoneRegex.test(value) && cleanValue.length >= 7) {
      return 'phone';
    }

    // Partial detection for better UX
    if (value.includes('@')) {
      return 'email-partial';
    }

    if (/^[\+]?[0-9]/.test(value)) {
      return 'phone-partial';
    }

    return '';
  };

  const handleInputChange = (e) => {
    const value = e.target.value;
    setInputValue(value);

    const type = detectInputType(value);
    setInputType(type);

    // Clear previous errors when user starts typing
    if (errors.root) {
      clearErrors('root');
    }

    // Set the appropriate field value
    if (type === 'email') {
      setValue('email', value);
      setValue('phone', '');
    } else if (type === 'phone') {
      setValue('phone', value);
      setValue('email', '');
    }
  };

  const onSubmit = async (data) => {
    const email = data.email?.trim();
    const phone = data.phone?.trim();

    if (!email && !phone) {
      setError('root', { message: 'Please enter your email address or phone number' });
      return;
    }

    // Validate input format
    if (email && !detectInputType(email)) {
      setError('root', { message: 'Please enter a valid email address' });
      return;
    }

    if (phone && detectInputType(phone) !== 'phone') {
      setError('root', { message: 'Please enter a valid phone number' });
      return;
    }

    const result = await forgotPassword(email, phone);
    if (!result.success) {
      setError('root', { message: result.error });
    } else {
      setRequestInfo(result);
    }
  };

  const handleVerifyOTP = async () => {
    if (!requestInfo?.identifier) return;
    setVerifying(true);
    const result = await verifyPasswordResetOTP(requestInfo.identifier, otp);
    setVerifying(false);
    if (result.success && result.resetToken) {
      navigate(`/reset-password?token=${result.resetToken}`);
    } else if (!result.success) {
      setError('root', { message: result.error });
    }
  };

  return (
    <>
      <Helmet>
        <title>Forgot Password - Finwise</title>
        <meta name="description" content="Reset your Finwise password" />
      </Helmet>

      <div className="min-h-screen flex items-center justify-center bg-[#e2e8ec] py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div>
            <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-[#8da5bd]/20">
              <Key className="h-6 w-6 text-[#8da5bd]" />
            </div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
              Forgot your password?
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
              Enter your email address or phone number and we'll send you a reset code.
            </p>
          </div>

          <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Email or Phone Number
              </label>
              <div className="relative">
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                  {inputType === 'email' || inputType === 'email-partial' ? (
                    <Mail className="h-5 w-5 text-green-500" />
                  ) : inputType === 'phone' || inputType === 'phone-partial' ? (
                    <Phone className="h-5 w-5 text-blue-500" />
                  ) : (
                    <User className="h-5 w-5 text-gray-400" />
                  )}
                </div>
                <input
                  {...register('identifier', {
                    required: 'Email or phone number is required',
                    validate: (value) => {
                      const type = detectInputType(value);
                      if (type === 'email') return true;
                      if (type === 'phone') return true;
                      return 'Please enter a valid email address or phone number';
                    }
                  })}
                  type="text"
                  value={inputValue}
                  onChange={handleInputChange}
                  className={`w-full pl-10 pr-20 py-3 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-300 ${
                    inputType === 'email' ? 'border-green-300 focus:ring-green-500' :
                    inputType === 'phone' ? 'border-blue-300 focus:ring-blue-500' :
                    inputType.includes('partial') ? 'border-yellow-300 focus:ring-yellow-500' :
                    'border-gray-300 dark:border-gray-600 focus:ring-blue-500'
                  }`}
                  placeholder="Enter your email or phone number"
                />

                {/* Detection indicator */}
                {inputType && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      inputType === 'email' ? 'bg-green-100 text-green-800' :
                      inputType === 'phone' ? 'bg-blue-100 text-blue-800' :
                      inputType.includes('partial') ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {inputType === 'email' ? '📧 Email' :
                       inputType === 'phone' ? '📱 Phone' :
                       inputType === 'email-partial' ? '📧 Email...' :
                       inputType === 'phone-partial' ? '📱 Phone...' :
                       'Detecting...'}
                    </span>
                  </div>
                )}
              </div>

              {errors.identifier && (
                <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                  {errors.identifier.message}
                </p>
              )}

              {/* Helper text */}
              <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                We'll automatically detect whether you entered an email or phone number
              </p>
            </div>

            {/* Error message */}
            {errors.root && (
              <div className="rounded-md bg-red-50 p-4">
                <p className="text-sm text-red-800">{errors.root.message}</p>
              </div>
            )}

            <div className="space-y-4">
              <button
                type="submit"
                disabled={isLoading}
                className="w-full py-2 px-4 rounded-md text-white bg-[#8da5bd] hover:bg-[#7a91a7] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#8da5bd] transition-colors"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Sending...
                  </div>
                ) : (
                  requestInfo ? 'Resend email' : 'Send email'
                )}
              </button>

              {requestInfo && (
                <div className="p-4 border rounded-md bg-gray-50 space-y-3">
                  <p className="text-sm text-gray-700">
                    Email sent. You can click the link inside the email OR enter the OTP below to continue.
                  </p>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Enter OTP</label>
                    <input
                      value={otp}
                      onChange={e => setOtp(e.target.value.replace(/\D/g, '').slice(0,6))}
                      placeholder="6-digit code"
                      className="form-input mt-1"
                    />
                  </div>
                  <button
                    type="button"
                    onClick={handleVerifyOTP}
                    disabled={verifying || otp.length !== 6}
                    className="btn btn-secondary w-full"
                  >
                    {verifying ? 'Verifying...' : 'Verify OTP'}
                  </button>
                  <p className="text-xs text-gray-500">
                    OTP expires in about {Math.ceil((requestInfo.otpExpiresIn || 600)/60)} minutes.
                  </p>
                </div>
              )}
            </div>

            <div className="text-center">
              <Link
                to="/login"
                className="inline-flex items-center text-sm font-medium text-[#476f95] hover:text-[#7593af]"
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to sign in
              </Link>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default ForgotPassword;
