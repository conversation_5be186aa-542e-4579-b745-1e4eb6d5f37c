const admin = require('firebase-admin');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyA1YKLPXrZNx8LLVSAV5dbpO3RxfPjBvCQ",
  authDomain: "finwise-cd63e.firebaseapp.com",
  projectId: "finwise-cd63e",
  storageBucket: "finwise-cd63e.firebasestorage.app",
  messagingSenderId: "************",
  appId: "1:************:web:361e215af4b141aee7181f",
  measurementId: "G-Q1VPM5PNEY"
};

// Initialize Firebase Admin SDK
// Note: For production, you should use a service account key file
// For now, we'll use the web config (limited functionality)
let firebaseApp;

try {
  // Check if Firebase is already initialized
  firebaseApp = admin.app();
} catch (error) {
  // Initialize Firebase if not already done
  firebaseApp = admin.initializeApp({
    credential: admin.credential.applicationDefault(),
    projectId: firebaseConfig.projectId
  });
}

module.exports = {
  firebaseConfig,
  firebaseApp,
  admin
};
