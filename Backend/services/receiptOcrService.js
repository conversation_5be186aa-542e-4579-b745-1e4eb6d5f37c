const documentAiService = require("./documentAiService");
const path = require("path");

/**
 * Receipt OCR Service following the exact 6-step workflow:
 * 1. Extract Text from Receipt using Vision API
 * 2. Send receipt image and retrieve raw OCR text
 * 3. Parse the Extracted Text (split into lines, search keywords)
 * 4. Handle Variations (fuzzy matching, bounding boxes)
 * 5. Optional Enhancements (store name, date extraction)
 * 6. Output Structured Data as JSON
 */
class ReceiptOcrService {
  constructor() {
    // Step 3: Keywords for total detection
    this.totalKeywords = [
      "total",
      "grand total",
      "amount due",
      "balance due",
      "total due",
      "total amount",
      "net amount",
      "final amount",
      "amount payable",
      "sum total",
      "amount",
      "due",
      // Swedish/Nordic keywords
      "totalt",
      "slutsumma",
      "summa",
      "att betala",
      "belopp",
      "totalsumma",
      "totalbelopp",
      // Norwegian keywords
      "totalsum",
      "til betaling",
      // Danish keywords
      "total beløb",
      "i alt",
      "beløb",
    ];

    // Step 4: Currency formats
    this.currencyPatterns = [
      {
        symbol: "$",
        code: "USD",
        regex: /\$\s*(\d+(?:,\d{3})*(?:\.\d{2})?)/gi,
      },
      { symbol: "៛", code: "KHR", regex: /៛\s*(\d+(?:,\d{3})*(?:\.\d{2})?)/gi },
      { symbol: "₫", code: "VND", regex: /₫\s*(\d+(?:,\d{3})*(?:\.\d{2})?)/gi },
      {
        symbol: "RM",
        code: "MYR",
        regex: /RM\s*(\d+(?:,\d{3})*(?:\.\d{2})?)/gi,
      },
      // Nordic currencies
      {
        symbol: "Kr",
        code: "SEK",
        regex: /(\d+(?:,\d{3})*(?:\.\d{2})?)\s*Kr/gi,
      },
      {
        symbol: "kr",
        code: "SEK",
        regex: /(\d+(?:,\d{3})*(?:\.\d{2})?)\s*kr/gi,
      },
      {
        symbol: "SEK",
        code: "SEK",
        regex: /(\d+(?:,\d{3})*(?:\.\d{2})?)\s*SEK/gi,
      },
      {
        symbol: "NOK",
        code: "NOK",
        regex: /(\d+(?:,\d{3})*(?:\.\d{2})?)\s*NOK/gi,
      },
      {
        symbol: "DKK",
        code: "DKK",
        regex: /(\d+(?:,\d{3})*(?:\.\d{2})?)\s*DKK/gi,
      },
      { symbol: "€", code: "EUR", regex: /€\s*(\d+(?:,\d{3})*(?:\.\d{2})?)/gi },
    ];

    // Generic amount pattern
    this.amountRegex = /(\d+(?:,\d{3})*(?:\.\d{2})?)/g;
  }

  /**
   * MAIN METHOD: Process receipt image following 6-step workflow
   */
  async processImage(imageBuffer) {
    console.log("Starting 6-Step Receipt OCR Workflow...");

    try {
      // STEP 1 & 2: Extract Text from Receipt using Vision API
      const ocrText = await this.extractTextFromReceipt(imageBuffer);

      // STEP 3: Parse the Extracted Text
      const parsedData = this.parseExtractedText(
        ocrText.text,
        ocrText.annotations
      );

      // STEP 4: Handle Variations with fuzzy matching
      const enhancedData = this.handleVariations(parsedData);

      // STEP 5: Optional Enhancements (store name, date)
      const storeInfo = this.extractStoreAndDate(ocrText.text);

      // STEP 6: Output Structured Data
      const structuredData = this.outputStructuredData({
        ...enhancedData,
        ...storeInfo,
        usingMockService: ocrText.usingMockService,
      });

      return {
        success: true,
        data: structuredData,
      };
    } catch (error) {
      console.error("❌ OCR Processing failed:", error.message);
      return {
        success: false,
        data: {
          error: error.message,
          step: "unknown",
        },
      };
    }
  }

  /**
   * STEP 1 & 2: Extract Text from Receipt using Document AI
   */
  async extractTextFromReceipt(imageBuffer) {
    console.log("📡 STEP 1-2: Extracting text using Document AI...");

    let usingMockService = false;

    try {
      // Use Document AI
      const documentAiResult = await documentAiService.processReceipt(
        imageBuffer,
        "image/jpeg"
      );

      if (!documentAiResult.success || !documentAiResult.data.raw_text) {
        throw new Error("No text detected in image");
      }

      console.log("Document AI successful - text extracted");
      return {
        text: documentAiResult.data.raw_text,
        annotations: [
          {
            description: documentAiResult.data.raw_text,
            boundingPoly: {
              vertices: [
                { x: 0, y: 0 },
                { x: 1000, y: 0 },
                { x: 1000, y: 1000 },
                { x: 0, y: 1000 },
              ],
            },
          },
        ],
        usingMockService: false,
      };
    } catch (error) {
      console.log(`Document AI failed: ${error.message}`);
      console.log("Using mock OCR service...");

      // Fallback to mock service
      const mockResult = this.getMockOCRResult();
      return {
        text: mockResult.text,
        annotations: mockResult.annotations,
        usingMockService: true,
      };
    }
  }

  /**
   * STEP 3: Parse the Extracted Text
   */
  parseExtractedText(ocrText, annotations) {
    console.log("STEP 3: Parsing extracted text...");

    // Split OCR text into lines
    const lines = ocrText
      .split("\n")
      .map((line) => line.trim())
      .filter((line) => line.length > 0);
    console.log(`Split into ${lines.length} lines`);

    // Search for keywords and extract amounts
    const totalCandidates = [];

    lines.forEach((line, index) => {
      const lowerLine = line.toLowerCase();

      // Check if line contains total keywords
      const hasKeyword = this.totalKeywords.some((keyword) =>
        lowerLine.includes(keyword.toLowerCase())
      );

      if (hasKeyword) {
        // Extract numeric values from this line
        const amounts = this.extractAmountsFromLine(line);
        amounts.forEach((amount) => {
          totalCandidates.push({
            line: line,
            lineIndex: index,
            amount: amount.value,
            currency: amount.currency,
            keyword: this.findMatchingKeyword(lowerLine),
            confidence: this.calculateKeywordConfidence(lowerLine),
          });
        });
      }
    });

    console.log(`Found ${totalCandidates.length} total candidates`);
    return { lines, totalCandidates };
  }

  /**
   * STEP 4: Handle Variations using fuzzy matching
   */
  handleVariations(parsedData) {
    console.log("STEP 4: Handling variations and improving accuracy...");

    if (parsedData.totalCandidates.length === 0) {
      // Try fuzzy matching if no exact matches found
      return this.fuzzyMatchTotal(parsedData.lines);
    }

    // Sort by confidence and select best match
    const bestMatch = parsedData.totalCandidates.sort(
      (a, b) => b.confidence - a.confidence
    )[0];

    console.log(
      `Best match: ${bestMatch.keyword} -> ${bestMatch.currency}${bestMatch.amount}`
    );

    return {
      total: bestMatch.amount.toString(),
      currency: bestMatch.currency,
      keyword: bestMatch.keyword,
      confidence: bestMatch.confidence,
      line: bestMatch.line,
    };
  }

  /**
   * STEP 5: Optional Enhancements - Extract store name and date
   */
  extractStoreAndDate(ocrText) {
    console.log("✨ STEP 5: Extracting store name and date...");

    const lines = ocrText
      .split("\n")
      .map((line) => line.trim())
      .filter((line) => line.length > 0);

    // Extract store name (usually first few lines)
    const storeName = this.extractStoreName(lines);

    // Extract date using regex patterns
    const date = this.extractDate(ocrText);

    console.log(`Store: ${storeName || "Not found"}`);
    console.log(`Date: ${date || "Not found"}`);

    return { store: storeName, date: date };
  }

  /**
   * STEP 6: Output Structured Data as JSON
   */
  outputStructuredData(data) {
    console.log("STEP 6: Generating structured JSON output...");

    const structuredData = {
      // Core extracted data
      store: data.store || "Unknown",
      date: data.date || new Date().toISOString().split("T")[0],
      total: data.total || "0.00",
      currency: data.currency || "USD",

      // Additional metadata
      extraction_method: data.usingMockService ? "mock_service" : "vision_api",
      confidence: data.confidence || 0,
      keyword_found: data.keyword || null,
      source_line: data.line || null,

      // For compatibility with existing system
      total_detection: {
        keyword: data.keyword || "total",
        amount_text: `${data.currency || "$"}${data.total || "0.00"}`,
        amount_value: parseFloat(data.total || "0"),
        currency: data.currency || "USD",
        confidence: data.confidence || 0,
        method: data.usingMockService ? "mock_extraction" : "vision_extraction",
        bbox: { x_min: 0, y_min: 0, x_max: 100, y_max: 20 },
      },

      // Enhanced extractions
      date_extraction: {
        found: !!data.date,
        date: data.date || null,
        formatted_date: data.date || new Date().toISOString().split("T")[0],
        confidence: data.date ? 0.9 : 0,
        source: "regex_pattern",
      },

      description_extraction: {
        found: !!data.store,
        description: data.store || "Receipt processed via OCR",
        merchant_name: data.store || null,
        business_type: this.identifyBusinessType(data.store || ""),
        confidence: data.store ? 0.8 : 0,
        source: "header_analysis",
      },

      // Suggested transaction
      suggested_transaction: {
        date: data.date || new Date().toISOString().split("T")[0],
        details: data.store || "Receipt processed via OCR",
        money_out: parseFloat(data.total || "0"),
        currency: data.currency || "USD",
        imported_from: "ocr",
        merchant_name: data.store || null,
        business_type: this.identifyBusinessType(data.store || ""),
        ocr_confidence: {
          total: data.confidence || 0,
          date: data.date ? 0.9 : 0,
          description: data.store ? 0.8 : 0,
        },
      },
    };

    console.log(
      `Structured data generated: ${JSON.stringify(structuredData, null, 2)}`
    );
    return structuredData;
  }

  /**
   * Helper Methods
   */

  extractAmountsFromLine(line) {
    const amounts = [];

    // Try currency-specific patterns first
    for (const pattern of this.currencyPatterns) {
      const matches = [...line.matchAll(pattern.regex)];
      matches.forEach((match) => {
        amounts.push({
          value: parseFloat(match[1].replace(/,/g, "")),
          currency: pattern.code,
          text: match[0],
        });
      });
    }

    // If no currency-specific matches, try generic pattern
    if (amounts.length === 0) {
      const matches = [...line.matchAll(this.amountRegex)];
      matches.forEach((match) => {
        amounts.push({
          value: parseFloat(match[1].replace(/,/g, "")),
          currency: "USD", // Default currency
          text: match[0],
        });
      });
    }

    return amounts;
  }

  findMatchingKeyword(line) {
    for (const keyword of this.totalKeywords) {
      if (line.includes(keyword.toLowerCase())) {
        return keyword;
      }
    }
    return "total";
  }

  calculateKeywordConfidence(line) {
    // Higher confidence for more specific keywords
    if (line.includes("grand total")) return 100;
    if (line.includes("amount due")) return 95;
    if (line.includes("total due")) return 90;
    if (line.includes("total")) return 80;
    return 50;
  }

  fuzzyMatchTotal(lines) {
    // Implement fuzzy matching for OCR errors
    console.log("Attempting fuzzy matching...");

    // Look for lines with amounts even without perfect keyword matches
    for (const line of lines) {
      const amounts = this.extractAmountsFromLine(line);
      if (amounts.length > 0 && amounts[0].value > 0) {
        return {
          total: amounts[0].value.toString(),
          currency: amounts[0].currency,
          keyword: "fuzzy_match",
          confidence: 30,
          line: line,
        };
      }
    }

    return {
      total: "0.00",
      currency: "USD",
      keyword: null,
      confidence: 0,
      line: null,
    };
  }

  extractStoreName(lines) {
    // Store name is usually in the first few lines
    for (let i = 0; i < Math.min(3, lines.length); i++) {
      const line = lines[i];
      if (
        line.length > 3 &&
        !this.isDateLine(line) &&
        !this.isAmountLine(line)
      ) {
        return line;
      }
    }
    return null;
  }

  extractDate(text) {
    // Date patterns
    const datePatterns = [
      /(\d{1,2}\/\d{1,2}\/\d{4})/g,
      /(\d{4}-\d{1,2}-\d{1,2})/g,
      /(\d{1,2}-\d{1,2}-\d{4})/g,
      /(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{1,2},?\s+\d{4}/gi,
    ];

    for (const pattern of datePatterns) {
      const match = text.match(pattern);
      if (match) {
        return this.normalizeDate(match[0]);
      }
    }
    return null;
  }

  normalizeDate(dateStr) {
    try {
      const date = new Date(dateStr);
      return date.toISOString().split("T")[0];
    } catch {
      return null;
    }
  }

  isDateLine(line) {
    return /\d{1,2}\/\d{1,2}\/\d{4}|\d{4}-\d{1,2}-\d{1,2}/.test(line);
  }

  isAmountLine(line) {
    return /\$?\d+\.?\d*/.test(line);
  }

  identifyBusinessType(storeName) {
    if (!storeName) return "unknown";

    const name = storeName.toLowerCase();
    if (
      name.includes("restaurant") ||
      name.includes("cafe") ||
      name.includes("diner")
    )
      return "restaurant";
    if (
      name.includes("market") ||
      name.includes("grocery") ||
      name.includes("store")
    )
      return "retail";
    if (name.includes("hotel") || name.includes("inn")) return "hotel";
    if (name.includes("gas") || name.includes("fuel")) return "gas station";

    return "unknown";
  }

  /**
   * Mock OCR service for testing when Vision API is unavailable
   */
  getMockOCRResult() {
    return {
      text: `HBM
H&M Hennes & Mauritz Sverige AB
SE0158
Vikdalsgägen 6A
131 40 Nacka
033-14 00 00
556161-2376

Försr: 14129         But: SE0158        Nr: 1011
Dat: 2014-10-02      Kassa: 06         Tid: 13:23

Handskar
98668        ONESIZE    Röd              59.50
Barn accessoarer
54222        ONESIZE    Svart            49.50
Baby accessoarer  
40203        ONESIZE    Blå              29.50

Total                                   138.50 Kr

ANTAL ARTIKLAR 3
Moms%    Moms    Netto    Totalt
25.00    27.70   110.80   138.50
Totalt   27.70   110.80   138.50

Mottaget    Kontokort Euroline         138.50
Kortbetalning
Butiksnr/Termid 69001584-001060190000006
2014-10-02 13:24`,
      annotations: [
        { description: "H&M" },
        { description: "Hennes" },
        { description: "&" },
        { description: "Mauritz" },
        { description: "Sverige" },
        { description: "AB" },
        { description: "Total" },
        { description: "138.50" },
        { description: "Kr" },
      ],
    };
  }
}

module.exports = new ReceiptOcrService();
