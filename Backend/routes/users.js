const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const { authenticateToken } = require('../middleware/auth');

// Apply authentication middleware to all routes
router.use(authenticateToken);

// @route   GET /api/users/profile
// @desc    Get user profile
// @access  Private
router.get('/profile', userController.getProfile);

// @route   PUT /api/users/profile
// @desc    Update user profile
// @access  Private
router.put('/profile', userController.updateProfile);

// @route   PUT /api/users/settings
// @desc    Update user settings
// @access  Private
router.put('/settings', userController.updateSettings);

// @route   GET /api/users/stats
// @desc    Get user statistics
// @access  Private
router.get('/stats', userController.getUserStats);

module.exports = router;
