const { Transaction, Budget, Goal, Category } = require('../models');
const { Op } = require('sequelize');
const moment = require('moment');

class DashboardController {
  /**
   * Get dashboard statistics
   */
  async getDashboardStats(req, res) {
    try {
      const userId = req.user.id;
      const now = moment();
      const startOfMonth = now.clone().startOf('month');
      const endOfMonth = now.clone().endOf('month');
      const startOfYear = now.clone().startOf('year');

      // Get current month transactions
      const monthlyTransactions = await Transaction.findAll({
        where: {
          user_id: userId,
          date: {
            [Op.between]: [startOfMonth.toDate(), endOfMonth.toDate()]
          }
        },
        raw: true
      });

      // Calculate monthly income and expenses
      const monthlyIncome = monthlyTransactions
        .filter(t => t.money_in)
        .reduce((sum, t) => sum + parseFloat(t.money_in || 0), 0);

      const monthlyExpenses = monthlyTransactions
        .filter(t => t.money_out)
        .reduce((sum, t) => sum + parseFloat(t.money_out || 0), 0);

      // Get total balance (all time)
      const allTransactions = await Transaction.findAll({
        where: { user_id: userId },
        attributes: ['money_in', 'money_out'],
        raw: true
      });

      const totalIncome = allTransactions
        .reduce((sum, t) => sum + parseFloat(t.money_in || 0), 0);
      const totalExpenses = allTransactions
        .reduce((sum, t) => sum + parseFloat(t.money_out || 0), 0);
      const totalBalance = totalIncome - totalExpenses;

      // Get previous month for comparison
      const prevMonthStart = now.clone().subtract(1, 'month').startOf('month');
      const prevMonthEnd = now.clone().subtract(1, 'month').endOf('month');

      const prevMonthTransactions = await Transaction.findAll({
        where: {
          user_id: userId,
          date: {
            [Op.between]: [prevMonthStart.toDate(), prevMonthEnd.toDate()]
          }
        },
        raw: true
      });

      const prevMonthIncome = prevMonthTransactions
        .filter(t => t.money_in)
        .reduce((sum, t) => sum + parseFloat(t.money_in || 0), 0);

      const prevMonthExpenses = prevMonthTransactions
        .filter(t => t.money_out)
        .reduce((sum, t) => sum + parseFloat(t.money_out || 0), 0);

      // Calculate percentage changes
      const incomeChange = prevMonthIncome > 0 
        ? ((monthlyIncome - prevMonthIncome) / prevMonthIncome * 100).toFixed(1)
        : monthlyIncome > 0 ? 100 : 0;

      const expenseChange = prevMonthExpenses > 0
        ? ((monthlyExpenses - prevMonthExpenses) / prevMonthExpenses * 100).toFixed(1)
        : monthlyExpenses > 0 ? 100 : 0;

      // Get budget information
      const budgets = await Budget.findAll({
        where: {
          user_id: userId,
          period_start: { [Op.lte]: endOfMonth.toDate() },
          period_end: { [Op.gte]: startOfMonth.toDate() }
        }
      });

      const totalBudget = budgets.reduce((sum, b) => sum + parseFloat(b.amount || 0), 0);
      const budgetUsed = (monthlyExpenses / totalBudget * 100).toFixed(1);

      // Get goals information
      const goals = await Goal.findAll({
        where: { user_id: userId }
      });

      const activeGoals = goals.filter(g => g.status === 'active').length;
      const completedGoals = goals.filter(g => g.status === 'completed').length;

      // Recent transactions
      const recentTransactions = await Transaction.findAll({
        where: { user_id: userId },
        include: [{
          model: Category,
          as: 'category',
          attributes: ['name', 'color']
        }],
        order: [['date', 'DESC'], ['created_at', 'DESC']],
        limit: 5
      });

      res.json({
        success: true,
        data: {
          totalBalance: {
            value: totalBalance,
            formatted: new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: 'USD'
            }).format(totalBalance)
          },
          monthlyIncome: {
            value: monthlyIncome,
            formatted: new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: 'USD'
            }).format(monthlyIncome),
            change: incomeChange,
            changeType: incomeChange >= 0 ? 'positive' : 'negative'
          },
          monthlyExpenses: {
            value: monthlyExpenses,
            formatted: new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: 'USD'
            }).format(monthlyExpenses),
            change: expenseChange,
            changeType: expenseChange <= 0 ? 'positive' : 'negative'
          },
          budget: {
            total: totalBudget,
            used: monthlyExpenses,
            percentage: budgetUsed,
            remaining: totalBudget - monthlyExpenses
          },
          goals: {
            active: activeGoals,
            completed: completedGoals,
            total: goals.length
          },
          recentTransactions: recentTransactions.map(t => ({
            id: t.id,
            date: t.date,
            details: t.details,
            amount: t.money_in || t.money_out,
            type: t.money_in ? 'income' : 'expense',
            category: t.category ? {
              name: t.category.name,
              color: t.category.color
            } : null,
            currency: t.currency || 'USD'
          })),
          period: {
            current: {
              start: startOfMonth.format('YYYY-MM-DD'),
              end: endOfMonth.format('YYYY-MM-DD'),
              label: startOfMonth.format('MMMM YYYY')
            }
          }
        }
      });

    } catch (error) {
      console.error('Dashboard stats error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch dashboard statistics'
      });
    }
  }

  /**
   * Get chart data for dashboard
   */
  async getChartData(req, res) {
    try {
      const userId = req.user.id;
      const { type } = req.params;
      const { period = '6months' } = req.query;

      let startDate, endDate, groupBy;
      const now = moment();

      // Determine date range and grouping
      switch (period) {
        case '7days':
          startDate = now.clone().subtract(7, 'days');
          endDate = now.clone();
          groupBy = 'day';
          break;
        case '30days':
          startDate = now.clone().subtract(30, 'days');
          endDate = now.clone();
          groupBy = 'day';
          break;
        case '6months':
          startDate = now.clone().subtract(6, 'months');
          endDate = now.clone();
          groupBy = 'month';
          break;
        case '1year':
          startDate = now.clone().subtract(1, 'year');
          endDate = now.clone();
          groupBy = 'month';
          break;
        default:
          startDate = now.clone().subtract(6, 'months');
          endDate = now.clone();
          groupBy = 'month';
      }

      const transactions = await Transaction.findAll({
        where: {
          user_id: userId,
          date: {
            [Op.between]: [startDate.toDate(), endDate.toDate()]
          }
        },
        include: [{
          model: Category,
          as: 'category',
          attributes: ['name', 'color']
        }],
        order: [['date', 'ASC']]
      });

      let chartData = {};

      switch (type) {
        case 'spending-trend':
          chartData = generateSpendingTrendData(transactions, startDate, endDate, groupBy);
          break;
        case 'category-breakdown':
          chartData = generateCategoryBreakdownData(transactions);
          break;
        case 'income-expense':
          chartData = generateIncomeExpenseData(transactions, startDate, endDate, groupBy);
          break;
        default:
          return res.status(400).json({
            success: false,
            error: 'Invalid chart type'
          });
      }

      res.json({
        success: true,
        data: chartData
      });

    } catch (error) {
      console.error('Chart data error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch chart data'
      });
    }
  }

  /**
   * Get financial insights
   */
  async getInsights(req, res) {
    try {
      const userId = req.user.id;
      const now = moment();
      const startOfMonth = now.clone().startOf('month');
      const endOfMonth = now.clone().endOf('month');

      // Get current month data
      const monthlyTransactions = await Transaction.findAll({
        where: {
          user_id: userId,
          date: {
            [Op.between]: [startOfMonth.toDate(), endOfMonth.toDate()]
          }
        },
        include: [{
          model: Category,
          as: 'category',
          attributes: ['name']
        }]
      });

      const insights = [];

      // Spending insights
      const expenses = monthlyTransactions.filter(t => t.money_out);
      const totalExpenses = expenses.reduce((sum, t) => sum + parseFloat(t.money_out), 0);

      if (expenses.length > 0) {
        // Top spending category
        const categorySpending = {};
        expenses.forEach(t => {
          const categoryName = t.category?.name || 'Uncategorized';
          categorySpending[categoryName] = (categorySpending[categoryName] || 0) + parseFloat(t.money_out);
        });

        const topCategory = Object.entries(categorySpending)
          .sort(([,a], [,b]) => b - a)[0];

        if (topCategory) {
          insights.push({
            type: 'spending',
            title: 'Top Spending Category',
            message: `You spent most on ${topCategory[0]} this month: $${topCategory[1].toFixed(2)}`,
            value: topCategory[1],
            category: topCategory[0],
            icon: 'trending-up'
          });
        }
      }

      // Budget insights
      const budgets = await Budget.findAll({
        where: {
          user_id: userId,
          period_start: { [Op.lte]: endOfMonth.toDate() },
          period_end: { [Op.gte]: startOfMonth.toDate() }
        }
      });

      budgets.forEach(budget => {
        const budgetExpenses = expenses
          .filter(t => t.category_id === budget.category_id)
          .reduce((sum, t) => sum + parseFloat(t.money_out), 0);

        const percentage = (budgetExpenses / budget.amount * 100);

        if (percentage > 90) {
          insights.push({
            type: 'budget-warning',
            title: 'Budget Alert',
            message: `You've used ${percentage.toFixed(1)}% of your budget`,
            value: percentage,
            budgetId: budget.id,
            icon: 'alert-triangle'
          });
        }
      });

      res.json({
        success: true,
        data: {
          insights,
          summary: {
            totalInsights: insights.length,
            hasWarnings: insights.some(i => i.type.includes('warning')),
            lastUpdated: new Date().toISOString()
          }
        }
      });

    } catch (error) {
      console.error('Insights error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch insights'
      });
    }
  }

}

/**
 * Generate spending trend chart data
 */
function generateSpendingTrendData(transactions, startDate, endDate, groupBy) {
    const data = [];
    const current = startDate.clone();

    while (current.isSameOrBefore(endDate)) {
      const periodStart = current.clone().startOf(groupBy);
      const periodEnd = current.clone().endOf(groupBy);

      const periodTransactions = transactions.filter(t => {
        const transactionDate = moment(t.date);
        return transactionDate.isBetween(periodStart, periodEnd, null, '[]');
      });

      const expenses = periodTransactions
        .filter(t => t.money_out)
        .reduce((sum, t) => sum + parseFloat(t.money_out), 0);

      data.push({
        period: groupBy === 'day' ? current.format('MMM DD') : current.format('MMM YYYY'),
        date: current.format('YYYY-MM-DD'),
        amount: expenses,
        transactions: periodTransactions.length
      });

      current.add(1, groupBy);
    }

    return {
      type: 'line',
      data,
      title: 'Spending Trend',
      xAxis: 'period',
      yAxis: 'amount'
    };
  }

/**
 * Generate category breakdown chart data
 */
function generateCategoryBreakdownData(transactions) {
    const categoryData = {};
    const expenses = transactions.filter(t => t.money_out);

    expenses.forEach(t => {
      const categoryName = t.category?.name || 'Uncategorized';
      const categoryColor = t.category?.color || '#6B7280';

      if (!categoryData[categoryName]) {
        categoryData[categoryName] = {
          name: categoryName,
          value: 0,
          color: categoryColor,
          count: 0
        };
      }

      categoryData[categoryName].value += parseFloat(t.money_out);
      categoryData[categoryName].count += 1;
    });

    const data = Object.values(categoryData)
      .sort((a, b) => b.value - a.value)
      .map(item => ({
        ...item,
        percentage: expenses.length > 0
          ? (item.value / expenses.reduce((sum, t) => sum + parseFloat(t.money_out), 0) * 100).toFixed(1)
          : 0
      }));

    return {
      type: 'pie',
      data,
      title: 'Spending by Category',
      total: data.reduce((sum, item) => sum + item.value, 0)
    };
  }

/**
 * Generate income vs expense chart data
 */
function generateIncomeExpenseData(transactions, startDate, endDate, groupBy) {
    const data = [];
    const current = startDate.clone();

    while (current.isSameOrBefore(endDate)) {
      const periodStart = current.clone().startOf(groupBy);
      const periodEnd = current.clone().endOf(groupBy);

      const periodTransactions = transactions.filter(t => {
        const transactionDate = moment(t.date);
        return transactionDate.isBetween(periodStart, periodEnd, null, '[]');
      });

      const income = periodTransactions
        .filter(t => t.money_in)
        .reduce((sum, t) => sum + parseFloat(t.money_in), 0);

      const expenses = periodTransactions
        .filter(t => t.money_out)
        .reduce((sum, t) => sum + parseFloat(t.money_out), 0);

      data.push({
        period: groupBy === 'day' ? current.format('MMM DD') : current.format('MMM YYYY'),
        date: current.format('YYYY-MM-DD'),
        income,
        expenses,
        net: income - expenses
      });

      current.add(1, groupBy);
    }

    return {
      type: 'bar',
      data,
      title: 'Income vs Expenses',
      xAxis: 'period',
      yAxis: ['income', 'expenses']
    };
}

module.exports = new DashboardController();
