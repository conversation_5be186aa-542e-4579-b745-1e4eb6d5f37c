const facebookAuthService = require('../services/facebookAuthService');
const { validationResult } = require('express-validator');

// Facebook Login/Register with Access Token
const facebookAuth = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { accessToken } = req.body;

    if (!accessToken) {
      return res.status(400).json({
        success: false,
        error: 'Facebook access token is required'
      });
    }

    // Authenticate with Facebook
    const result = await facebookAuthService.authenticateWithToken(accessToken);

    res.json({
      success: true,
      message: result.user.isNewUser ? 'Account created successfully with Facebook' : 'Login successful',
      data: {
        token: result.token,
        user: result.user,
        isNewUser: result.user.isNewUser,
        linked: result.user.linked
      }
    });
  } catch (error) {
    console.error('Facebook auth error:', error);
    res.status(401).json({
      success: false,
      error: error.message || 'Facebook authentication failed'
    });
  }
};

// Link Facebook Account to Existing User
const linkFacebookAccount = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { accessToken } = req.body;
    const userId = req.user.id; // From auth middleware

    if (!accessToken) {
      return res.status(400).json({
        success: false,
        error: 'Facebook access token is required'
      });
    }

    // Verify Facebook token and get profile
    const facebookProfile = await facebookAuthService.verifyFacebookToken(accessToken);

    // Check if Facebook account is already linked to another user
    const { User } = require('../models');
    const existingUser = await User.findOne({
      where: {
        provider: 'facebook',
        provider_id: facebookProfile.id
      }
    });

    if (existingUser && existingUser.id !== userId) {
      return res.status(409).json({
        success: false,
        error: 'This Facebook account is already linked to another user'
      });
    }

    // Link Facebook account to current user
    const user = await User.findByPk(userId);
    await user.update({
      provider: 'facebook',
      provider_id: facebookProfile.id,
      profile_picture: facebookProfile.picture ? facebookProfile.picture.data.url : user.profile_picture
    });

    res.json({
      success: true,
      message: 'Facebook account linked successfully',
      data: {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          provider: user.provider,
          profilePicture: user.profile_picture
        }
      }
    });
  } catch (error) {
    console.error('Link Facebook account error:', error);
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to link Facebook account'
    });
  }
};

// Unlink Facebook Account
const unlinkFacebookAccount = async (req, res) => {
  try {
    const userId = req.user.id; // From auth middleware

    await facebookAuthService.unlinkFacebookAccount(userId);

    res.json({
      success: true,
      message: 'Facebook account unlinked successfully'
    });
  } catch (error) {
    console.error('Unlink Facebook account error:', error);
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to unlink Facebook account'
    });
  }
};

// Get Facebook User Info (for profile sync)
const getFacebookUserInfo = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { accessToken } = req.body;

    if (!accessToken) {
      return res.status(400).json({
        success: false,
        error: 'Facebook access token is required'
      });
    }

    const userInfo = await facebookAuthService.getFacebookUserInfo(accessToken);

    res.json({
      success: true,
      data: {
        facebookProfile: {
          id: userInfo.id,
          name: userInfo.name,
          email: userInfo.email,
          picture: userInfo.picture ? userInfo.picture.data.url : null,
          birthday: userInfo.birthday,
          location: userInfo.location ? userInfo.location.name : null
        }
      }
    });
  } catch (error) {
    console.error('Get Facebook user info error:', error);
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to get Facebook user info'
    });
  }
};

// Verify Facebook Token (utility endpoint)
const verifyFacebookToken = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { accessToken } = req.body;

    if (!accessToken) {
      return res.status(400).json({
        success: false,
        error: 'Facebook access token is required'
      });
    }

    const profile = await facebookAuthService.verifyFacebookToken(accessToken);

    res.json({
      success: true,
      message: 'Facebook token is valid',
      data: {
        profile: {
          id: profile.id,
          name: profile.name,
          email: profile.email,
          picture: profile.picture ? profile.picture.data.url : null
        }
      }
    });
  } catch (error) {
    console.error('Verify Facebook token error:', error);
    res.status(401).json({
      success: false,
      error: error.message || 'Invalid Facebook token'
    });
  }
};

module.exports = {
  facebookAuth,
  linkFacebookAccount,
  unlinkFacebookAccount,
  getFacebookUserInfo,
  verifyFacebookToken
};
