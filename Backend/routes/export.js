const express = require("express");
const router = express.Router();
const { query } = require("express-validator");
const reportController = require("../controllers/reportController");
const { authenticateToken } = require("../middleware/auth");

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Validation rules for reports
const reportValidation = [
  query("start_date")
    .optional()
    .isDate()
    .withMessage("Valid start date is required"),
  query("end_date")
    .optional()
    .isDate()
    .withMessage("Valid end date is required"),
  query("currency")
    .optional()
    .isLength({ min: 3, max: 3 })
    .withMessage("Currency must be 3 characters"),
  query("period")
    .optional()
    .isIn(["daily", "weekly", "monthly", "quarterly", "yearly"])
    .withMessage("Invalid period"),
];

const exportValidation = [
  query("start_date")
    .optional()
    .isDate()
    .withMessage("Valid start date is required"),
  query("end_date")
    .optional()
    .isDate()
    .withMessage("Valid end date is required"),
  query("category_id")
    .optional()
    .isUUID()
    .withMessage("Valid category ID is required"),
  query("type")
    .optional()
    .isIn(["income", "expense"])
    .withMessage("Type must be income or expense"),
];

// Report routes
router.get(
  "/reports/financial-summary",
  reportValidation,
  reportController.getFinancialSummary
);
router.get(
  "/reports/budget-report",
  reportValidation,
  reportController.getBudgetReport
);
router.get(
  "/reports/goals-report",
  reportValidation,
  reportController.getGoalsReport
);

router.get(
  "/reports/financial-insights",
  reportValidation,
  reportController.getFinancialInsights
);

// Export routes
router.get(
  "/excel/transactions",
  exportValidation,
  reportController.exportTransactionsExcel
);

router.get(
  "/csv/transactions",
  exportValidation,
  reportController.exportTransactionsCSV
);

// Enhanced export routes
router.post(
  "/excel/enhanced",
  reportController.exportToExcelEnhanced
);

router.post(
  "/pdf/enhanced",
  reportController.exportToPDFEnhanced
);

// Legacy routes (for backward compatibility)
router.get("/excel", (req, res) => {
  res.redirect("/api/export/excel/transactions");
});

router.get("/pdf", (req, res) => {
  res.json({
    message: "PDF export endpoint - coming soon",
    available_exports: [
      "/api/export/excel/transactions",
      "/api/export/reports/financial-summary",
      "/api/export/reports/budget-report",
      "/api/export/reports/goals-report",
    ],
  });
});

module.exports = router;
