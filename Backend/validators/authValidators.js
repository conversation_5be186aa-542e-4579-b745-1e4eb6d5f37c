const { body } = require('express-validator');
const smsService = require('../services/smsService');

// Register validation
const validateRegister = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters')
    .matches(/^[a-zA-Z\s\u1780-\u17FF]+$/)
    .withMessage('Name can only contain letters and spaces (including Khmer characters)'),

  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),

  body('phone')
    .optional()
    .matches(/^[+]?[\d\s\-()]+$/)
    .withMessage('Please provide a valid phone number'),

  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),

  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('Password confirmation does not match password');
      }
      return true;
    }),

  body('preferred_language')
    .optional()
    .isIn(['en', 'km'])
    .withMessage('Preferred language must be either "en" or "km"'),

  body('preferred_currency')
    .optional()
    .isLength({ min: 3, max: 3 })
    .isUppercase()
    .withMessage('Currency must be a 3-letter uppercase code (e.g., USD, KHR)')
];

// Login validation (identifier can be email OR phone)
const validateLogin = [
  body('identifier')
    .optional({ nullable: true })
    .custom((value, { req }) => {
      const identifier = (value || req.body.email || '').trim();
      if (!identifier) throw new Error('Identifier (email or phone) is required');
      const isEmail = /@/.test(identifier);
      const isPhone = /^[+]?[-()\d\s]{6,}$/.test(identifier);
      if (!isEmail && !isPhone) throw new Error('Identifier must be a valid email or phone number');
      if (isPhone) {
        const validation = smsService.validatePhoneNumber(identifier);
        if (validation.isValid) req.body.identifier = validation.formatted; // canonicalize
      } else {
        req.body.identifier = identifier.toLowerCase();
      }
      return true;
    }),
  body('password')
    .notEmpty()
    .withMessage('Password is required')
];

// Forgot password validation (identifier OR separate email/phone for backward compat)
const validateForgotPassword = [
  body('identifier')
    .optional({ nullable: true })
    .custom((value, { req }) => {
      const identifier = (value || req.body.email || req.body.phone || '').trim();
      if (!identifier) throw new Error('Identifier (email or phone) is required');
      const isEmail = /@/.test(identifier);
      const isPhone = /^[+]?[-()\d\s]{6,}$/.test(identifier);
      if (!isEmail && !isPhone) throw new Error('Identifier must be a valid email or phone number');
      if (isPhone) {
        const validation = smsService.validatePhoneNumber(identifier);
        if (!validation.isValid) throw new Error('Invalid phone number');
        req.body.identifier = validation.formatted;
      } else {
        req.body.identifier = identifier.toLowerCase();
      }
      return true;
    }),
  body('email')
    .optional({ nullable: true })
    .custom((value, { req }) => {
      // Only validate email if it's provided and no identifier is used
      if (value && !req.body.identifier) {
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          throw new Error('Invalid email');
        }
      }
      return true;
    }),
  body('phone')
    .optional({ nullable: true })
    .custom((value, { req }) => {
      // Only validate phone if it's provided and no identifier is used
      if (value && !req.body.identifier) {
        if (!/^[+]?[-()\d\s]{6,}$/.test(value)) {
          throw new Error('Invalid phone number');
        }
      }
      return true;
    })
];

// Reset password validation
const validateResetPassword = [
  body('token')
    .notEmpty()
    .withMessage('Reset token is required'),

  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),

  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('Password confirmation does not match password');
      }
      return true;
    })
];

// Verify OTP validation (identifier OR email)
const validateVerifyOTP = [
  body('identifier')
    .optional({ nullable: true })
    .custom((value, { req }) => {
      const identifier = (value || req.body.email || '').trim();
      if (!identifier) throw new Error('Identifier (email or phone) is required');
      const isEmail = /@/.test(identifier);
      const isPhone = /^[+]?[-()\d\s]{6,}$/.test(identifier);
      if (!isEmail && !isPhone) throw new Error('Identifier must be a valid email or phone number');
      if (isPhone) {
        const validation = smsService.validatePhoneNumber(identifier);
        if (!validation.isValid) throw new Error('Invalid phone number');
        req.body.identifier = validation.formatted;
      } else {
        req.body.identifier = identifier.toLowerCase();
      }
      return true;
    }),
  body('otp')
    .isLength({ min: 6, max: 6 })
    .isNumeric()
    .withMessage('OTP must be a 6-digit number')
];

// Change password validation
const validateChangePassword = [
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),

  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('New password must contain at least one lowercase letter, one uppercase letter, and one number'),

  body('confirmNewPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('Password confirmation does not match new password');
      }
      return true;
    })
];

module.exports = {
  validateRegister,
  validateLogin,
  validateForgotPassword,
  validateVerifyOTP,
  validateResetPassword,
  validateChangePassword
};
