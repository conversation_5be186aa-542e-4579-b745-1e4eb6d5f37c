'use strict';

module.exports = (sequelize, DataTypes) => {
  const Category = sequelize.define('Category', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 100]
      }
    },
    name_km: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: 'Khmer translation of category name'
    },
    type: {
      type: DataTypes.ENUM('income', 'expense'),
      allowNull: false
    },
    icon: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: 'Icon identifier for UI'
    },
    color: {
      type: DataTypes.STRING(7),
      allowNull: true,
      validate: {
        is: /^#[0-9A-F]{6}$/i
      },
      comment: 'Hex color code for UI'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    description_km: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Khmer translation of description'
    },
    is_default: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'System default categories'
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: 'Display order in UI'
    }
  }, {
    tableName: 'categories',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['type']
      },
      {
        fields: ['is_default']
      },
      {
        fields: ['is_active']
      },
      {
        fields: ['sort_order']
      }
    ]
  });

  Category.associate = function(models) {
    Category.hasMany(models.Transaction, {
      foreignKey: 'category_id',
      as: 'transactions'
    });
    Category.hasMany(models.Budget, {
      foreignKey: 'category_id',
      as: 'budgets'
    });
  };

  return Category;
};
