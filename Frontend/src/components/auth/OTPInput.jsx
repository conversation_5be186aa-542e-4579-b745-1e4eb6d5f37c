import React, { useState, useRef, useEffect } from 'react';
import { toast } from 'react-hot-toast';

const OTPInput = ({ 
  length = 6, 
  onComplete, 
  onResend, 
  identifier, 
  type = 'email_verification',
  className = '' 
}) => {
  const [otp, setOtp] = useState(new Array(length).fill(''));
  const [loading, setLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);
  const inputRefs = useRef([]);

  useEffect(() => {
    // Focus first input on mount
    if (inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, []);

  useEffect(() => {
    // Countdown timer for resend
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [timeLeft]);

  const handleChange = (element, index) => {
    if (isNaN(element.value)) return false;

    setOtp([...otp.map((d, idx) => (idx === index ? element.value : d))]);

    // Focus next input
    if (element.nextSibling && element.value !== '') {
      element.nextSibling.focus();
    }
  };

  const handleKeyDown = (e, index) => {
    if (e.key === 'Backspace') {
      if (otp[index] === '' && index > 0) {
        // Focus previous input if current is empty
        inputRefs.current[index - 1].focus();
      }
      // Clear current input
      setOtp([...otp.map((d, idx) => (idx === index ? '' : d))]);
    }
  };

  const handlePaste = (e) => {
    e.preventDefault();
    const pasteData = e.clipboardData.getData('text');
    const pasteArray = pasteData.slice(0, length).split('');
    
    if (pasteArray.every(char => !isNaN(char))) {
      const newOtp = [...otp];
      pasteArray.forEach((char, index) => {
        if (index < length) {
          newOtp[index] = char;
        }
      });
      setOtp(newOtp);
      
      // Focus last filled input or next empty input
      const nextIndex = Math.min(pasteArray.length, length - 1);
      inputRefs.current[nextIndex]?.focus();
    }
  };

  const handleSubmit = async () => {
    const otpValue = otp.join('');
    if (otpValue.length !== length) {
      toast.error(`Please enter all ${length} digits`);
      return;
    }

    setLoading(true);
    try {
      await onComplete(otpValue);
    } catch (error) {
      console.error('OTP verification error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleResend = async () => {
    if (timeLeft > 0) return;
    
    setResendLoading(true);
    try {
      await onResend();
      setTimeLeft(60); // 60 seconds cooldown
      setOtp(new Array(length).fill('')); // Clear OTP inputs
      inputRefs.current[0]?.focus(); // Focus first input
      toast.success('OTP sent successfully');
    } catch (error) {
      console.error('Resend OTP error:', error);
      toast.error('Failed to resend OTP');
    } finally {
      setResendLoading(false);
    }
  };

  // Auto-submit when all digits are filled
  useEffect(() => {
    const otpValue = otp.join('');
    if (otpValue.length === length) {
      handleSubmit();
    }
  }, [otp]);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* OTP Input Fields */}
      <div className="flex justify-center space-x-3">
        {otp.map((data, index) => (
          <input
            key={index}
            ref={(ref) => (inputRefs.current[index] = ref)}
            type="text"
            maxLength="1"
            value={data}
            onChange={(e) => handleChange(e.target, index)}
            onKeyDown={(e) => handleKeyDown(e, index)}
            onPaste={handlePaste}
            className="
              w-12 h-12 text-center text-xl font-semibold
              border-2 border-gray-300 rounded-lg
              focus:border-primary-500 focus:ring-2 focus:ring-primary-200
              dark:border-gray-600 dark:bg-gray-700 dark:text-white
              dark:focus:border-primary-400 dark:focus:ring-primary-800
              transition-colors duration-200
            "
            disabled={loading}
          />
        ))}
      </div>

      {/* Instructions */}
      <div className="text-center">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Enter the {length}-digit code sent to{' '}
          <span className="font-medium text-gray-900 dark:text-gray-100">
            {identifier}
          </span>
        </p>
      </div>

      {/* Submit Button */}
      <button
        onClick={handleSubmit}
        disabled={loading || otp.join('').length !== length}
        className="
          w-full px-4 py-2 bg-primary-600 hover:bg-primary-700 
          disabled:bg-gray-400 text-white font-medium rounded-lg
          transition-colors duration-200
        "
      >
        {loading ? (
          <div className="flex items-center justify-center space-x-2">
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            <span>Verifying...</span>
          </div>
        ) : (
          'Verify Code'
        )}
      </button>

      {/* Resend Button */}
      <div className="text-center">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Didn't receive the code?{' '}
          {timeLeft > 0 ? (
            <span className="text-gray-500">
              Resend in {timeLeft}s
            </span>
          ) : (
            <button
              onClick={handleResend}
              disabled={resendLoading}
              className="
                text-primary-600 hover:text-primary-700 font-medium
                disabled:text-gray-400 transition-colors duration-200
              "
            >
              {resendLoading ? 'Sending...' : 'Resend Code'}
            </button>
          )}
        </p>
      </div>
    </div>
  );
};

export default OTPInput;
