import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import {
  Crown,
  CreditCard,
  Calendar,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  Zap,
  Shield,
  Star,
  QrCode,
  RefreshCw
} from 'lucide-react';
import useBillingStore from '../store/billingStore';
import PaymentModal from '../components/billing/PaymentModal';
// import UsageChart from '../components/billing/UsageChart';
// import PaymentHistory from '../components/billing/PaymentHistory';
import { toast } from 'react-hot-toast';
import { useTranslation } from 'react-i18next';

const Billing = () => {
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const { t } = useTranslation();

  const {
    billingInfo,
    usageStats,
    paymentHistory,
    isLoading,
    error,
    fetchBillingInfo,
    fetchUsageStats,
    fetchPaymentHistory,
    isPremium,
    getSubscriptionExpiry,
    clearError
  } = useBillingStore();

  useEffect(() => {
    fetchBillingInfo();
    fetchUsageStats();
    fetchPaymentHistory();
  }, []);

  useEffect(() => {
    if (error) {
      toast.error(error);
      clearError();
    }
  }, [error]);

  const handleUpgrade = () => {
    setShowPaymentModal(true);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const getUsageColor = (percentage) => {
    if (percentage >= 90) return 'text-red-600';
    if (percentage >= 70) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getUsageBgColor = (percentage) => {
    if (percentage >= 90) return 'bg-red-100';
    if (percentage >= 70) return 'bg-yellow-100';
    return 'bg-green-100';
  };

  if (isLoading && !billingInfo) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Helmet>
        <title>{t('billing.title')} - Finwise</title>
      </Helmet>

      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              <CreditCard className="h-6 w-6" />
              {t('billing.title')}
            </h1>
            <p className="text-gray-600 mt-1">
              {t('billing.title')}
            </p>
          </div>

          {!isPremium() && (
            <button
              onClick={handleUpgrade}
              className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:from-purple-700 hover:to-blue-700 transition-all duration-200 flex items-center gap-2"
            >
              <Crown className="h-5 w-5" />
              {t('billing.upgradeToPremium')}
            </button>
          )}
        </div>
      </div>

      {/* Current Plan Status */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className={`bg-white rounded-lg shadow-sm p-6 border-l-4 ${
          isPremium() ? 'border-purple-500' : 'border-gray-300'
        }`}>
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                {isPremium() ? (
                  <>
                    <Crown className="h-5 w-5 text-purple-600" />
                    Premium Plan
                  </>
                ) : (
                  <>
                    <Shield className="h-5 w-5 text-gray-600" />
                    Freemium Plan
                  </>
                )}
              </h3>
              <p className="text-gray-600 text-sm mt-1">
                {isPremium() ? 'Unlimited access to all features' : 'Limited access with usage restrictions'}
              </p>
            </div>
            <div className={`p-2 rounded-full ${
              isPremium() ? 'bg-purple-100' : 'bg-gray-100'
            }`}>
              {isPremium() ? (
                <CheckCircle className="h-6 w-6 text-purple-600" />
              ) : (
                <AlertCircle className="h-6 w-6 text-gray-600" />
              )}
            </div>
          </div>
          
          {isPremium() && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Calendar className="h-4 w-4" />
                Expires: {formatDate(getSubscriptionExpiry())}
              </div>
            </div>
          )}
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Monthly Price</h3>
              <p className="text-2xl font-bold text-green-600 mt-1">
                {isPremium() ? '$1.99' : 'Free'}
              </p>
            </div>
            <div className="p-2 rounded-full bg-green-100">
              <TrendingUp className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Next Billing</h3>
              <p className="text-lg font-medium text-gray-900 mt-1">
                {isPremium() ? formatDate(getSubscriptionExpiry()) : 'N/A'}
              </p>
            </div>
            <div className="p-2 rounded-full bg-blue-100">
              <Clock className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Usage Overview', icon: TrendingUp },
              { id: 'history', label: 'Payment History', icon: CreditCard },
              { id: 'features', label: 'Features', icon: Star }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Usage Statistics */}
              {usageStats && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {Object.entries(usageStats.stats).map(([featureType, stats]) => (
                    <div key={featureType} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900 capitalize">
                          {featureType.replace('_', ' ')}
                        </h4>
                        <span className={`text-sm font-medium ${getUsageColor(stats.percentage)}`}>
                          {stats.currentUsage}/{stats.limit === 'unlimited' ? '∞' : stats.limit}
                        </span>
                      </div>
                      
                      <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                        <div
                          className={`h-2 rounded-full transition-all duration-300 ${
                            stats.limit === 'unlimited' ? 'bg-green-500' : getUsageBgColor(stats.percentage).replace('bg-', 'bg-').replace('-100', '-500')
                          }`}
                          style={{
                            width: stats.limit === 'unlimited' ? '100%' : `${Math.min(stats.percentage, 100)}%`
                          }}
                        ></div>
                      </div>
                      
                      <p className="text-xs text-gray-600">
                        {stats.limit === 'unlimited' 
                          ? 'Unlimited usage' 
                          : `${stats.remaining} remaining this month`
                        }
                      </p>
                    </div>
                  ))}
                </div>
              )}

              {/* Usage Chart */}
              {/* <UsageChart /> */}
            </div>
          )}

          {activeTab === 'history' && (
            <div className="text-center py-8">
              <p className="text-gray-500">Payment history will be displayed here</p>
            </div>
          )}

          {activeTab === 'features' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Freemium Features */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <Shield className="h-5 w-5 text-gray-600" />
                  Freemium Plan
                </h3>
                <ul className="space-y-3">
                  <li className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span>10 receipt scans per month</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span>10 Excel imports per month</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span>Basic transaction management</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span>Budget tracking</span>
                  </li>
                </ul>
              </div>

              {/* Premium Features */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <Crown className="h-5 w-5 text-purple-600" />
                  Premium Plan - $1.99/month
                </h3>
                <ul className="space-y-3">
                  <li className="flex items-center gap-3">
                    <Zap className="h-5 w-5 text-purple-500" />
                    <span>Unlimited receipt scans</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <Zap className="h-5 w-5 text-purple-500" />
                    <span>Unlimited Excel imports</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <Zap className="h-5 w-5 text-purple-500" />
                    <span>Advanced analytics</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <Zap className="h-5 w-5 text-purple-500" />
                    <span>Priority support</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <Zap className="h-5 w-5 text-purple-500" />
                    <span>Export to multiple formats</span>
                  </li>
                </ul>
                
                {!isPremium() && (
                  <button
                    onClick={handleUpgrade}
                    className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 rounded-lg font-medium hover:from-purple-700 hover:to-blue-700 transition-all duration-200 flex items-center justify-center gap-2 mt-4"
                  >
                    <Crown className="h-5 w-5" />
                    Upgrade Now
                  </button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
      />
    </div>
  );
};

export default Billing;
