#!/bin/bash

# Generate Secure Secrets for Production
# This script generates strong, random secrets for your production environment

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo "🔐 Finwise - Production Secrets Generator"
echo "========================================"

# Check if openssl is available
if ! command -v openssl &> /dev/null; then
    print_warning "OpenSSL not found. Installing..."
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        sudo apt-get update && sudo apt-get install -y openssl
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        brew install openssl
    else
        echo "Please install OpenSSL manually"
        exit 1
    fi
fi

# Generate secrets
print_status "Generating secure secrets..."

JWT_SECRET=$(openssl rand -base64 64 | tr -d '\n')
SESSION_SECRET=$(openssl rand -base64 64 | tr -d '\n')
API_KEY=$(openssl rand -hex 32)
ENCRYPTION_KEY=$(openssl rand -base64 32 | tr -d '\n')

echo ""
print_success "Secrets generated successfully!"
echo ""
echo "🔑 Generated Secrets (KEEP THESE SECURE!):"
echo "=========================================="
echo ""
echo "JWT_SECRET=$JWT_SECRET"
echo "SESSION_SECRET=$SESSION_SECRET"
echo "API_KEY=$API_KEY"
echo "ENCRYPTION_KEY=$ENCRYPTION_KEY"
echo ""

# Option to save to file
read -p "Do you want to save these secrets to a secure file? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    SECRETS_FILE="secrets-$(date +%Y%m%d-%H%M%S).env"
    
    cat > "$SECRETS_FILE" << EOF
# Generated secrets for Finwise production deployment
# Generated on: $(date)
# KEEP THIS FILE SECURE AND DO NOT COMMIT TO VERSION CONTROL

JWT_SECRET=$JWT_SECRET
SESSION_SECRET=$SESSION_SECRET
API_KEY=$API_KEY
ENCRYPTION_KEY=$ENCRYPTION_KEY

# Instructions:
# 1. Copy these values to your production .env file
# 2. Store this file in a secure location
# 3. Delete this file after copying the secrets
# 4. Never commit secrets to version control
EOF

    chmod 600 "$SECRETS_FILE"
    print_success "Secrets saved to: $SECRETS_FILE"
    print_warning "Remember to delete this file after copying the secrets!"
fi

echo ""
echo "📋 Next Steps:"
echo "1. Copy the JWT_SECRET and SESSION_SECRET to your production .env file"
echo "2. Update your Digital Ocean environment variables"
echo "3. Restart your backend service"
echo "4. Test authentication functionality"
echo ""
echo "🔒 Security Reminders:"
echo "- Never commit secrets to version control"
echo "- Use different secrets for different environments"
echo "- Rotate secrets regularly"
echo "- Store secrets securely (use a password manager)"
echo ""

print_success "Secret generation completed!"
