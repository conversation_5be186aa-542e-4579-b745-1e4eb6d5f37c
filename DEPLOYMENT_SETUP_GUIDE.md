# Finwise Deployment Setup Guide

This guide will help you deploy your Finwise application with:
- **Frontend**: Netlify (React/Vite)
- **Backend**: Digital Ocean (Docker)
- **Database**: Aiven MySQL (already configured)

## 🚀 Quick Deployment Overview

### Current Configuration Status
- ✅ Frontend configured for Netlify
- ✅ Backend configured for Digital Ocean with Docker
- ✅ Database connected to Aiven MySQL
- ✅ Environment files ready for both development and production

---

## 📱 Frontend Deployment (Netlify)

### Step 1: Connect Repository to Netlify

1. **Login to Netlify**: Go to [netlify.com](https://netlify.com) and login
2. **New Site**: Click "New site from Git"
3. **Connect Repository**: Choose your Git provider and select this repository
4. **Build Settings**: Netlify will auto-detect, but verify:
   - **Base directory**: `Frontend`
   - **Build command**: `npm install --legacy-peer-deps && npm run build`
   - **Publish directory**: `Frontend/dist`

### Step 2: Environment Variables in Netlify

Go to Site Settings → Environment Variables and add:

```
VITE_API_URL=https://seal-app-mvbaj.ondigitalocean.app/api
VITE_API_BASE_URL=https://seal-app-mvbaj.ondigitalocean.app
VITE_GOOGLE_OAUTH_URL=https://seal-app-mvbaj.ondigitalocean.app/api/auth/google
VITE_FACEBOOK_OAUTH_URL=https://seal-app-mvbaj.ondigitalocean.app/api/auth/facebook
VITE_APP_NAME=Finwise
VITE_APP_VERSION=1.0.0
VITE_NODE_ENV=production
```

### Step 3: Deploy

1. **Trigger Deploy**: Push to your main branch or click "Deploy site"
2. **Custom Domain** (Optional): Set up your custom domain in Site Settings → Domain management
3. **HTTPS**: Netlify automatically provides SSL certificates

---

## 🐳 Backend Deployment (Digital Ocean)

### Step 1: Create Digital Ocean Droplet

1. **Create Droplet**: 
   - Choose Ubuntu 22.04 LTS
   - Minimum: 1GB RAM, 1 vCPU (Basic plan $6/month)
   - Recommended: 2GB RAM, 1 vCPU ($12/month)

2. **Initial Server Setup**:
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo apt install docker-compose -y

# Logout and login again for Docker permissions
exit
```

### Step 2: Deploy Your Application

1. **Clone Repository**:
```bash
git clone https://github.com/your-username/finwise.git
cd finwise
```

2. **Set Environment Variables**:
```bash
# Copy production environment file
cp Backend/.env.production Backend/.env

# Edit if needed
nano Backend/.env
```

3. **Build and Run**:
```bash
# Make deploy script executable
chmod +x deploy.sh

# Run deployment
./deploy.sh
```

### Step 3: Alternative Docker Compose Deployment

```bash
# Use docker-compose for easier management
docker-compose up -d

# Check logs
docker-compose logs -f backend

# Stop services
docker-compose down
```

---

## 🔧 Configuration Updates Needed

### Update Your Digital Ocean URL

**Current URL**: `https://seal-app-mvbaj.ondigitalocean.app`

If you need to update this URL:

1. **Update Frontend Environment**:
   - Update `Frontend/.env.production`
   - Update `netlify.toml` (lines 14, 25-26, 30-31, 35-36, 47)

2. **Update Backend CORS**:
   - Update `Backend/.env.production`
   - Update `docker-compose.yml` (lines 19-20)
   - Update `update-digital-ocean.sh` (lines 30-31)

### Get Your Digital Ocean App URL

If using Digital Ocean App Platform:
1. Go to Digital Ocean Dashboard
2. Navigate to Apps
3. Your app URL will be shown in the app details

---

## 🔐 Security Recommendations

### 1. Update Secrets
```bash
# Generate strong JWT secret
openssl rand -base64 32

# Generate strong session secret  
openssl rand -base64 32
```

### 2. Environment Variables Security
- Never commit `.env` files to Git
- Use Digital Ocean's environment variables feature
- Rotate secrets regularly

### 3. Database Security
- Your Aiven MySQL is already secured
- Ensure firewall rules allow only your Digital Ocean droplet

---

## 📊 Monitoring & Maintenance

### Health Checks
- Frontend: Automatic via Netlify
- Backend: `https://your-backend-url/api/health`

### Logs
```bash
# View backend logs
docker logs finwise-backend

# Follow logs in real-time
docker logs -f finwise-backend
```

### Updates
```bash
# Update backend
git pull origin main
./update-digital-ocean.sh

# Frontend updates automatically via Netlify when you push to Git
```

---

## 🚨 Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure CORS_ORIGIN matches your Netlify URL exactly
2. **Database Connection**: Check if your Digital Ocean IP is whitelisted in Aiven
3. **Build Failures**: Check Node.js version compatibility (using Node 18)

### Quick Fixes
```bash
# Restart backend
docker restart finwise-backend

# Check backend status
docker ps

# View detailed logs
docker logs finwise-backend --tail 100
```

---

## 📞 Support

If you encounter issues:
1. Check the logs first
2. Verify environment variables
3. Ensure all services are running
4. Check network connectivity between services

Your deployment is ready! 🎉
