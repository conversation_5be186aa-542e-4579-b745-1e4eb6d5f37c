const { User, UsageTracking } = require('../models');
const { getUserUsageStats, canUseFeature, USAGE_LIMITS } = require('../middleware/usageLimits');

async function testUsageLimits() {
  console.log('🧪 Starting Usage Limits Test...\n');

  try {
    // Test 1: Create a freemium test user
    console.log('1️⃣ Creating freemium test user...');
    const freemiumUser = await User.create({
      name: 'Freemium Test User',
      email: `freemium_test_${Date.now()}@example.com`,
      password_hash: 'test123',
      role: 'Freemium'
    });
    console.log(`✅ Freemium user created: ${freemiumUser.id}\n`);

    // Test 2: Check usage limits configuration
    console.log('2️⃣ Checking usage limits configuration...');
    console.log('📊 Usage limits:', JSON.stringify(USAGE_LIMITS, null, 2));
    console.log('');

    // Test 3: Test initial usage stats
    console.log('3️⃣ Testing initial usage stats...');
    const initialStats = await getUserUsageStats(freemiumUser.id);
    console.log('📊 Initial stats:', JSON.stringify(initialStats, null, 2));
    console.log('');

    // Test 4: Test OCR scan usage tracking
    console.log('4️⃣ Testing OCR scan usage tracking...');
    for (let i = 1; i <= 12; i++) {
      const canUse = await canUseFeature(freemiumUser.id, 'ocr_scan');
      console.log(`Scan ${i}: Can use OCR? ${canUse}`);
      
      if (canUse) {
        await UsageTracking.incrementCurrentMonthUsage(freemiumUser.id, 'ocr_scan');
      } else {
        console.log(`❌ OCR scan limit reached at attempt ${i}`);
        break;
      }
    }
    console.log('');

    // Test 5: Test Excel import usage tracking
    console.log('5️⃣ Testing Excel import usage tracking...');
    for (let i = 1; i <= 12; i++) {
      const canUse = await canUseFeature(freemiumUser.id, 'excel_import');
      console.log(`Import ${i}: Can use Excel import? ${canUse}`);
      
      if (canUse) {
        await UsageTracking.incrementCurrentMonthUsage(freemiumUser.id, 'excel_import');
      } else {
        console.log(`❌ Excel import limit reached at attempt ${i}`);
        break;
      }
    }
    console.log('');

    // Test 6: Check final freemium usage stats
    console.log('6️⃣ Checking final freemium usage stats...');
    const freemiumFinalStats = await getUserUsageStats(freemiumUser.id);
    console.log('📊 Freemium final stats:', JSON.stringify(freemiumFinalStats, null, 2));
    console.log('');

    // Test 7: Create a premium test user
    console.log('7️⃣ Creating premium test user...');
    const premiumUser = await User.create({
      name: 'Premium Test User',
      email: `premium_test_${Date.now()}@example.com`,
      password_hash: 'test123',
      role: 'Premium',
      subscription_expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
    });
    console.log(`✅ Premium user created: ${premiumUser.id}\n`);

    // Test 8: Test premium usage (should be unlimited)
    console.log('8️⃣ Testing premium usage (unlimited)...');
    
    // Test OCR scans for premium user
    for (let i = 1; i <= 15; i++) {
      const canUse = await canUseFeature(premiumUser.id, 'ocr_scan');
      if (canUse) {
        await UsageTracking.incrementCurrentMonthUsage(premiumUser.id, 'ocr_scan');
      }
    }

    // Test Excel imports for premium user
    for (let i = 1; i <= 15; i++) {
      const canUse = await canUseFeature(premiumUser.id, 'excel_import');
      if (canUse) {
        await UsageTracking.incrementCurrentMonthUsage(premiumUser.id, 'excel_import');
      }
    }

    const premiumStats = await getUserUsageStats(premiumUser.id);
    console.log('📊 Premium usage stats:', JSON.stringify(premiumStats, null, 2));
    console.log('');

    // Test 9: Test expired premium user (should behave like freemium)
    console.log('9️⃣ Testing expired premium user...');
    const expiredPremiumUser = await User.create({
      name: 'Expired Premium User',
      email: `expired_premium_test_${Date.now()}@example.com`,
      password_hash: 'test123',
      role: 'Premium',
      subscription_expires_at: new Date(Date.now() - 24 * 60 * 60 * 1000) // 1 day ago
    });

    const expiredStats = await getUserUsageStats(expiredPremiumUser.id);
    console.log('📊 Expired premium user stats:', JSON.stringify(expiredStats, null, 2));
    console.log('');

    console.log('🎉 All usage limit tests completed successfully!');
    console.log('\n📋 Test Summary:');
    console.log(`👤 Freemium User ID: ${freemiumUser.id}`);
    console.log(`👤 Premium User ID: ${premiumUser.id}`);
    console.log(`👤 Expired Premium User ID: ${expiredPremiumUser.id}`);

    return {
      freemiumUserId: freemiumUser.id,
      premiumUserId: premiumUser.id,
      expiredPremiumUserId: expiredPremiumUser.id,
      freemiumStats: freemiumFinalStats,
      premiumStats: premiumStats,
      expiredStats: expiredStats
    };

  } catch (error) {
    console.error('❌ Usage limits test failed:', error);
    throw error;
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testUsageLimits()
    .then((result) => {
      console.log('\n✅ Usage limits test completed successfully');
      console.log('🔑 Test Result Summary:', {
        freemiumUserId: result.freemiumUserId,
        premiumUserId: result.premiumUserId,
        expiredPremiumUserId: result.expiredPremiumUserId
      });
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Usage limits test failed:', error);
      process.exit(1);
    });
}

module.exports = testUsageLimits;
