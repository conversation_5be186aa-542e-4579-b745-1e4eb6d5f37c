const multer = require("multer");
const path = require("path");
const fs = require("fs").promises;
const { validationResult } = require("express-validator");
const documentAiService = require("../services/documentAiService");
const receiptOcrService = require("../services/receiptOcrService");

// Mock OCR service for testing when Google Cloud billing is not enabled
const mockOCRService = (imageBuffer) => {
  // Simulate OCR processing delay
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        textAnnotations: [
          {
            description: `MOCK RECEIPT DATA
Store Name: Sample Store
Date: ${new Date().toLocaleDateString()}
Item 1: Coffee - $4.50
Item 2: Sandwich - $8.95
Item 3: Tax - $1.35
Total: $14.80
Thank you for your purchase!`,
          },
        ],
      });
    }, 1000);
  });
};

// Configure multer for file upload
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Check file type
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(
        new Error(
          "Invalid file type. Only JPEG, PNG, and WebP images are allowed."
        ),
        false
      );
    }
  },
});

// Helper function to extract transaction data from OCR text
const extractTransactionData = (text) => {
  const lines = text
    .split("\n")
    .map((line) => line.trim())
    .filter((line) => line.length > 0);

  const result = {
    merchantName: "",
    date: "",
    amount: 0,
    currency: "USD",
    items: [],
    confidence: 0,
    rawText: text,
  };

  // Patterns for different data extraction
  const amountPatterns = [
    /(?:total|amount|sum|subtotal|grand total)[\s:]*\$?([0-9,]+\.?[0-9]*)/gi,
    /\$([0-9,]+\.?[0-9]*)/g,
    /([0-9,]+\.?[0-9]*)\s*(?:usd|dollar|$)/gi,
    /([0-9,]+\.?[0-9]*)\s*(?:khr|riel|៛)/gi,
  ];

  const datePatterns = [
    /(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})/g,
    /(\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2})/g,
    /(\d{1,2}\s+(?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\s+\d{2,4})/gi,
    /(?:date|dated?)[\s:]*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})/gi,
  ];

  const merchantPatterns = [
    /^([A-Z\s&]+)$/gm, // All caps line (often merchant name)
    /^([A-Za-z\s&]{3,30})$/gm, // Mixed case business name
  ];

  // Extract merchant name (usually first few lines)
  for (let i = 0; i < Math.min(5, lines.length); i++) {
    const line = lines[i];
    if (line.length > 3 && line.length < 50 && /^[A-Za-z\s&]+$/.test(line)) {
      result.merchantName = line;
      break;
    }
  }

  // Extract date
  for (const line of lines) {
    for (const pattern of datePatterns) {
      const match = line.match(pattern);
      if (match) {
        result.date = match[1];
        break;
      }
    }
    if (result.date) break;
  }

  // Extract amount
  let amounts = [];
  for (const line of lines) {
    for (const pattern of amountPatterns) {
      const matches = line.matchAll
        ? Array.from(line.matchAll(pattern))
        : [line.match(pattern)].filter(Boolean);
      for (const match of matches) {
        if (match && match[1]) {
          const amount = parseFloat(match[1].replace(/,/g, ""));
          if (!isNaN(amount) && amount > 0) {
            amounts.push(amount);
          }
        }
      }
    }
  }

  // Get the largest amount (likely the total)
  if (amounts.length > 0) {
    result.amount = Math.max(...amounts);
  }

  // Detect currency
  if (
    text.toLowerCase().includes("khr") ||
    text.includes("៛") ||
    text.toLowerCase().includes("riel")
  ) {
    result.currency = "KHR";
  }

  // Extract line items (simplified)
  const itemLines = lines.filter((line) => {
    return (
      line.length > 3 &&
      line.length < 100 &&
      !line.match(/^[\d\s\-\/]+$/) && // Not just numbers/dates
      !line.toLowerCase().includes("total") &&
      !line.toLowerCase().includes("subtotal") &&
      !line.toLowerCase().includes("tax")
    );
  });

  result.items = itemLines.slice(0, 10); // Limit to 10 items

  // Calculate confidence based on extracted data
  let confidence = 0;
  if (result.merchantName) confidence += 0.3;
  if (result.date) confidence += 0.3;
  if (result.amount > 0) confidence += 0.4;

  result.confidence = Math.round(confidence * 100) / 100;

  return result;
};

// @desc    Scan receipt image and extract transaction data
// @route   POST /api/ocr/scan
// @access  Private
const scanReceipt = async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        errors: errors.array(),
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: "No image file provided",
      });
    }

    const imageBuffer = req.file.buffer;
    const mimeType = req.file.mimetype;

    console.log("Starting Document AI OCR processing...");

    let documentAiResult = null;
    let usingFallback = false;

    try {
      // Primary: Use Document AI
      documentAiResult = await documentAiService.processReceipt(
        imageBuffer,
        mimeType
      );

      if (documentAiResult.success) {
        console.log("Document AI processing successful");

        const data = documentAiResult.data;

        // Format response for frontend compatibility
        const transactionSuggestion = {
          date: data.date || new Date().toISOString().split("T")[0],
          details: data.merchant_name || "Receipt purchase",
          money_in: 0,
          money_out: data.total_amount || 0,
          currency: data.currency || "USD",
          category_id: null,
          notes: `Document AI extracted receipt. ${
            data.items.length > 0
              ? `Items: ${data.items
                  .slice(0, 3)
                  .map((item) => item.description)
                  .join(", ")}${data.items.length > 3 ? "..." : ""}`
              : ""
          }`,
          ocr_confidence: data.confidence,
          merchant_name: data.merchant_name,
          subtotal: data.subtotal,
          tax_amount: data.tax_amount,
          line_items: data.items,
        };

        return res.json({
          success: true,
          message: "Receipt scanned successfully with Document AI",
          data: {
            extractedData: {
              merchantName: data.merchant_name,
              date: data.date,
              amount: data.total_amount,
              currency: data.currency,
              items: data.items.map((item) => item.description),
              confidence: data.confidence,
              rawText: data.raw_text.substring(0, 1000),
              subtotal: data.subtotal,
              taxAmount: data.tax_amount,
            },
            transactionSuggestion,
            ocrResult: {
              fullText: data.raw_text.substring(0, 1000),
              confidence: data.confidence,
              detectedItems: data.items.length,
              usingDocumentAI: true,
              usingMockService: false,
              processor: "document-ai",
            },
            documentAI: {
              lineItems: data.items,
              structuredData: data.structured_data,
            },
          },
        });
      }
    } catch (documentAiError) {
      console.error("Document AI error:", documentAiError.message);
      console.log("Falling back to enhanced OCR service...");
      usingFallback = true;
    }

    // Fallback 1: Try enhanced OCR service
    if (usingFallback) {
      try {
        const enhancedResult = await receiptOcrService.processImage(
          imageBuffer
        );

        if (enhancedResult.success) {
          console.log("Enhanced OCR processing successful");
          return res.json({
            success: true,
            message:
              "Receipt scanned successfully with enhanced OCR (Document AI fallback)",
            data: {
              text: enhancedResult.data.full_text,
              total_detection: enhancedResult.data.total_detection,
              date_extraction: enhancedResult.data.date_extraction,
              description_extraction:
                enhancedResult.data.description_extraction,
              suggested_transaction: {
                date:
                  enhancedResult.data.date_extraction?.formatted_date ||
                  new Date().toISOString().split("T")[0],
                details:
                  enhancedResult.data.description_extraction?.description ||
                  `Receipt processed via OCR - ${enhancedResult.data.total_detection.keyword}`,
                money_out: enhancedResult.data.total_detection.amount_value,
                currency: enhancedResult.data.total_detection.currency,
                imported_from: "ocr",
                merchant_name:
                  enhancedResult.data.description_extraction?.merchant_name,
                business_type:
                  enhancedResult.data.description_extraction?.business_type,
                ocr_confidence: {
                  total: enhancedResult.data.total_detection.confidence,
                  date: enhancedResult.data.date_extraction?.confidence || 0,
                  description:
                    enhancedResult.data.description_extraction?.confidence || 0,
                },
                notes: `Enhanced OCR extraction: Total(${
                  enhancedResult.data.total_detection.method
                }), Date(${
                  enhancedResult.data.date_extraction?.found
                    ? "found"
                    : "not found"
                }), Description(${
                  enhancedResult.data.description_extraction?.found
                    ? "found"
                    : "not found"
                })`,
              },
              debug_info: {
                raw_tokens: enhancedResult.data.raw_tokens,
                lines_detected: enhancedResult.data.lines_detected,
                keyword_anchors: enhancedResult.data.keyword_anchors,
                amount_candidates: enhancedResult.data.amount_candidates,
              },
              ocrResult: {
                processor: "enhanced-ocr",
                usingFallback: true,
              },
            },
          });
        }
      } catch (enhancedError) {
        console.error("Enhanced OCR error:", enhancedError.message);
        console.log("Falling back to basic Vision API...");
      }
    }

    // Fallback 2: Use Mock Service for testing
    let result, detections;
    let usingMockService = true;

    console.log("Using mock OCR service as final fallback...");
    result = await mockOCRService(imageBuffer);
    detections = result.textAnnotations;

    if (!detections || detections.length === 0) {
      return res.status(400).json({
        success: false,
        error:
          "No text detected in the image. Please ensure the receipt is clear and well-lit.",
      });
    }

    // Extract full text
    const fullText = detections[0].description || "";

    // Process the text to extract transaction data
    const extractedData = extractTransactionData(fullText);

    // Format the date if found
    if (extractedData.date) {
      try {
        const parsedDate = new Date(extractedData.date);
        if (!isNaN(parsedDate.getTime())) {
          extractedData.date = parsedDate.toISOString().split("T")[0];
        }
      } catch (error) {
        console.log("Date parsing error:", error);
        extractedData.date = new Date().toISOString().split("T")[0];
      }
    } else {
      extractedData.date = new Date().toISOString().split("T")[0];
    }

    // Suggest transaction details
    const transactionSuggestion = {
      date: extractedData.date,
      details: extractedData.merchantName || "Receipt purchase",
      money_in: 0,
      money_out: extractedData.amount,
      currency: extractedData.currency,
      category_id: null, // Will be suggested based on merchant name
      notes: `OCR extracted from receipt. Items: ${extractedData.items
        .slice(0, 3)
        .join(", ")}${extractedData.items.length > 3 ? "..." : ""}`,
      ocr_confidence: extractedData.confidence,
    };

    res.json({
      success: true,
      message: usingMockService
        ? "Receipt scanned successfully (using mock service - enable Google Cloud billing for production)"
        : "Receipt scanned successfully with Mock Service (fallback)",
      data: {
        extractedData,
        transactionSuggestion,
        ocrResult: {
          fullText: fullText.substring(0, 1000), // Limit text length
          confidence: extractedData.confidence,
          detectedItems: detections.length,
          usingMockService: usingMockService,
          usingDocumentAI: false,
          processor: usingMockService ? "mock" : "enhanced-ocr-fallback",
        },
      },
    });
  } catch (error) {
    console.error("OCR Error:", error);

    if (error.code === 3) {
      return res.status(400).json({
        success: false,
        error: "Invalid image format or corrupted file",
      });
    }

    if (error.code === 7) {
      return res.status(403).json({
        success: false,
        error:
          "Document AI API billing not enabled. Please enable billing or use the mock service for testing.",
      });
    }

    next(error);
  }
};

// @desc    Get OCR processing status
// @route   GET /api/ocr/status
// @access  Private
const getOCRStatus = async (req, res, next) => {
  try {
    const status = {
      documentAI: { available: false, error: null },
      enhancedOCR: { available: false, error: null },
      mockService: { available: true },
    };

    // Check Document AI availability
    try {
      // Create a simple test to check if Document AI is accessible
      const testResult = await documentAiService.createMockResponse(
        Buffer.from("test")
      );
      status.documentAI.available = true;
      console.log("Document AI service accessible");
    } catch (docAiError) {
      status.documentAI.error = docAiError.message;
      console.log("Document AI service not accessible:", docAiError.message);
    }

    // Check Enhanced OCR availability
    try {
      status.enhancedOCR.available = true; // Uses Document AI internally
    } catch (enhancedError) {
      status.enhancedOCR.error = enhancedError.message;
    }

    const overallStatus = status.documentAI.available
      ? "optimal"
      : status.enhancedOCR.available
      ? "good"
      : "limited";

    res.json({
      success: true,
      data: {
        status: overallStatus,
        services: status,
        recommendations: {
          primary: status.documentAI.available ? "Document AI" : "Enhanced OCR",
          fallback: status.enhancedOCR.available
            ? "Enhanced OCR"
            : "Mock Service",
        },
        supportedFormats: ["JPEG", "PNG", "WebP", "PDF"],
        maxFileSize: "10MB",
        features: {
          documentAI: [
            "Advanced receipt parsing",
            "Structured data extraction",
            "Line item detection",
            "Merchant identification",
            "Tax calculation",
            "Multi-currency support",
          ],
          enhancedOCR: [
            "Enhanced text detection",
            "Advanced pattern matching",
            "Smart amount extraction",
            "Date and merchant detection",
            "Document AI integration",
          ],
          mockService: [
            "Sample receipt generation",
            "Development testing",
            "API structure demonstration",
          ],
        },
        processingOrder: [
          "Document AI (primary)",
          "Enhanced OCR (fallback 1)",
          "Mock Service (development)",
        ],
      },
    });
  } catch (error) {
    console.error("Status check error:", error);
    res.status(503).json({
      success: false,
      error: "OCR service status check failed",
      data: {
        status: "error",
        message: error.message,
      },
    });
  }
};

module.exports = {
  scanReceipt,
  getOCRStatus,
  upload,
};
