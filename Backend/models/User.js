'use strict';
const bcrypt = require('bcryptjs');

module.exports = (sequelize, DataTypes) => {
  const User = sequelize.define('User', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 100]
      }
    },
    email: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true,
        notEmpty: true
      }
    },
    phone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      validate: {
        is: /^[+]?[\d\s\-()]+$/
      }
    },
    password_hash: {
      type: DataTypes.STRING(255),
      allowNull: true // Can be null for OAuth users
    },
    provider: {
      type: DataTypes.ENUM('local', 'google', 'facebook'),
      defaultValue: 'local',
      allowNull: false
    },
    provider_id: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    email_verified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    phone_verified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    preferred_language: {
      type: DataTypes.ENUM('en', 'km'),
      defaultValue: 'en'
    },
    preferred_currency: {
      type: DataTypes.STRING(3),
      defaultValue: 'USD',
      validate: {
        len: [3, 3]
      }
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    last_login: {
      type: DataTypes.DATE,
      allowNull: true
    },
    profile_picture: {
      type: DataTypes.STRING(500),
      allowNull: true
    },
    role: {
      type: DataTypes.ENUM('Freemium', 'Premium'),
      defaultValue: 'Freemium',
      allowNull: false,
      comment: 'User subscription role'
    },
    subscription_expires_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Premium subscription expiration date'
    }
  }, {
    tableName: 'users',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['email']
      },
      {
        fields: ['provider', 'provider_id']
      }
    ],
    hooks: {
      beforeCreate: async (user) => {
        if (user.password_hash) {
          user.password_hash = await bcrypt.hash(user.password_hash, 12);
        }
      },
      beforeUpdate: async (user) => {
        if (user.changed('password_hash') && user.password_hash) {
          user.password_hash = await bcrypt.hash(user.password_hash, 12);
        }
      }
    }
  });

  User.associate = function(models) {
    User.hasMany(models.Transaction, {
      foreignKey: 'user_id',
      as: 'transactions'
    });
    User.hasMany(models.Budget, {
      foreignKey: 'user_id',
      as: 'budgets'
    });
    User.hasMany(models.Goal, {
      foreignKey: 'user_id',
      as: 'goals'
    });
    User.hasMany(models.AuthRefreshToken, {
      foreignKey: 'user_id',
      as: 'refreshTokens'
    });
    User.hasMany(models.PasswordResetToken, {
      foreignKey: 'user_id',
      as: 'passwordResetTokens'
    });
    User.hasMany(models.ImportLog, {
      foreignKey: 'user_id',
      as: 'importLogs'
    });
    User.hasMany(models.UsageTracking, {
      foreignKey: 'user_id',
      as: 'usageTracking'
    });
    User.hasMany(models.Subscription, {
      foreignKey: 'user_id',
      as: 'subscriptions'
    });
    User.hasMany(models.Payment, {
      foreignKey: 'user_id',
      as: 'payments'
    });
    User.hasMany(models.Feedback, {
      foreignKey: 'user_id',
      as: 'feedback'
    });
  };

  // Instance methods
  User.prototype.validatePassword = async function(password) {
    if (!this.password_hash) return false;
    return await bcrypt.compare(password, this.password_hash);
  };

  User.prototype.isPremium = function() {
    return this.role === 'Premium' &&
           (!this.subscription_expires_at || new Date() < this.subscription_expires_at);
  };

  User.prototype.isFreemium = function() {
    return this.role === 'Freemium' || !this.isPremium();
  };

  User.prototype.upgradeToPremium = async function(expirationDate = null) {
    this.role = 'Premium';
    if (expirationDate) {
      this.subscription_expires_at = expirationDate;
    } else {
      // Default to 1 month from now
      const expiry = new Date();
      expiry.setMonth(expiry.getMonth() + 1);
      this.subscription_expires_at = expiry;
    }
    return await this.save();
  };

  User.prototype.downgradeToFreemium = async function() {
    this.role = 'Freemium';
    this.subscription_expires_at = null;
    return await this.save();
  };

  User.prototype.toJSON = function() {
    const values = Object.assign({}, this.get());
    delete values.password_hash;
    return values;
  };

  return User;
};
