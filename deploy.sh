#!/bin/bash

# Digital Ocean Deployment Script for Finwise Backend
# This script helps deploy the Finwise backend to Digital Ocean using Docker

set -e  # Exit on any error

echo "🚀 Starting Finwise Backend Deployment to Digital Ocean"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    echo "Install Docker with: curl -fsSL https://get.docker.com -o get-docker.sh && sudo sh get-docker.sh"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "Dockerfile" ]; then
    print_error "Dockerfile not found. Please run this script from the project root directory."
    exit 1
fi

# Check if Backend directory exists
if [ ! -d "Backend" ]; then
    print_error "Backend directory not found. Please run this script from the project root directory."
    exit 1
fi

# Stop and remove existing container if it exists
print_status "Checking for existing containers..."
if docker ps -a | grep -q finwise-backend; then
    print_warning "Stopping existing finwise-backend container..."
    docker stop finwise-backend || true
    docker rm finwise-backend || true
fi

# Remove old image if it exists
if docker images | grep -q finwise-backend; then
    print_warning "Removing old finwise-backend image..."
    docker rmi finwise-backend:latest || true
fi

# Build the Docker image
print_status "Building Docker image..."
if docker build -t finwise-backend:latest .; then
    print_success "Docker image built successfully!"
else
    print_error "Failed to build Docker image"
    exit 1
fi

# Check if .env file exists in Backend directory
if [ ! -f "Backend/.env" ]; then
    print_warning "No .env file found in Backend directory."
    if [ -f "Backend/.env.production" ]; then
        print_status "Copying .env.production to .env..."
        cp Backend/.env.production Backend/.env
    else
        print_error "No environment file found. Please create Backend/.env or Backend/.env.production"
        exit 1
    fi
fi

print_success "Build complete!"
print_status "Docker image 'finwise-backend:latest' is ready for deployment"

echo ""
echo "📋 Next steps for Digital Ocean deployment:"
echo "1. Transfer this image to your Digital Ocean droplet"
echo "2. Run the container with proper environment variables"
echo "3. Set up reverse proxy (nginx) if needed"
echo "4. Configure SSL certificate"
echo ""

# Option to run locally for testing
read -p "Do you want to run the container locally for testing? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Starting container locally..."

    # Create uploads directory if it doesn't exist
    mkdir -p Backend/uploads

    # Run container with environment file
    docker run -d \
        --name finwise-backend \
        -p 5003:5003 \
        --env-file Backend/.env \
        -v "$(pwd)/Backend/uploads:/app/uploads" \
        --restart unless-stopped \
        finwise-backend:latest

    if [ $? -eq 0 ]; then
        print_success "Container started successfully!"
        print_status "Backend is running at http://localhost:5003"
        print_status "Health check: http://localhost:5003/api/health"

        # Wait a moment and check if container is still running
        sleep 3
        if docker ps | grep -q finwise-backend; then
            print_success "Container is running properly!"
            echo ""
            echo "📊 Container status:"
            docker ps | grep finwise-backend
            echo ""
            echo "📋 To view logs: docker logs finwise-backend"
            echo "📋 To stop: docker stop finwise-backend"
        else
            print_error "Container stopped unexpectedly. Check logs:"
            docker logs finwise-backend
        fi
    else
        print_error "Failed to start container"
        exit 1
    fi
else
    print_status "Skipping local test run"
fi

echo ""
echo "🚀 For Digital Ocean deployment, use this command on your droplet:"
echo ""
echo "# Save this as deploy-on-droplet.sh on your Digital Ocean server"
echo "docker run -d \\"
echo "  --name finwise-backend \\"
echo "  -p 5003:5003 \\"
echo "  --env-file .env \\"
echo "  -v \$(pwd)/uploads:/app/uploads \\"
echo "  --restart unless-stopped \\"
echo "  finwise-backend:latest"
echo ""
echo "🔧 Make sure to:"
echo "- Copy your .env file to the droplet"
echo "- Create uploads directory: mkdir -p uploads"
echo "- Open port 5003 in firewall: sudo ufw allow 5003"
echo ""
print_success "Deployment script completed!"
