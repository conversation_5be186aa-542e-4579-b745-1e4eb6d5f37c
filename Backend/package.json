{"name": "finwise-backend", "version": "1.0.0", "description": "Finwise Financial Management Backend API", "main": "server.js", "type": "commonjs", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "migrate": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo", "seed": "npx sequelize-cli db:seed:all", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@google-cloud/documentai": "^9.4.0", "axios": "^1.11.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "connect-session-sequelize": "^7.1.7", "cors": "^2.8.5", "csv-parse": "^6.1.0", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-slow-down": "^2.0.1", "express-validator": "^7.0.1", "firebase": "^12.2.1", "firebase-admin": "^13.5.0", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "node-cron": "^4.2.1", "nodemailer": "^6.10.1", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-facebook-token": "^4.0.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "pdfkit": "^0.14.0", "qrcode": "^1.5.4", "sequelize": "^6.35.2", "twilio": "^5.1.0", "uuid": "^9.0.1", "winston": "^3.17.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/jest": "^29.5.8", "eslint": "^8.55.0", "eslint-config-node": "^4.1.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "sequelize-cli": "^6.6.2", "supertest": "^6.3.3"}, "keywords": ["finwise", "financial", "management", "expense", "tracker", "budget", "ocr", "receipt"], "author": "Finwise Team", "license": "MIT", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}