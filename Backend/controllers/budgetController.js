const { Budget, Category, Transaction } = require("../models");
const { validationResult } = require("express-validator");
const { Op } = require("sequelize");

// Get all budgets for a user
const getBudgets = async (req, res) => {
  try {
    const userId = req.user.id;
    const { active = "true", period_type } = req.query;

    const whereClause = { user_id: userId };

    if (active !== "all") {
      whereClause.is_active = active === "true";
    }

    if (period_type) {
      whereClause.period_type = period_type;
    }

    const budgets = await Budget.findAll({
      where: whereClause,
      include: [
        {
          model: Category,
          as: "category",
          attributes: ["id", "name", "name_km", "type", "icon", "color"],
        },
      ],
      order: [["created_at", "DESC"]],
    });

    // Calculate progress for each budget
    const budgetsWithProgress = await Promise.all(
      budgets.map(async (budget) => {
        const budgetData = budget.toJSON();

        // Calculate spent amount from transactions
        const spentAmount =
          (await Transaction.sum("money_out", {
            where: {
              user_id: userId,
              category_id: budget.category_id,
              date: {
                [Op.between]: [budget.period_start, budget.period_end],
              },
            },
          })) || 0;

        budgetData.spent_amount = parseFloat(spentAmount);
        budgetData.remaining_amount =
          parseFloat(budget.amount) - parseFloat(spentAmount);
        budgetData.progress_percentage = Math.min(
          (parseFloat(spentAmount) / parseFloat(budget.amount)) * 100,
          100
        );
        budgetData.is_over_budget =
          parseFloat(spentAmount) > parseFloat(budget.amount);
        budgetData.is_warning =
          (parseFloat(spentAmount) / parseFloat(budget.amount)) * 100 >=
          budget.warning_threshold;

        return budgetData;
      })
    );

    res.json({
      success: true,
      data: budgetsWithProgress,
    });
  } catch (error) {
    console.error("Get budgets error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch budgets",
    });
  }
};

// Get a single budget
const getBudget = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    const budget = await Budget.findOne({
      where: { id, user_id: userId },
      include: [
        {
          model: Category,
          as: "category",
          attributes: ["id", "name", "name_km", "type", "icon", "color"],
        },
      ],
    });

    if (!budget) {
      return res.status(404).json({
        success: false,
        error: "Budget not found",
      });
    }

    // Calculate spent amount
    const spentAmount =
      (await Transaction.sum("money_out", {
        where: {
          user_id: userId,
          category_id: budget.category_id,
          date: {
            [Op.between]: [budget.period_start, budget.period_end],
          },
        },
      })) || 0;

    const budgetData = budget.toJSON();
    budgetData.spent_amount = parseFloat(spentAmount);
    budgetData.remaining_amount =
      parseFloat(budget.amount) - parseFloat(spentAmount);
    budgetData.progress_percentage = Math.min(
      (parseFloat(spentAmount) / parseFloat(budget.amount)) * 100,
      100
    );
    budgetData.is_over_budget =
      parseFloat(spentAmount) > parseFloat(budget.amount);
    budgetData.is_warning =
      (parseFloat(spentAmount) / parseFloat(budget.amount)) * 100 >=
      budget.warning_threshold;

    res.json({
      success: true,
      data: budgetData,
    });
  } catch (error) {
    console.error("Get budget error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch budget",
    });
  }
};

// Create a new budget
const createBudget = async (req, res) => {
  try {
    console.log(
      "🔍 Budget creation request body:",
      JSON.stringify(req.body, null, 2)
    );

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log(
        "❌ Validation errors:",
        JSON.stringify(errors.array(), null, 2)
      );
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const userId = req.user.id;
    console.log("👤 User ID:", userId);

    const {
      name,
      name_km,
      category_id,
      amount,
      currency = "USD",
      period_type = "monthly",
      period_start,
      period_end,
      warning_threshold = 80,
      auto_renew = true,
      carry_over = false,
      notes,
      alert_settings,
    } = req.body;

    console.log("📊 Extracted data:", {
      name,
      category_id,
      amount,
      period_start,
      period_end,
      period_type,
    });

    // Check if category exists and is active
    const category = await Category.findOne({
      where: {
        id: category_id,
        is_active: true,
      },
    });

    if (!category) {
      console.log("❌ Category not found:", category_id);
      return res.status(400).json({
        success: false,
        error: "Invalid category",
      });
    }

    console.log("✅ Category found:", category.name);

    // Check for overlapping budgets for the same category
    const existingBudget = await Budget.findOne({
      where: {
        user_id: userId,
        category_id,
        is_active: true,
        [Op.or]: [
          {
            period_start: {
              [Op.between]: [period_start, period_end],
            },
          },
          {
            period_end: {
              [Op.between]: [period_start, period_end],
            },
          },
          {
            [Op.and]: [
              { period_start: { [Op.lte]: period_start } },
              { period_end: { [Op.gte]: period_end } },
            ],
          },
        ],
      },
    });

    if (existingBudget) {
      console.log("❌ Overlapping budget found:", existingBudget.id);
      return res.status(400).json({
        success: false,
        error:
          "A budget already exists for this category in the specified period",
      });
    }

    console.log("✅ No overlapping budgets found");

    const budgetData = {
      user_id: userId,
      name,
      name_km,
      category_id,
      amount,
      currency,
      period_type,
      period_start,
      period_end,
      spent_amount: 0,
      warning_threshold,
      auto_renew,
      carry_over,
      carry_over_amount: 0,
      is_active: true,
      notes,
      alert_settings,
    };

    console.log(
      "💾 Creating budget with data:",
      JSON.stringify(budgetData, null, 2)
    );

    const budget = await Budget.create(budgetData);

    console.log("✅ Budget created successfully:", budget.id);

    // Fetch the created budget with category info
    const createdBudget = await Budget.findByPk(budget.id, {
      include: [
        {
          model: Category,
          as: "category",
          attributes: ["id", "name", "name_km", "type", "icon", "color"],
        },
      ],
    });

    res.status(201).json({
      success: true,
      message: "Budget created successfully",
      data: createdBudget,
    });
  } catch (error) {
    console.error("💥 Create budget error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to create budget",
    });
  }
};

// Update a budget
const updateBudget = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const userId = req.user.id;
    const { id } = req.params;

    const budget = await Budget.findOne({
      where: { id, user_id: userId },
    });

    if (!budget) {
      return res.status(404).json({
        success: false,
        error: "Budget not found",
      });
    }

    const updateData = req.body;

    // If category is being changed, validate it
    if (updateData.category_id) {
      const category = await Category.findOne({
        where: {
          id: updateData.category_id,
          is_active: true,
        },
      });

      if (!category) {
        return res.status(400).json({
          success: false,
          error: "Invalid category",
        });
      }
    }

    await budget.update(updateData);

    // Fetch updated budget with category info
    const updatedBudget = await Budget.findByPk(budget.id, {
      include: [
        {
          model: Category,
          as: "category",
          attributes: ["id", "name", "name_km", "type", "icon", "color"],
        },
      ],
    });

    res.json({
      success: true,
      message: "Budget updated successfully",
      data: updatedBudget,
    });
  } catch (error) {
    console.error("Update budget error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to update budget",
    });
  }
};

// Delete a budget
const deleteBudget = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    const budget = await Budget.findOne({
      where: { id, user_id: userId },
    });

    if (!budget) {
      return res.status(404).json({
        success: false,
        error: "Budget not found",
      });
    }

    await budget.destroy();

    res.json({
      success: true,
      message: "Budget deleted successfully",
    });
  } catch (error) {
    console.error("Delete budget error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to delete budget",
    });
  }
};

module.exports = {
  getBudgets,
  getBudget,
  createBudget,
  updateBudget,
  deleteBudget,
};
