'use strict';

module.exports = (sequelize, DataTypes) => {
  const Feedback = sequelize.define('Feedback', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    feedback_content: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 2000]
      },
      comment: 'User feedback content'
    },
    feedback_month: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1,
        max: 12
      },
      comment: 'Month when feedback was given (1-12)'
    },
    feedback_year: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 2020
      },
      comment: 'Year when feedback was given'
    },
    rating: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1,
        max: 5
      },
      comment: 'Optional rating from 1-5 stars'
    },
    category: {
      type: DataTypes.ENUM('general', 'feature_request', 'bug_report', 'improvement', 'other'),
      defaultValue: 'general',
      allowNull: false,
      comment: 'Feedback category'
    },
    is_anonymous: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Whether feedback should be treated as anonymous'
    },
    status: {
      type: DataTypes.ENUM('new', 'reviewed', 'in_progress', 'resolved', 'closed'),
      defaultValue: 'new',
      allowNull: false,
      comment: 'Feedback processing status'
    },
    admin_response: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Admin response to feedback'
    },
    responded_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When admin responded to feedback'
    }
  }, {
    tableName: 'feedback',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['user_id', 'feedback_month', 'feedback_year'],
        name: 'unique_user_feedback_month_year'
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['feedback_month', 'feedback_year']
      },
      {
        fields: ['category']
      },
      {
        fields: ['status']
      },
      {
        fields: ['rating']
      }
    ]
  });

  Feedback.associate = function(models) {
    Feedback.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
  };

  // Instance methods
  Feedback.prototype.markAsReviewed = async function() {
    this.status = 'reviewed';
    return await this.save();
  };

  Feedback.prototype.addAdminResponse = async function(response) {
    this.admin_response = response;
    this.responded_at = new Date();
    this.status = 'resolved';
    return await this.save();
  };

  // Static methods
  Feedback.hasUserProvidedFeedbackThisMonth = async function(userId) {
    const now = new Date();
    const month = now.getMonth() + 1;
    const year = now.getFullYear();

    const feedback = await this.findOne({
      where: {
        user_id: userId,
        feedback_month: month,
        feedback_year: year
      }
    });

    return !!feedback;
  };

  Feedback.createMonthlyFeedback = async function(userId, content, rating = null, category = 'general') {
    const now = new Date();
    const month = now.getMonth() + 1;
    const year = now.getFullYear();

    // Check if user already provided feedback this month
    const existingFeedback = await this.findOne({
      where: {
        user_id: userId,
        feedback_month: month,
        feedback_year: year
      }
    });

    if (existingFeedback) {
      throw new Error('User has already provided feedback for this month');
    }

    return await this.create({
      user_id: userId,
      feedback_content: content,
      feedback_month: month,
      feedback_year: year,
      rating,
      category
    });
  };

  Feedback.getFeedbackStats = async function(month = null, year = null) {
    const whereClause = {};
    
    if (month && year) {
      whereClause.feedback_month = month;
      whereClause.feedback_year = year;
    } else if (year) {
      whereClause.feedback_year = year;
    }

    const stats = await this.findAll({
      where: whereClause,
      attributes: [
        'category',
        'rating',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
        [sequelize.fn('AVG', sequelize.col('rating')), 'avg_rating']
      ],
      group: ['category', 'rating']
    });

    return stats;
  };

  return Feedback;
};
