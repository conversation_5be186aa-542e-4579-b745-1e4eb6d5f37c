const express = require('express');
const router = express.Router();
const {
  getBillingInfo,
  createPremiumPayment,
  checkPaymentStatus,
  getUsageStats,
  cancelSubscription,
  getPaymentHistory,
  getMonitoringStatus
} = require('../controllers/billingController');
const { authenticateToken } = require('../middleware/auth');
const rateLimit = require('express-rate-limit');

// Rate limiting for payment endpoints
const paymentRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 payment requests per windowMs
  message: {
    success: false,
    error: 'Too many payment requests, please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Apply authentication to all routes
router.use(authenticateToken);

/**
 * @route   GET /api/billing/info
 * @desc    Get user's billing and subscription information
 * @access  Private
 */
router.get('/info', getBillingInfo);

/**
 * @route   POST /api/billing/premium
 * @desc    Create premium subscription payment
 * @access  Private
 * @body    sessionId: string (optional)
 */
router.post('/premium', paymentRateLimit, createPremiumPayment);

/**
 * @route   GET /api/billing/payment/:paymentId/status
 * @desc    Check payment status
 * @access  Private
 */
router.get('/payment/:paymentId/status', checkPaymentStatus);

/**
 * @route   GET /api/billing/usage
 * @desc    Get usage statistics
 * @access  Private
 */
router.get('/usage', getUsageStats);

/**
 * @route   POST /api/billing/cancel
 * @desc    Cancel subscription
 * @access  Private
 * @body    reason: string (optional)
 */
router.post('/cancel', cancelSubscription);

/**
 * @route   GET /api/billing/payments
 * @desc    Get payment history
 * @access  Private
 * @query   page: number (optional, default: 1)
 * @query   limit: number (optional, default: 20)
 */
router.get('/payments', getPaymentHistory);

/**
 * @route   GET /api/billing/monitoring
 * @desc    Get payment monitoring status (admin only)
 * @access  Private
 */
router.get('/monitoring', getMonitoringStatus);

module.exports = router;
