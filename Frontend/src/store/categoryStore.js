import { create } from 'zustand';
import { categoryAPI } from '../services/api';
import toast from 'react-hot-toast';

const useCategoryStore = create((set, get) => ({
  // State
  categories: [],
  isLoading: false,
  error: null,
  currentCategory: null,

  // Actions
  fetchCategories: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await categoryAPI.getCategories();
      const categories = response.data.data || [];

      set({
        categories,
        isLoading: false,
        error: null
      });

      return { success: true, data: categories };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to fetch categories';
      
      set({
        categories: [],
        isLoading: false,
        error: errorMessage
      });

      return { success: false, error: errorMessage };
    }
  },

  createCategory: async (categoryData) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await categoryAPI.createCategory(categoryData);
      const newCategory = response.data.data;

      set(state => ({
        categories: [...state.categories, newCategory],
        isLoading: false,
        error: null
      }));

      toast.success('Category created successfully!');
      return { success: true, data: newCategory };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to create category';
      
      set({
        isLoading: false,
        error: errorMessage
      });

      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  updateCategory: async (categoryId, categoryData) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await categoryAPI.updateCategory(categoryId, categoryData);
      const updatedCategory = response.data.data;

      set(state => ({
        categories: state.categories.map(category => 
          category.id === categoryId ? updatedCategory : category
        ),
        isLoading: false,
        error: null,
        currentCategory: state.currentCategory?.id === categoryId ? updatedCategory : state.currentCategory
      }));

      toast.success('Category updated successfully!');
      return { success: true, data: updatedCategory };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to update category';
      
      set({
        isLoading: false,
        error: errorMessage
      });

      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  deleteCategory: async (categoryId) => {
    set({ isLoading: true, error: null });
    
    try {
      await categoryAPI.deleteCategory(categoryId);

      set(state => ({
        categories: state.categories.filter(category => category.id !== categoryId),
        isLoading: false,
        error: null,
        currentCategory: state.currentCategory?.id === categoryId ? null : state.currentCategory
      }));

      toast.success('Category deleted successfully!');
      return { success: true };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to delete category';
      
      set({
        isLoading: false,
        error: errorMessage
      });

      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  getCategory: async (categoryId) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await categoryAPI.getCategory(categoryId);
      const category = response.data.data;

      set({
        currentCategory: category,
        isLoading: false,
        error: null
      });

      return { success: true, data: category };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to fetch category';
      
      set({
        currentCategory: null,
        isLoading: false,
        error: errorMessage
      });

      return { success: false, error: errorMessage };
    }
  },

  // Utility actions
  setCurrentCategory: (category) => {
    set({ currentCategory: category });
  },

  clearCurrentCategory: () => {
    set({ currentCategory: null });
  },

  clearError: () => {
    set({ error: null });
  },

  // Computed values
  getCategoriesByType: (type) => {
    const { categories } = get();
    return categories.filter(category => category.type === type);
  },

  getIncomeCategories: () => {
    return get().getCategoriesByType('income');
  },

  getExpenseCategories: () => {
    return get().getCategoriesByType('expense');
  },

  getCategoryById: (categoryId) => {
    const { categories } = get();
    return categories.find(category => category.id === categoryId);
  },

  getCategoryName: (categoryId) => {
    const category = get().getCategoryById(categoryId);
    return category ? category.name : 'Unknown Category';
  },

  getDefaultCategories: () => {
    const { categories } = get();
    return categories.filter(category => category.is_default);
  },

  getActiveCategories: () => {
    const { categories } = get();
    return categories.filter(category => category.is_active);
  }
}));

export default useCategoryStore;
