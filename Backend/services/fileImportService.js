const XLSX = require("xlsx");
const csv = require("csv-parse");
const pdf = require("pdf-parse");
const fs = require("fs");
const path = require("path");
const excelImportService = require("./excelImportService");

class FileImportService {
  constructor() {
    this.supportedFormats = [".xlsx", ".xls", ".csv", ".pdf"];
    this.csvDelimiters = [",", ";", "\t", "|"];
  }

  /**
   * Determine file type and route to appropriate processor
   */
  async processFile(fileBuffer, fileName, userId, options = {}) {
    try {
      const fileExtension = path.extname(fileName).toLowerCase();

      console.log(`📁 Processing ${fileExtension} file: ${fileName}`);

      switch (fileExtension) {
        case ".xlsx":
        case ".xls":
          return await this.processExcelFile(fileBuffer, userId, {
            ...options,
            fileName,
          });

        case ".csv":
          return await this.processCsvFile(fileBuffer, userId, {
            ...options,
            fileName,
          });

        case ".pdf":
          return await this.processPdfFile(fileBuffer, userId, {
            ...options,
            fileName,
          });

        default:
          throw new Error(`Unsupported file format: ${fileExtension}`);
      }
    } catch (error) {
      console.error("❌ File processing error:", error);
      throw error;
    }
  }

  /**
   * Process Excel files using existing excel import service
   */
  async processExcelFile(fileBuffer, userId, options) {
    return await excelImportService.importExcelFile(
      fileBuffer,
      userId,
      options
    );
  }

  /**
   * Process CSV files
   */
  async processCsvFile(fileBuffer, userId, options) {
    try {
      const csvText = fileBuffer.toString("utf-8");
      const delimiter = this.detectCsvDelimiter(csvText);

      console.log(`Detected CSV delimiter: "${delimiter}"`);

      return new Promise((resolve, reject) => {
        const records = [];

        csv
          .parse(csvText, {
            delimiter: delimiter,
            columns: true,
            skip_empty_lines: true,
            trim: true,
            relax_quotes: true,
          })
          .on("data", (record) => {
            records.push(record);
          })
          .on("error", (error) => {
            reject(error);
          })
          .on("end", async () => {
            try {
              const result = await this.processCsvRecords(
                records,
                userId,
                options
              );
              resolve(result);
            } catch (error) {
              reject(error);
            }
          });
      });
    } catch (error) {
      throw new Error(`CSV processing failed: ${error.message}`);
    }
  }

  /**
   * Process PDF files (basic text extraction)
   */
  async processPdfFile(fileBuffer, userId, options) {
    try {
      console.log("Processing PDF file...");

      const pdfData = await pdf(fileBuffer);
      const text = pdfData.text;

      // Try to extract tabular data from PDF text
      const lines = text.split("\n").filter((line) => line.trim());
      const transactions = this.extractTransactionsFromPdfText(lines);

      if (transactions.length === 0) {
        return {
          success: false,
          error:
            "No transaction data found in PDF. Please ensure the PDF contains a readable bank statement.",
          processedTransactions: 0,
          uniqueTransactions: 0,
          duplicates: [],
          errors: [],
        };
      }

      const result = await this.processTransactionRecords(
        transactions,
        userId,
        options
      );
      return result;
    } catch (error) {
      throw new Error(`PDF processing failed: ${error.message}`);
    }
  }

  /**
   * Detect CSV delimiter
   */
  detectCsvDelimiter(csvText) {
    const sampleLines = csvText.split("\n").slice(0, 5).join("\n");
    let bestDelimiter = ",";
    let maxFields = 0;

    for (const delimiter of this.csvDelimiters) {
      const fieldCount = (
        sampleLines.match(new RegExp("\\" + delimiter, "g")) || []
      ).length;
      if (fieldCount > maxFields) {
        maxFields = fieldCount;
        bestDelimiter = delimiter;
      }
    }

    return bestDelimiter;
  }

  /**
   * Process CSV records into transactions
   */
  async processCsvRecords(records, userId, options) {
    try {
      console.log(`Processing ${records.length} CSV records`);

      if (records.length === 0) {
        return {
          success: false,
          error: "No data found in CSV file",
          processedTransactions: 0,
          uniqueTransactions: 0,
          duplicates: [],
          errors: [],
        };
      }

      // Auto-detect format based on column headers
      const format = this.detectCsvFormat(records[0]);
      console.log(`Detected CSV format: ${format}`);

      // Map CSV columns to standard transaction fields
      const transactions = records
        .map((record, index) => {
          try {
            return this.mapCsvRecordToTransaction(record, format, index);
          } catch (error) {
            console.warn(
              `Skipping invalid CSV record ${index + 1}:`,
              error.message
            );
            return null;
          }
        })
        .filter(Boolean);

      return await this.processTransactionRecords(
        transactions,
        userId,
        options
      );
    } catch (error) {
      throw new Error(`CSV record processing failed: ${error.message}`);
    }
  }

  /**
   * Detect CSV format based on headers
   */
  detectCsvFormat(firstRecord) {
    const headers = Object.keys(firstRecord).map((h) => h.toLowerCase());

    // Check for ACLEDA format indicators
    const acledaIndicators = [
      "cash in",
      "cash out",
      "particulars",
      "trans date",
    ];
    const abaIndicators = ["money in", "money out", "transaction details"];

    const acledaMatches = acledaIndicators.filter((indicator) =>
      headers.some((header) => header.includes(indicator))
    ).length;

    const abaMatches = abaIndicators.filter((indicator) =>
      headers.some((header) => header.includes(indicator))
    ).length;

    if (acledaMatches > abaMatches) {
      return "ACLEDA";
    } else if (abaMatches > 0) {
      return "ABA";
    } else {
      return "GENERIC";
    }
  }

  /**
   * Map CSV record to transaction object
   */
  mapCsvRecordToTransaction(record, format, index) {
    const transaction = {
      date: null,
      details: "",
      amount: 0,
      type: "expense",
      currency: "USD",
      source: `CSV-${format}`,
      row_number: index + 1,
    };

    // Map based on detected format
    switch (format) {
      case "ACLEDA":
        transaction.date = this.parseDate(
          record["Trans Date"] || record["Date"] || record["date"]
        );
        transaction.details =
          record["Particulars"] ||
          record["Description"] ||
          record["details"] ||
          "";

        const cashIn = this.parseAmount(
          record["Cash In(Cr)"] ||
            record["Cash In"] ||
            record["CASH IN (Cr)"] ||
            "0"
        );
        const cashOut = this.parseAmount(
          record["Cash Out(Dr)"] ||
            record["Cash Out"] ||
            record["CASH OUT (Dr)"] ||
            "0"
        );

        if (cashIn > 0) {
          transaction.amount = cashIn;
          transaction.type = "income";
        } else if (cashOut > 0) {
          transaction.amount = cashOut;
          transaction.type = "expense";
        }

        transaction.currency = record["CCY"] || record["Currency"] || "USD";
        break;

      case "ABA":
        transaction.date = this.parseDate(
          record["Date"] || record["Transaction Date"] || record["date"]
        );
        transaction.details =
          record["Transaction Details"] ||
          record["Description"] ||
          record["details"] ||
          "";

        const moneyIn = this.parseAmount(
          record["Money In"] || record["MONEY IN"] || "0"
        );
        const moneyOut = this.parseAmount(
          record["Money Out"] || record["MONEY OUT"] || "0"
        );

        if (moneyIn > 0) {
          transaction.amount = moneyIn;
          transaction.type = "income";
        } else if (moneyOut > 0) {
          transaction.amount = moneyOut;
          transaction.type = "expense";
        }

        transaction.currency = record["CCY"] || record["Currency"] || "USD";
        break;

      case "GENERIC":
      default:
        // Try to map common column names
        const dateFields = ["date", "transaction date", "trans date", "Date"];
        const descFields = [
          "description",
          "details",
          "particulars",
          "memo",
          "Description",
        ];
        const amountFields = ["amount", "Amount", "value", "total"];
        const typeFields = ["type", "Type", "transaction type"];

        // Find date field
        for (const field of dateFields) {
          if (record[field]) {
            transaction.date = this.parseDate(record[field]);
            break;
          }
        }

        // Find description field
        for (const field of descFields) {
          if (record[field]) {
            transaction.details = record[field];
            break;
          }
        }

        // Find amount field
        for (const field of amountFields) {
          if (record[field]) {
            transaction.amount = Math.abs(this.parseAmount(record[field]));
            break;
          }
        }

        // Find type field
        for (const field of typeFields) {
          if (record[field]) {
            const typeValue = record[field].toLowerCase();
            transaction.type =
              typeValue.includes("income") ||
              typeValue.includes("credit") ||
              typeValue.includes("in")
                ? "income"
                : "expense";
            break;
          }
        }
        break;
    }

    // Validate required fields
    if (!transaction.date || !transaction.amount || transaction.amount <= 0) {
      throw new Error(`Invalid transaction data: missing date or amount`);
    }

    return transaction;
  }

  /**
   * Extract transactions from PDF text
   */
  extractTransactionsFromPdfText(lines) {
    const transactions = [];
    let currentTransaction = null;

    for (const line of lines) {
      // Skip empty lines and headers
      if (!line.trim() || line.includes("Page") || line.includes("Statement")) {
        continue;
      }

      // Try to parse line as transaction (very basic implementation)
      const dateMatch = line.match(/(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})/);
      const amountMatch = line.match(/[\$]?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/);

      if (dateMatch && amountMatch) {
        const transaction = {
          date: this.parseDate(dateMatch[1]),
          details: line
            .replace(dateMatch[0], "")
            .replace(amountMatch[0], "")
            .trim(),
          amount: this.parseAmount(amountMatch[1]),
          type: "expense", // Default to expense, could be improved with better parsing
          currency: "USD",
          source: "PDF",
        };

        if (transaction.date && transaction.amount > 0) {
          transactions.push(transaction);
        }
      }
    }

    return transactions;
  }

  /**
   * Parse date from various formats
   */
  parseDate(dateStr) {
    if (!dateStr) return null;

    try {
      // Handle various date formats
      const cleanDate = dateStr.toString().trim();

      // Try parsing common formats
      const formats = [
        /(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})/, // MM/DD/YYYY or DD/MM/YYYY
        /(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2})/, // MM/DD/YY or DD/MM/YY
        /(\d{4})[\/\-](\d{1,2})[\/\-](\d{1,2})/, // YYYY/MM/DD
      ];

      for (const format of formats) {
        const match = cleanDate.match(format);
        if (match) {
          const date = new Date(cleanDate);
          if (!isNaN(date.getTime())) {
            return date.toISOString().split("T")[0];
          }
        }
      }

      // If no format matches, try direct parsing
      const date = new Date(cleanDate);
      if (!isNaN(date.getTime())) {
        return date.toISOString().split("T")[0];
      }

      return null;
    } catch (error) {
      console.warn("⚠️ Date parsing failed:", dateStr, error.message);
      return null;
    }
  }

  /**
   * Parse amount from string
   */
  parseAmount(amountStr) {
    if (!amountStr) return 0;

    try {
      // Remove currency symbols, commas, and extra spaces
      const cleanAmount = amountStr
        .toString()
        .replace(/[\$€£¥₹₨]/g, "")
        .replace(/,/g, "")
        .replace(/\s+/g, "")
        .trim();

      const amount = parseFloat(cleanAmount);
      return isNaN(amount) ? 0 : Math.abs(amount);
    } catch (error) {
      console.warn("Amount parsing failed:", amountStr, error.message);
      return 0;
    }
  }

  /**
   * Process transaction records (common logic for all formats)
   */
  async processTransactionRecords(transactions, userId, options) {
    try {
      const { fileName, skipDuplicates = true } = options;

      console.log(
        `🔄 Processing ${transactions.length} transactions for user ${userId}`
      );

      const processedTransactions = [];
      const errors = [];
      const duplicates = [];

      for (const [index, transaction] of transactions.entries()) {
        try {
          // Add user ID and metadata
          const processedTransaction = {
            ...transaction,
            user_id: userId,
            category_id: 1, // Default category
            imported_at: new Date(),
            import_source: fileName,
          };

          // Check for duplicates if enabled
          if (skipDuplicates) {
            const isDuplicate = await this.checkForDuplicate(
              processedTransaction,
              userId
            );
            if (isDuplicate) {
              duplicates.push(processedTransaction);
              continue;
            }
          }

          processedTransactions.push(processedTransaction);
        } catch (error) {
          errors.push({
            row: index + 1,
            error: error.message,
            data: transaction,
          });
        }
      }

      return {
        success: true,
        processedTransactions: processedTransactions.length,
        uniqueTransactions: processedTransactions.length,
        duplicates: duplicates.length,
        errors: errors.length,
        transactions: processedTransactions,
        duplicateTransactions: duplicates,
        errorTransactions: errors,
        metadata: {
          fileName,
          totalRows: transactions.length,
          format: "Multi-format",
        },
      };
    } catch (error) {
      throw new Error(`Transaction processing failed: ${error.message}`);
    }
  }

  /**
   * Check for duplicate transactions
   */
  async checkForDuplicate(transaction, userId) {
    try {
      // Simple duplicate check based on date, amount, and details
      const existingTransaction = await Transaction.findOne({
        where: {
          user_id: userId,
          date: transaction.date,
          amount: transaction.amount,
          details: transaction.details,
        },
      });

      return !!existingTransaction;
    } catch (error) {
      console.warn("⚠️ Duplicate check failed:", error.message);
      return false;
    }
  }

  /**
   * Validate file format
   */
  validateImportFile(fileBuffer, fileName) {
    try {
      const fileExtension = path.extname(fileName).toLowerCase();

      if (!this.supportedFormats.includes(fileExtension)) {
        return {
          valid: false,
          error: `Unsupported file format: ${fileExtension}. Supported formats: ${this.supportedFormats.join(
            ", "
          )}`,
        };
      }

      // Basic file size check (should be handled by multer, but double-check)
      if (fileBuffer.length > 10 * 1024 * 1024) {
        return {
          valid: false,
          error: "File size exceeds 10MB limit",
        };
      }

      return { valid: true };
    } catch (error) {
      return {
        valid: false,
        error: `File validation failed: ${error.message}`,
      };
    }
  }
}

module.exports = new FileImportService();
