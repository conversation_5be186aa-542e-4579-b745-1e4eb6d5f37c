import React from 'react';
import { <PERSON>, <PERSON>, Monitor } from 'lucide-react';
import useThemeStore from '../../store/themeStore';

const ThemeToggle = ({ className = '' }) => {
  const { theme, setTheme } = useThemeStore();

  const themes = [
    { value: 'light', icon: Sun, label: 'Light' },
    { value: 'dark', icon: Moon, label: 'Dark' },
    { value: 'system', icon: Monitor, label: 'System' }
  ];

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      {themes.map(({ value, icon: Icon, label }) => (
        <button
          key={value}
          onClick={() => setTheme(value)}
          className={`
            p-2 rounded-lg transition-colors duration-200
            ${theme === value 
              ? 'bg-primary-100 text-primary-600 dark:bg-primary-900 dark:text-primary-400' 
              : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-800'
            }
          `}
          title={label}
        >
          <Icon className="w-4 h-4" />
        </button>
      ))}
    </div>
  );
};

export default ThemeToggle;
