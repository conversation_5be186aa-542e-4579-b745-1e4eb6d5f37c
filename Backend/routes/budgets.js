const express = require("express");
const router = express.Router();
const { body } = require("express-validator");
const budgetController = require("../controllers/budgetController");
const { authenticateToken } = require("../middleware/auth");

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Validation rules
const budgetValidation = [
  body("name").notEmpty().withMessage("Budget name is required"),
  body("category_id").isUUID().withMessage("Valid category ID is required"),
  body("amount")
    .isFloat({ min: 0.01 })
    .withMessage("Amount must be greater than 0"),
  body("period_start").isDate().withMessage("Valid start date is required"),
  body("period_end").isDate().withMessage("Valid end date is required"),
  body("warning_threshold")
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage("Warning threshold must be between 0 and 100"),
  body("currency")
    .optional()
    .isLength({ min: 3, max: 3 })
    .withMessage("Currency must be 3 characters"),
  body("period_type")
    .optional()
    .isIn(["daily", "weekly", "monthly", "quarterly", "yearly"])
    .withMessage("Invalid period type"),
];

const budgetUpdateValidation = [
  body("name").optional().notEmpty().withMessage("Budget name cannot be empty"),
  body("category_id")
    .optional()
    .isUUID()
    .withMessage("Valid category ID is required"),
  body("amount")
    .optional()
    .isFloat({ min: 0.01 })
    .withMessage("Amount must be greater than 0"),
  body("period_start")
    .optional()
    .isDate()
    .withMessage("Valid start date is required"),
  body("period_end")
    .optional()
    .isDate()
    .withMessage("Valid end date is required"),
  body("warning_threshold")
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage("Warning threshold must be between 0 and 100"),
  body("currency")
    .optional()
    .isLength({ min: 3, max: 3 })
    .withMessage("Currency must be 3 characters"),
  body("period_type")
    .optional()
    .isIn(["daily", "weekly", "monthly", "quarterly", "yearly"])
    .withMessage("Invalid period type"),
];

// Routes
router.get("/", budgetController.getBudgets);
router.get("/:id", budgetController.getBudget);
router.post("/", budgetValidation, budgetController.createBudget);
router.put("/:id", budgetUpdateValidation, budgetController.updateBudget);
router.delete("/:id", budgetController.deleteBudget);

module.exports = router;
