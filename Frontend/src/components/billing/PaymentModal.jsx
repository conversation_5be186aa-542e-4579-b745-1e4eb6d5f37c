import React, { useState, useEffect } from 'react';
import { X, QrC<PERSON>, Refresh<PERSON><PERSON>, CheckCircle, AlertCircle, Clock } from 'lucide-react';
import useBillingStore from '../../store/billingStore';
import { toast } from 'react-hot-toast';
import { useTranslation } from 'react-i18next';

const PaymentModal = ({ isOpen, onClose }) => {
  const [paymentStep, setPaymentStep] = useState('create'); // create, qr, checking, success, error
  const [checkingInterval, setCheckingInterval] = useState(null);
  const [timeRemaining, setTimeRemaining] = useState(1800); // 30 minutes in seconds
  const { t } = useTranslation();

  const {
    currentPayment,
    isLoading,
    createPremiumPayment,
    checkPaymentStatus,
    clearCurrentPayment
  } = useBillingStore();

  useEffect(() => {
    if (!isOpen) {
      // Clean up when modal closes
      if (checkingInterval) {
        clearInterval(checkingInterval);
        setCheckingInterval(null);
      }
      setPaymentStep('create');
      setTimeRemaining(1800);
      clearCurrentPayment();
    }
  }, [isOpen]);

  useEffect(() => {
    // Start countdown timer when QR is shown
    if (paymentStep === 'qr' || paymentStep === 'checking') {
      const timer = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            setPaymentStep('error');
            toast.error('Payment session expired');
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [paymentStep]);

  const handleCreatePayment = async () => {
    try {
      const result = await createPremiumPayment();
      if (result.success) {
        setPaymentStep('qr');
        startPaymentMonitoring(result.data.paymentId);
      }
    } catch (error) {
      setPaymentStep('error');
      toast.error('Failed to create payment');
    }
  };

  const startPaymentMonitoring = (paymentId) => {
    const interval = setInterval(async () => {
      try {
        const result = await checkPaymentStatus(paymentId);
        if (result.success && result.data.status === 'completed') {
          clearInterval(interval);
          setCheckingInterval(null);
          setPaymentStep('success');
          toast.success('Payment successful! Your account has been upgraded to Premium.');
        }
      } catch (error) {
        console.error('Error checking payment status:', error);
      }
    }, 5000); // Check every 5 seconds

    setCheckingInterval(interval);
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleClose = () => {
    if (checkingInterval) {
      clearInterval(checkingInterval);
      setCheckingInterval(null);
    }
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Upgrade to Premium
          </h3>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {paymentStep === 'create' && (
            <div className="text-center space-y-4">
              <div className="bg-gradient-to-r from-purple-100 to-blue-100 rounded-lg p-6">
                <h4 className="text-xl font-bold text-gray-900 mb-2">Premium Plan</h4>
                <p className="text-3xl font-bold text-purple-600 mb-2">$1.99/month</p>
                <p className="text-gray-600 text-sm">Unlimited access to all features</p>
              </div>

              <div className="space-y-3 text-left">
                <h5 className="font-semibold text-gray-900">What you'll get:</h5>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    Unlimited receipt scans
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    Unlimited Excel imports
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    Advanced analytics
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    Priority support
                  </li>
                </ul>
              </div>

              <button
                onClick={handleCreatePayment}
                disabled={isLoading}
                className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 rounded-lg font-medium hover:from-purple-700 hover:to-blue-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
              >
                {isLoading ? (
                  <>
                    <RefreshCw className="h-5 w-5 animate-spin" />
                    Creating Payment...
                  </>
                ) : (
                  <>
                    <QrCode className="h-5 w-5" />
                    Generate Payment QR
                  </>
                )}
              </button>
            </div>
          )}

          {(paymentStep === 'qr' || paymentStep === 'checking') && currentPayment && (
            <div className="text-center space-y-4">
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Clock className="h-5 w-5 text-blue-600" />
                  <span className="font-medium text-blue-900">
                    Time remaining: {formatTime(timeRemaining)}
                  </span>
                </div>
                <p className="text-sm text-blue-700">
                  Scan the QR code with your Bakong app to complete payment
                </p>
              </div>

              {currentPayment.imagePath ? (
                <div className="bg-white border-2 border-gray-200 rounded-lg p-4">
                  <img
                    src={`http://localhost:5003${currentPayment.imagePath}`}
                    alt="Payment QR Code"
                    className="w-full max-w-xs mx-auto"
                    onError={(e) => {
                      console.error('QR Image load error:', e);
                      console.log('Attempted URL:', e.target.src);
                    }}
                    onLoad={() => console.log('QR Image loaded successfully')}
                  />
                </div>
              ) : (
                <div className="bg-gray-100 rounded-lg p-8">
                  <QrCode className="h-24 w-24 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500">QR Code will appear here</p>
                </div>
              )}

              <div className="space-y-2 text-sm text-gray-600">
                <p><strong>Amount:</strong> ${currentPayment.amount} {currentPayment.currency}</p>
                <p><strong>Bill Number:</strong> {currentPayment.billNumber}</p>
              </div>

              {paymentStep === 'checking' && (
                <div className="flex items-center justify-center gap-2 text-blue-600">
                  <RefreshCw className="h-5 w-5 animate-spin" />
                  <span>Checking payment status...</span>
                </div>
              )}

              <button
                onClick={() => setPaymentStep('checking')}
                className="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Check Payment Status
              </button>
            </div>
          )}

          {paymentStep === 'success' && (
            <div className="text-center space-y-4">
              <div className="bg-green-50 rounded-lg p-6">
                <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                <h4 className="text-xl font-bold text-green-900 mb-2">Payment Successful!</h4>
                <p className="text-green-700">
                  Your account has been upgraded to Premium. You now have unlimited access to all features.
                </p>
              </div>

              <button
                onClick={handleClose}
                className="w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 transition-colors"
              >
                Continue
              </button>
            </div>
          )}

          {paymentStep === 'error' && (
            <div className="text-center space-y-4">
              <div className="bg-red-50 rounded-lg p-6">
                <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
                <h4 className="text-xl font-bold text-red-900 mb-2">Payment Failed</h4>
                <p className="text-red-700">
                  The payment session has expired or failed. Please try again.
                </p>
              </div>

              <div className="space-y-2">
                <button
                  onClick={() => setPaymentStep('create')}
                  className="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Try Again
                </button>
                <button
                  onClick={handleClose}
                  className="w-full bg-gray-500 text-white py-2 rounded-lg hover:bg-gray-600 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PaymentModal;
