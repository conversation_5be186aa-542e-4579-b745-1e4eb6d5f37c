name: Deploy Frontend to Netlify

on:
  push:
    branches: [ main ]
    paths:
      - 'Frontend/**'
      - 'netlify.toml'
      - '.github/workflows/deploy-frontend.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'Frontend/**'
      - 'netlify.toml'

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: Frontend/package-lock.json
        
    - name: Install dependencies
      run: |
        cd Frontend
        npm ci --legacy-peer-deps
        
    - name: Run tests
      run: |
        cd Frontend
        npm run test -- --run
        
    - name: Build application
      run: |
        cd Frontend
        npm run build
      env:
        VITE_API_URL: ${{ secrets.VITE_API_URL }}
        VITE_API_BASE_URL: ${{ secrets.VITE_API_BASE_URL }}
        VITE_GOOGLE_OAUTH_URL: ${{ secrets.VITE_GOOGLE_OAUTH_URL }}
        VITE_FACEBOOK_OAUTH_URL: ${{ secrets.VITE_FACEBOOK_OAUTH_URL }}
        VITE_APP_NAME: Finwise
        VITE_APP_VERSION: 1.0.0
        VITE_NODE_ENV: production
        
    - name: Deploy to Netlify
      uses: nwtgck/actions-netlify@v3.0
      with:
        publish-dir: './Frontend/dist'
        production-branch: main
        github-token: ${{ secrets.GITHUB_TOKEN }}
        deploy-message: "Deploy from GitHub Actions"
        enable-pull-request-comment: false
        enable-commit-comment: true
        overwrites-pull-request-comment: true
      env:
        NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
        NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}
        
    - name: Lighthouse CI
      uses: treosh/lighthouse-ci-action@v10
      with:
        configPath: './Frontend/.lighthouserc.json'
        uploadArtifacts: true
        temporaryPublicStorage: true
      continue-on-error: true
      
  notify:
    needs: build-and-deploy
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Notify deployment status
      run: |
        if [ "${{ needs.build-and-deploy.result }}" == "success" ]; then
          echo "✅ Frontend deployment successful!"
        else
          echo "❌ Frontend deployment failed!"
          exit 1
        fi
