'use strict';

module.exports = (sequelize, DataTypes) => {
  const ImportLog = sequelize.define('ImportLog', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    filename: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: 'Original filename for audit purposes'
    },
    file_type: {
      type: DataTypes.ENUM('excel', 'csv', 'pdf'),
      allowNull: false,
      defaultValue: 'excel'
    },
    file_size: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'File size in bytes'
    },
    row_count: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Total rows processed'
    },
    inserted_count: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Successfully inserted transactions'
    },
    duplicate_count: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Duplicate transactions skipped'
    },
    error_count: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Rows with errors'
    },
    status: {
      type: DataTypes.ENUM('processing', 'completed', 'failed', 'partial'),
      allowNull: false,
      defaultValue: 'processing'
    },
    error_details: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Detailed error information for failed rows'
    },
    column_mapping: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Column mapping used during import'
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Additional notes or summary'
    },
    processing_time_ms: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Processing time in milliseconds'
    },
    ip_address: {
      type: DataTypes.STRING(45),
      allowNull: true,
      comment: 'IP address of user who initiated import'
    }
  }, {
    tableName: 'import_logs',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['file_type']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  ImportLog.associate = function(models) {
    ImportLog.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
  };

  // Instance methods
  ImportLog.prototype.getSuccessRate = function() {
    return this.row_count > 0 ? (this.inserted_count / this.row_count) * 100 : 0;
  };

  ImportLog.prototype.hasErrors = function() {
    return this.error_count > 0;
  };

  ImportLog.prototype.isCompleted = function() {
    return ['completed', 'failed', 'partial'].includes(this.status);
  };

  return ImportLog;
};
