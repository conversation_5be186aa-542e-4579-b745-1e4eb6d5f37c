const express = require("express");
const router = express.Router();

// Import controllers and middleware
const transactionController = require("../controllers/transactionController");
const { authenticateToken } = require("../middleware/auth");
const {
  validateCreateTransaction,
  validateUpdateTransaction,
  validateGetTransactions,
  validateDeleteTransaction,
  validateBulkDelete,
} = require("../validators/transactionValidators");

// Apply authentication middleware to all routes
router.use(authenticateToken);

// @route   GET /api/transactions
// @desc    Get all transactions for authenticated user
// @access  Private
router.get("/", validateGetTransactions, transactionController.getTransactions);

// @route   GET /api/transactions/:id
// @desc    Get single transaction
// @access  Private
router.get(
  "/:id",
  validateDeleteTransaction,
  transactionController.getTransaction
);

// @route   POST /api/transactions
// @desc    Create new transaction
// @access  Private
router.post(
  "/",
  validateCreateTransaction,
  transactionController.createTransaction
);

// @route   PUT /api/transactions/:id
// @desc    Update transaction
// @access  Private
router.put(
  "/:id",
  validateUpdateTransaction,
  transactionController.updateTransaction
);

// @route   DELETE /api/transactions/bulk
// @desc    Bulk delete transactions
// @access  Private
router.delete(
  "/bulk",
  validateBulkDelete,
  transactionController.bulkDeleteTransactions
);

// @route   DELETE /api/transactions/:id
// @desc    Delete transaction
// @access  Private
router.delete(
  "/:id",
  validateDeleteTransaction,
  transactionController.deleteTransaction
);

module.exports = router;
