import { useState, useEffect, useRef } from 'react';
import { Edit } from 'lucide-react'; // Keep your pencil icon

function CategoryDropdown({ transaction, categories, onChange, onClose }) {
  const [open, setOpen] = useState(true);
  const [highlightedIndex, setHighlightedIndex] = useState(0);
  const dropdownRef = useRef(null);


    useEffect(() => {
    function onClickOutside(e) {
        if (dropdownRef.current && !dropdownRef.current.contains(e.target)) {
        onClose();
        }
    }
    function onKeyDown(e) {
        if (e.key === 'Escape') onClose();
        if (e.key === 'ArrowDown') {
        e.preventDefault();
        setHighlightedIndex(i => (i + 1) > categories.length - 1 ? -1 : i + 1);
        }
        if (e.key === 'ArrowUp') {
        e.preventDefault();
        setHighlightedIndex(i => (i - 1) < -1 ? categories.length - 1 : i - 1);
        }
        if (e.key === 'Enter') {
        e.preventDefault();
        if (highlightedIndex === -1) {
            onChange(null);
        } else {
            onChange(categories[highlightedIndex].id);
        }
        onClose();
        }
    }
    document.addEventListener('mousedown', onClickOutside);
    document.addEventListener('keydown', onKeyDown);
    return () => {
        document.removeEventListener('mousedown', onClickOutside);
        document.removeEventListener('keydown', onKeyDown);
    };
    }, [highlightedIndex, categories, onChange, onClose]);

  return (
    <div
      ref={dropdownRef}
      className="absolute z-10 mt-1 w-full rounded-md bg-white dark:bg-[#2A2B3D] shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
    >
      <ul
        tabIndex={-1}
        role="listbox"
        aria-activedescendant={`category-option-${categories[highlightedIndex]?.id}`}
        className="max-h-60 overflow-auto text-sm"
        >
        <li
            key="uncategorized"
            id="category-option-uncategorized"
            role="option"
            className={`cursor-pointer px-3 py-2 rounded ${
            highlightedIndex === -1 ? 'bg-blue-500 text-white' : 'hover:bg-gray-200 dark:hover:bg-gray-700'
            }`}
            onClick={() => {
            onChange(null);
            onClose();
            }}
            onMouseEnter={() => setHighlightedIndex(-1)}
        >
            Uncategorized
        </li>
        {categories.map((cat, index) => (
            <li
            key={cat.id}
            id={`category-option-${cat.id}`}
            role="option"
            className={`flex items-center gap-2 cursor-pointer px-3 py-2 rounded ${
                highlightedIndex === index ? 'bg-blue-500 text-white' : 'hover:bg-gray-200 dark:hover:bg-gray-700'
            }`}
            onClick={() => {
                onChange(cat.id);
                onClose();
            }}
            onMouseEnter={() => setHighlightedIndex(index)}
            >
            <span
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: cat.color }}
                aria-hidden="true"
            />
            {cat.name}
            </li>
        ))}
        </ul>
    </div>
  );
}

export default CategoryDropdown;