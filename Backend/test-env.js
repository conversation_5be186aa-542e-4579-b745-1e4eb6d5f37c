require('dotenv').config();

console.log('Environment Variables Test:');
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PORT:', process.env.DB_PORT);
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_PASSWORD:', process.env.DB_PASSWORD);
console.log('DB_NAME:', process.env.DB_NAME);
console.log('DB_DIALECT:', process.env.DB_DIALECT);
console.log('NODE_ENV:', process.env.NODE_ENV);

// Test database config loading
const config = require('./config/database.js');
console.log('\nDatabase Config Test:');
console.log('Development config:', JSON.stringify(config.development, null, 2));
