/**
 * Bot Controller for Finwise Telegram Bot
 * Handles bot management and administration endpoints
 */

const express = require('express');
const router = express.Router();
const { logger, logBotEvent } = require('../utils/logger');

// Get bot status
router.get('/status', async (req, res) => {
  try {
    // This would typically get bot status from the service
    const status = {
      status: 'running',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      botActive: true // This would come from the actual bot service
    };

    res.json(status);
  } catch (error) {
    logger.error('Error getting bot status', { error: error.message });
    res.status(500).json({
      error: 'Failed to get bot status',
      message: error.message
    });
  }
});

// Get bot information
router.get('/info', async (req, res) => {
  try {
    // This would typically get bot info from Telegram API
    const info = {
      name: 'Finwise Bot',
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      features: [
        'Financial tracking',
        'Budget management',
        'Goal setting',
        'Expense analysis'
      ]
    };

    res.json(info);
  } catch (error) {
    logger.error('Error getting bot info', { error: error.message });
    res.status(500).json({
      error: 'Failed to get bot info',
      message: error.message
    });
  }
});

// Restart bot (admin endpoint)
router.post('/restart', async (req, res) => {
  try {
    logBotEvent('Bot restart requested via API');
    
    // This would typically restart the bot service
    res.json({
      message: 'Bot restart initiated',
      timestamp: new Date().toISOString()
    });

    // Note: In a real implementation, you'd restart the bot service here
    
  } catch (error) {
    logger.error('Error restarting bot', { error: error.message });
    res.status(500).json({
      error: 'Failed to restart bot',
      message: error.message
    });
  }
});

// Get bot metrics
router.get('/metrics', async (req, res) => {
  try {
    const metrics = {
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      timestamp: new Date().toISOString(),
      // Add more bot-specific metrics here
      activeUsers: 0, // Would come from actual tracking
      messagesProcessed: 0, // Would come from actual tracking
      commandsExecuted: 0 // Would come from actual tracking
    };

    res.json(metrics);
  } catch (error) {
    logger.error('Error getting bot metrics', { error: error.message });
    res.status(500).json({
      error: 'Failed to get bot metrics',
      message: error.message
    });
  }
});

// Health check for the bot
router.get('/health', async (req, res) => {
  try {
    const health = {
      status: 'healthy',
      checks: {
        bot: 'healthy',
        memory: process.memoryUsage().heapUsed < 500 * 1024 * 1024 ? 'healthy' : 'warning',
        uptime: process.uptime() > 0 ? 'healthy' : 'unhealthy'
      },
      timestamp: new Date().toISOString()
    };

    const isHealthy = Object.values(health.checks).every(status => status === 'healthy');
    const httpStatus = isHealthy ? 200 : 503;

    res.status(httpStatus).json(health);
  } catch (error) {
    logger.error('Error checking bot health', { error: error.message });
    res.status(500).json({
      error: 'Failed to check bot health',
      message: error.message
    });
  }
});

// Get bot configuration (non-sensitive)
router.get('/config', async (req, res) => {
  try {
    const config = {
      environment: process.env.NODE_ENV || 'development',
      port: process.env.BOT_PORT || 3002,
      logLevel: process.env.LOG_LEVEL || 'info',
      features: {
        webhooks: !!process.env.TELEGRAM_WEBHOOK_URL,
        rateLimit: true,
        healthChecks: true
      }
    };

    res.json(config);
  } catch (error) {
    logger.error('Error getting bot config', { error: error.message });
    res.status(500).json({
      error: 'Failed to get bot config',
      message: error.message
    });
  }
});

module.exports = router;
