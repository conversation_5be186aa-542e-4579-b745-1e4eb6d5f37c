require("dotenv").config();
const express = require("express");
const cors = require("cors");
const helmet = require("helmet");
const morgan = require("morgan");
const compression = require("compression");
const rateLimit = require("express-rate-limit");
const slowDown = require("express-slow-down");
const session = require("express-session");
const SequelizeStore = require("connect-session-sequelize")(session.Store);
const path = require("path");

// Import passport configuration
const passport = require("./config/passport");

// Import database
const { sequelize } = require("./models");

// Import routes
const authRoutes = require("./routes/auth");
const userRoutes = require("./routes/users");
const transactionRoutes = require("./routes/transactions");
const categoryRoutes = require("./routes/categories");
const budgetRoutes = require("./routes/budgets");
const goalRoutes = require("./routes/goals");
const ocrRoutes = require("./routes/ocr");
const importRoutes = require("./routes/import");
const exportRoutes = require("./routes/export");
const dashboardRoutes = require("./routes/dashboard");
const otpRoutes = require("./routes/otpRoutes");
const facebookAuthRoutes = require("./routes/facebookAuthRoutes");
const billingRoutes = require("./routes/billing");
const feedbackRoutes = require("./routes/feedback");
const notificationRoutes = require("./routes/notifications");

// Import middleware
const errorHandler = require("./middleware/errorHandler");
const notFound = require("./middleware/notFound");

// Import services
const SubscriptionExpirationService = require("./services/subscriptionExpirationService");


const app = express();
const PORT = process.env.PORT || 5002;

// Trust proxy for rate limiting behind reverse proxy
app.set("trust proxy", 1);

// Security middleware
app.use(
  helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" },
  })
);

// Compression middleware
app.use(compression());

// Build allowed origins list dynamically.
function buildAllowedOrigins() {
  const list = new Set();
  const commaList = process.env.ALLOWED_ORIGINS || ""; // Optional comma-separated list
  commaList
    .split(/[,\n]/)
    .map((s) => s.trim())
    .filter(Boolean)
    .forEach((o) => list.add(o));

  // Explicit single vars
  [process.env.FRONTEND_URL, process.env.CORS_ORIGIN]
    .filter(Boolean)
    .forEach((o) => list.add(o));

  // Local dev defaults
  [
    "http://localhost:5173",
    "http://localhost:5174",
    "http://localhost:5175",
    "http://localhost:3000",
  ].forEach((o) => list.add(o));

  // Production Netlify
  list.add("https://fin-wise.netlify.app");

  return Array.from(list);
}

let allowedOrigins = buildAllowedOrigins();

// Hot-reload origins periodically (in case env vars injected differently, e.g., platform reload)
setInterval(() => {
  allowedOrigins = buildAllowedOrigins();
}, 5 * 60 * 1000); // every 5 minutes

// Netlify deploy previews pattern: https://deploy-preview-123--fin-wise.netlify.app
const isNetlifyDeployPreview = (origin) =>
  !!origin && /^https:\/\/deploy-preview-\d+--fin-wise\.netlify\.app$/.test(origin);

app.use(
  cors({
    origin: (origin, callback) => {
      // Allow requests with no origin (like mobile apps or curl requests)
      if (!origin) return callback(null, true);

      if (allowedOrigins.includes(origin) || isNetlifyDeployPreview(origin)) {
        return callback(null, true);
      }

      // In development, be more permissive
      if (process.env.NODE_ENV === "development") {
        return callback(null, true);
      }

  const msg = `CORS blocked. Origin: ${origin}. Allowed: ${allowedOrigins.join(", ")} (plus Netlify deploy previews). Update ALLOWED_ORIGINS / FRONTEND_URL / CORS_ORIGIN env vars if needed.`;
  return callback(new Error(msg), false);
    },
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
  })
);

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  message: {
    error: "Too many requests from this IP, please try again later.",
    retryAfter: Math.ceil(
      (parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000) / 1000
    ),
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Speed limiting for additional protection
const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 50, // allow 50 requests per windowMs without delay
  delayMs: () => 500, // add 500ms delay per request after delayAfter
  validate: { delayMs: false }, // Disable the warning
});

app.use(limiter);
app.use(speedLimiter);

// Logging
if (process.env.NODE_ENV !== "test") {
  app.use(morgan("combined"));
}

// Body parsing middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Session configuration
const sessionStore = new SequelizeStore({
  db: sequelize,
  tableName: "sessions",
  checkExpirationInterval: 15 * 60 * 1000, // Clean up expired sessions every 15 minutes
  expiration: 24 * 60 * 60 * 1000, // 24 hours
});

app.use(
  session({
    secret: process.env.SESSION_SECRET,
    store: sessionStore,
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: process.env.NODE_ENV === "production",
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
    },
  })
);

// Initialize Passport
app.use(passport.initialize());
app.use(passport.session());

// Static files
app.use("/uploads", express.static(path.join(__dirname, "uploads")));
app.use("/public", express.static(path.join(__dirname, "public")));
app.use("/qr", express.static(path.join(__dirname, "temp")));

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "OK",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
  });
});

// API Health check endpoint
app.get("/api/health", (req, res) => {
  res.status(200).json({
    status: "OK",
    message: "Finwise API is running",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: "1.0.0",
  });
});

// API routes
app.use("/api/auth", authRoutes);
app.use("/api/users", userRoutes);
app.use("/api/transactions", transactionRoutes);
app.use("/api/categories", categoryRoutes);
app.use("/api/budgets", budgetRoutes);
app.use("/api/goals", goalRoutes);
app.use("/api/ocr", ocrRoutes);
app.use("/api/import", importRoutes);
app.use("/api/export", exportRoutes);
app.use("/api/dashboard", dashboardRoutes);
app.use("/api/otp", otpRoutes);
app.use("/api/auth", facebookAuthRoutes);
app.use("/api/billing", billingRoutes);
app.use("/api/feedback", feedbackRoutes);
app.use("/api/notifications", notificationRoutes);



// Error handling middleware
app.use(notFound);
app.use(errorHandler);

// Seed default categories function
async function seedDefaultCategories() {
  try {
    const { Category } = require("./models");
    const count = await Category.count();

    if (count === 0) {
      const defaultCategories = [
        {
          name: "Food & Dining",
          name_km: "អាហារ និង ភោជនីយដ្ឋាន",
          type: "expense",
          color: "#FF6B6B",
          icon: "🍽️",
        },
        {
          name: "Transportation",
          name_km: "ការដឹកជញ្ជូន",
          type: "expense",
          color: "#4ECDC4",
          icon: "🚗",
        },
        {
          name: "Shopping",
          name_km: "ការទិញទំនិញ",
          type: "expense",
          color: "#45B7D1",
          icon: "🛍️",
        },
        {
          name: "Entertainment",
          name_km: "កម្សាន្ត",
          type: "expense",
          color: "#96CEB4",
          icon: "🎬",
        },
        {
          name: "Bills & Utilities",
          name_km: "វិក្កយបត្រ និង សេវាកម្ម",
          type: "expense",
          color: "#FFEAA7",
          icon: "💡",
        },
        {
          name: "Healthcare",
          name_km: "សុខភាព",
          type: "expense",
          color: "#DDA0DD",
          icon: "🏥",
        },
        {
          name: "Education",
          name_km: "ការអប់រំ",
          type: "expense",
          color: "#98D8C8",
          icon: "📚",
        },
        {
          name: "Travel",
          name_km: "ការធ្វើដំណើរ",
          type: "expense",
          color: "#F7DC6F",
          icon: "✈️",
        },
        {
          name: "Personal Care",
          name_km: "ការថែទាំខ្លួន",
          type: "expense",
          color: "#BB8FCE",
          icon: "💄",
        },
        {
          name: "Other Expenses",
          name_km: "ការចំណាយផ្សេងៗ",
          type: "expense",
          color: "#85C1E9",
          icon: "📦",
        },
        {
          name: "Salary",
          name_km: "ប្រាក់ខែ",
          type: "income",
          color: "#58D68D",
          icon: "💰",
        },
        {
          name: "Other Income",
          name_km: "ចំណូលផ្សេងៗ",
          type: "income",
          color: "#52C41A",
          icon: "💵",
        },
      ];

      await Category.bulkCreate(defaultCategories);
      console.log("✅ Default categories seeded successfully");
    }
  } catch (error) {
    console.error("❌ Failed to seed categories:", error.message);
  }
}

// Database connection and server startup
async function startServer() {
  try {
    // Test database connection
    await sequelize.authenticate();
    console.log("✅ Database connection established successfully.");

    // Sync database (create tables if they don't exist)
    if (process.env.NODE_ENV !== "production") {
      try {
        await sequelize.sync();
        console.log("✅ Database synchronized successfully.");
      } catch (alterError) {
        console.log("⚠️ Alter sync failed, attempting force sync...");
        console.log("Error details:", alterError.message);

        // Force sync to recreate all tables
        await sequelize.sync();
        console.log("✅ Database force synchronized successfully.");

        // Seed default categories after force sync
        await seedDefaultCategories();
      }
    } else {
      await sequelize.sync();
      console.log("✅ Database synchronized successfully.");
    }

    // Create session table
    await sessionStore.sync();

    // Initialize subscription expiration service
    const subscriptionService = new SubscriptionExpirationService();
    subscriptionService.start();



    // Start server
    const server = app.listen(PORT, () => {
      console.log(`🚀 Finwise Backend Server running on port ${PORT}`);
      console.log(`📊 Environment: ${process.env.NODE_ENV}`);
  console.log(`🔗 Primary CORS Origin env (CORS_ORIGIN): ${process.env.CORS_ORIGIN}`);
  console.log(`🔗 FRONTEND_URL env: ${process.env.FRONTEND_URL}`);
  console.log(`🔐 Computed allowed origins: ${allowedOrigins.join(", ")}`);
    });

    // Graceful shutdown
    process.on("SIGTERM", () => {
      console.log("SIGTERM received, shutting down gracefully");
      subscriptionService.stop();
      server.close(() => {
        console.log("Process terminated");
        sequelize.close();
      });
    });
  } catch (error) {
    console.error("❌ Unable to start server:", error);
    process.exit(1);
  }
}

// Start the server
if (require.main === module) {
  startServer();
}

module.exports = app;
