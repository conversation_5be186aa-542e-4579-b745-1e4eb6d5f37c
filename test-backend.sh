#!/bin/bash

# Test script to verify the complete Finwise deployment
echo "🧪 Testing Complete Finwise Deployment..."
echo "Frontend URL: https://spiffy-madeleine-3ff7c6.netlify.app"
echo "Backend URL: https://seal-app-mvbaj.ondigitalocean.app"
echo ""

# Test backend health endpoint
echo "1. Testing backend health endpoint..."
curl -s -w "Status Code: %{http_code}\n" https://seal-app-mvbaj.ondigitalocean.app/health
echo ""

# Test API health endpoint
echo "2. Testing API health endpoint..."
curl -s -w "Status Code: %{http_code}\n" https://seal-app-mvbaj.ondigitalocean.app/api/health
echo ""

# Test CORS with frontend origin
echo "3. Testing CORS from frontend..."
curl -s -I -X OPTIONS \
  -H "Origin: https://spiffy-madeleine-3ff7c6.netlify.app" \
  -H "Access-Control-Request-Method: GET" \
  https://seal-app-mvbaj.ondigitalocean.app/api/health
echo ""

# Test frontend accessibility
echo "4. Testing frontend accessibility..."
curl -s -w "Status Code: %{http_code}\n" -o /dev/null https://spiffy-madeleine-3ff7c6.netlify.app
echo ""

echo "✅ Deployment test complete!"
echo ""
echo "🔗 Your URLs:"
echo "Frontend: https://spiffy-madeleine-3ff7c6.netlify.app"
echo "Backend: https://seal-app-mvbaj.ondigitalocean.app"
echo "API Health: https://seal-app-mvbaj.ondigitalocean.app/api/health"
