'use strict';

module.exports = (sequelize, DataTypes) => {
  const Budget = sequelize.define('Budget', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    category_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'categories',
        key: 'id'
      },
      comment: 'Null for overall budget'
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 100]
      }
    },
    name_km: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: 'Khmer translation of budget name'
    },
    amount: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    currency: {
      type: DataTypes.STRING(3),
      allowNull: false,
      defaultValue: 'USD',
      validate: {
        len: [3, 3],
        isUppercase: true
      }
    },
    period_type: {
      type: DataTypes.ENUM('daily', 'weekly', 'monthly', 'quarterly', 'yearly'),
      allowNull: false,
      defaultValue: 'monthly'
    },
    period_start: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      validate: {
        isDate: true
      }
    },
    period_end: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      validate: {
        isDate: true
      }
    },
    spent_amount: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      defaultValue: 0.00,
      validate: {
        min: 0
      }
    },
    warning_threshold: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
      defaultValue: 80.00,
      validate: {
        min: 0,
        max: 100
      },
      comment: 'Warning when spent percentage reaches this threshold'
    },
    auto_renew: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: 'Automatically create next period budget'
    },
    carry_over: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Carry over unused budget to next period'
    },
    carry_over_amount: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
      defaultValue: 0.00,
      comment: 'Amount carried over from previous period'
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    alert_settings: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Notification preferences for this budget'
    }
  }, {
    tableName: 'budgets',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['category_id']
      },
      {
        fields: ['period_type']
      },
      {
        fields: ['period_start', 'period_end']
      },
      {
        fields: ['is_active']
      },
      {
        unique: true,
        fields: ['user_id', 'category_id', 'period_start'],
        name: 'unique_user_category_period'
      }
    ],
    validate: {
      periodEndAfterStart() {
        if (this.period_end <= this.period_start) {
          throw new Error('Period end must be after period start');
        }
      }
    }
  });

  Budget.associate = function(models) {
    Budget.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
    Budget.belongsTo(models.Category, {
      foreignKey: 'category_id',
      as: 'category'
    });
  };

  // Instance methods
  Budget.prototype.getSpentPercentage = function() {
    return this.amount > 0 ? (this.spent_amount / this.amount) * 100 : 0;
  };

  Budget.prototype.getRemainingAmount = function() {
    return Math.max(0, this.amount - this.spent_amount);
  };

  Budget.prototype.isOverBudget = function() {
    return this.spent_amount > this.amount;
  };

  Budget.prototype.shouldWarn = function() {
    return this.getSpentPercentage() >= this.warning_threshold;
  };

  return Budget;
};
