import { create } from 'zustand';
import { goalAPI } from '../services/api';
import toast from 'react-hot-toast';

const useGoalStore = create((set, get) => ({
  // State
  goals: [],
  isLoading: false,
  error: null,
  currentGoal: null,

  // Actions
  fetchGoals: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await goalAPI.getGoals();
      const goals = response.data.data || [];

      set({
        goals,
        isLoading: false,
        error: null
      });

      return { success: true, data: goals };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to fetch goals';
      
      set({
        goals: [],
        isLoading: false,
        error: errorMessage
      });

      return { success: false, error: errorMessage };
    }
  },

  createGoal: async (goalData) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await goalAPI.createGoal(goalData);
      const newGoal = response.data.data;

      set(state => ({
        goals: [...state.goals, newGoal],
        isLoading: false,
        error: null
      }));

      toast.success('Goal created successfully!');
      return { success: true, data: newGoal };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to create goal';
      
      set({
        isLoading: false,
        error: errorMessage
      });

      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  updateGoal: async (goalId, goalData) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await goalAPI.updateGoal(goalId, goalData);
      const updatedGoal = response.data.data;

      set(state => ({
        goals: state.goals.map(goal => 
          goal.id === goalId ? updatedGoal : goal
        ),
        isLoading: false,
        error: null,
        currentGoal: state.currentGoal?.id === goalId ? updatedGoal : state.currentGoal
      }));

      toast.success('Goal updated successfully!');
      return { success: true, data: updatedGoal };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to update goal';
      
      set({
        isLoading: false,
        error: errorMessage
      });

      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  deleteGoal: async (goalId) => {
    set({ isLoading: true, error: null });
    
    try {
      await goalAPI.deleteGoal(goalId);

      set(state => ({
        goals: state.goals.filter(goal => goal.id !== goalId),
        isLoading: false,
        error: null,
        currentGoal: state.currentGoal?.id === goalId ? null : state.currentGoal
      }));

      toast.success('Goal deleted successfully!');
      return { success: true };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to delete goal';
      
      set({
        isLoading: false,
        error: errorMessage
      });

      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  getGoal: async (goalId) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await goalAPI.getGoal(goalId);
      const goal = response.data.data;

      set({
        currentGoal: goal,
        isLoading: false,
        error: null
      });

      return { success: true, data: goal };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to fetch goal';
      
      set({
        currentGoal: null,
        isLoading: false,
        error: errorMessage
      });

      return { success: false, error: errorMessage };
    }
  },

  updateGoalProgress: async (goalId, amount) => {
    try {
      const response = await goalAPI.updateGoalProgress(goalId, { amount });
      const updatedGoal = response.data.data;

      set(state => ({
        goals: state.goals.map(goal => 
          goal.id === goalId ? updatedGoal : goal
        ),
        currentGoal: state.currentGoal?.id === goalId ? updatedGoal : state.currentGoal
      }));

      toast.success('Goal progress updated!');
      return { success: true, data: updatedGoal };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to update goal progress';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  // Utility actions
  setCurrentGoal: (goal) => {
    set({ currentGoal: goal });
  },

  clearCurrentGoal: () => {
    set({ currentGoal: null });
  },

  clearError: () => {
    set({ error: null });
  },

  // Computed values
  getGoalProgress: (goal) => {
    if (!goal || !goal.target_amount) return 0;
    return Math.min((goal.current_amount / goal.target_amount) * 100, 100);
  },

  getGoalStatus: (goal) => {
    if (!goal) return 'unknown';
    
    const progress = get().getGoalProgress(goal);
    const now = new Date();
    const targetDate = new Date(goal.target_date);
    
    if (progress >= 100) return 'completed';
    if (now > targetDate) return 'overdue';
    if (progress >= 75) return 'on-track';
    if (progress >= 25) return 'behind';
    return 'just-started';
  }
}));

export default useGoalStore;
