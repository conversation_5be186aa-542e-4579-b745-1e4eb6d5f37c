require("dotenv").config();
const { DocumentProcessorServiceClient } = require("@google-cloud/documentai");
const path = require("path");
const fs = require("fs");

console.log("🔍 DEBUGGING DOCUMENT AI CONFIGURATION");
console.log("=====================================");

// Check environment variables
console.log("\n📋 Environment Variables:");
console.log(
  "GOOGLE_APPLICATION_CREDENTIALS:",
  process.env.GOOGLE_APPLICATION_CREDENTIALS
);
console.log("DOCUMENT_AI_PROJECT_ID:", process.env.DOCUMENT_AI_PROJECT_ID);
console.log("DOCUMENT_AI_LOCATION:", process.env.DOCUMENT_AI_LOCATION);
console.log("DOCUMENT_AI_PROCESSOR_ID:", process.env.DOCUMENT_AI_PROCESSOR_ID);

// Check file existence
const credentialsPath =
  process.env.GOOGLE_APPLICATION_CREDENTIALS ||
  path.join(__dirname, "finwise-471010-0dfebced9164.json");

console.log("\n📁 File System Check:");
console.log("Checking credentials file:", credentialsPath);

if (fs.existsSync(credentialsPath)) {
  console.log("✅ Credentials file exists");
  try {
    const credentials = JSON.parse(fs.readFileSync(credentialsPath, "utf8"));
    console.log("✅ Credentials file is valid JSON");
    console.log("Project ID in file:", credentials.project_id);
    console.log("Client email:", credentials.client_email);
  } catch (error) {
    console.log("❌ Error reading credentials file:", error.message);
  }
} else {
  console.log("❌ Credentials file not found");

  // List available JSON files
  console.log("\nAvailable JSON files in Backend directory:");
  try {
    const files = fs
      .readdirSync(__dirname)
      .filter((file) => file.endsWith(".json"));
    files.forEach((file) => console.log("-", file));
  } catch (error) {
    console.log("Error listing files:", error.message);
  }
}

// Test Document AI client initialization
console.log("\n🤖 Document AI Client Test:");
try {
  // Force use of the specific file
  const explicitPath = path.join(__dirname, "finwise-471010-0dfebced9164.json");
  console.log("Explicit path:", explicitPath);

  const client = new DocumentProcessorServiceClient({
    keyFilename: explicitPath,
    projectId: "finwise-471010",
  });

  console.log("✅ Document AI client initialized successfully");

  // Test the processor path
  const processorPath = client.processorPath(
    "finwise-471010",
    "eu",
    "a895696dbaeb663f"
  );
  console.log("✅ Processor path generated:", processorPath);
} catch (error) {
  console.log("❌ Document AI client initialization failed:", error.message);
  console.log("Error details:", error);
}

console.log("\n🏁 Debug complete!");
