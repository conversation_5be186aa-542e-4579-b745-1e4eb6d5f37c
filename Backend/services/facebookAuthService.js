const passport = require("passport");
const FacebookTokenStrategy = require("passport-facebook-token");
const { User } = require("../models");
const jwt = require("jsonwebtoken");

class FacebookAuthService {
  constructor() {
    this.initializeStrategy();
  }

  initializeStrategy() {
    passport.use(
      new FacebookTokenStrategy(
        {
          clientID: process.env.FACEBOOK_APP_ID || "629097753590174",
          clientSecret:
            process.env.FACEBOOK_APP_SECRET ||
            "********************************",
          fbGraphVersion: "v18.0",
        },
        async (accessToken, refreshToken, profile, done) => {
          try {
            const result = await this.handleFacebookAuth(profile, accessToken);
            return done(null, result);
          } catch (error) {
            return done(error, null);
          }
        }
      )
    );

    console.log("✅ Facebook authentication strategy initialized");
  }

  async handleFacebookAuth(profile, accessToken) {
    try {
      const facebookId = profile.id;
      const email =
        profile.emails && profile.emails[0] ? profile.emails[0].value : null;
      const name =
        profile.displayName ||
        `${profile.name.givenName} ${profile.name.familyName}`;
      const profilePicture =
        profile.photos && profile.photos[0] ? profile.photos[0].value : null;

      // Check if user already exists with this Facebook ID
      let user = await User.findOne({
        where: {
          provider: "facebook",
          provider_id: facebookId,
        },
      });

      if (user) {
        // Update last login
        await user.update({
          last_login: new Date(),
          profile_picture: profilePicture, // Update profile picture if changed
        });

        console.log(`✅ Existing Facebook user logged in: ${user.email}`);
        return {
          user,
          isNewUser: false,
        };
      }

      // Check if user exists with the same email but different provider
      if (email) {
        const existingUser = await User.findOne({
          where: { email },
        });

        if (existingUser) {
          // Link Facebook account to existing user
          await existingUser.update({
            provider: "facebook",
            provider_id: facebookId,
            profile_picture: profilePicture,
            last_login: new Date(),
          });

          console.log(`✅ Facebook account linked to existing user: ${email}`);
          return {
            user: existingUser,
            isNewUser: false,
            linked: true,
          };
        }
      }

      // Create new user
      const newUser = await User.create({
        name,
        email: email || `facebook_${facebookId}@finwise.app`, // Fallback email if not provided
        provider: "facebook",
        provider_id: facebookId,
        email_verified: email ? true : false, // Facebook emails are considered verified
        profile_picture: profilePicture,
        is_active: true,
        last_login: new Date(),
      });

      console.log(`✅ New Facebook user created: ${newUser.email}`);
      return {
        user: newUser,
        isNewUser: true,
      };
    } catch (error) {
      console.error("❌ Facebook authentication error:", error);
      throw new Error("Failed to authenticate with Facebook");
    }
  }

  async verifyFacebookToken(accessToken) {
    try {
      // Verify the token with Facebook Graph API
      const response = await fetch(
        `https://graph.facebook.com/me?access_token=${accessToken}&fields=id,name,email,picture`
      );

      if (!response.ok) {
        throw new Error("Invalid Facebook access token");
      }

      const profile = await response.json();

      if (profile.error) {
        throw new Error(profile.error.message);
      }

      return profile;
    } catch (error) {
      console.error("❌ Facebook token verification failed:", error);
      throw new Error("Invalid Facebook access token");
    }
  }

  async authenticateWithToken(accessToken) {
    try {
      // Verify token with Facebook
      const facebookProfile = await this.verifyFacebookToken(accessToken);

      // Transform Facebook profile to match passport format
      const profile = {
        id: facebookProfile.id,
        displayName: facebookProfile.name,
        name: {
          givenName: facebookProfile.name.split(" ")[0],
          familyName: facebookProfile.name.split(" ").slice(1).join(" "),
        },
        emails: facebookProfile.email ? [{ value: facebookProfile.email }] : [],
        photos: facebookProfile.picture
          ? [{ value: facebookProfile.picture.data.url }]
          : [],
      };

      // Handle authentication
      const result = await this.handleFacebookAuth(profile, accessToken);

      // Generate JWT token
      const token = jwt.sign(
        {
          userId: result.user.id,
          email: result.user.email,
          provider: "facebook",
        },
        process.env.JWT_SECRET,
        { expiresIn: "7d" }
      );

      return {
        success: true,
        token,
        user: {
          id: result.user.id,
          name: result.user.name,
          email: result.user.email,
          profilePicture: result.user.profile_picture,
          provider: result.user.provider,
          isNewUser: result.isNewUser,
          linked: result.linked || false,
        },
      };
    } catch (error) {
      console.error("❌ Facebook authentication with token failed:", error);
      throw new Error(`Facebook authentication failed: ${error.message}`);
    }
  }

  async unlinkFacebookAccount(userId) {
    try {
      const user = await User.findByPk(userId);

      if (!user) {
        throw new Error("User not found");
      }

      if (user.provider !== "facebook") {
        throw new Error("User is not linked with Facebook");
      }

      // Reset provider fields
      await user.update({
        provider: "local",
        provider_id: null,
      });

      console.log(`✅ Facebook account unlinked for user: ${user.email}`);
      return true;
    } catch (error) {
      console.error("❌ Failed to unlink Facebook account:", error);
      throw new Error(`Failed to unlink Facebook account: ${error.message}`);
    }
  }

  // Method to get Facebook user info (for profile sync)
  async getFacebookUserInfo(accessToken) {
    try {
      const response = await fetch(
        `https://graph.facebook.com/me?access_token=${accessToken}&fields=id,name,email,picture.type(large),birthday,location`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch Facebook user info");
      }

      const userInfo = await response.json();

      if (userInfo.error) {
        throw new Error(userInfo.error.message);
      }

      return userInfo;
    } catch (error) {
      console.error("❌ Failed to get Facebook user info:", error);
      throw new Error(`Failed to get Facebook user info: ${error.message}`);
    }
  }
}

module.exports = new FacebookAuthService();
