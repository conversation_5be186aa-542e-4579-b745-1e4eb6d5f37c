require('dotenv').config();
const axios = require('axios');

class FacebookAppTester {
  constructor() {
    this.appId = process.env.FACEBOOK_APP_ID;
    this.appSecret = process.env.FACEBOOK_APP_SECRET;
    this.graphApiVersion = 'v18.0';
    this.testResults = {
      appCredentials: null,
      appAccessToken: null,
      appInfo: null,
      permissions: null,
      testUserCreation: null,
      tokenValidation: null,
      overall: 'pending'
    };
  }

  async runAllTests() {
    console.log('🧪 FACEBOOK APP COMPREHENSIVE TESTING');
    console.log('=====================================');
    console.log(`📱 App ID: ${this.appId}`);
    console.log(`🔐 App Secret: ${this.appSecret ? '***' + this.appSecret.slice(-4) : 'NOT SET'}`);
    console.log('');

    try {
      // Test 1: Basic credentials validation
      await this.testCredentials();
      
      // Test 2: Get app access token
      await this.testAppAccessToken();
      
      // Test 3: Get app information
      await this.testAppInfo();
      
      // Test 4: Test permissions and features
      await this.testPermissions();
      
      // Test 5: Create test user (if possible)
      await this.testUserCreation();
      
      // Test 6: Test token validation endpoint
      await this.testTokenValidation();
      
      // Generate final report
      this.generateFinalReport();
      
      return this.testResults;
    } catch (error) {
      console.error('❌ Testing failed:', error.message);
      this.testResults.overall = 'failed';
      return this.testResults;
    }
  }

  async testCredentials() {
    console.log('1️⃣ Testing Basic Credentials...');
    
    if (!this.appId || !this.appSecret) {
      this.testResults.appCredentials = {
        status: 'error',
        message: 'App ID or App Secret not configured'
      };
      console.log('❌ App ID or App Secret missing');
      return;
    }

    if (this.appId.length < 10 || this.appSecret.length < 20) {
      this.testResults.appCredentials = {
        status: 'error',
        message: 'App ID or App Secret appears to be invalid (too short)'
      };
      console.log('❌ App credentials appear to be invalid');
      return;
    }

    this.testResults.appCredentials = {
      status: 'success',
      message: 'App credentials format looks valid'
    };
    console.log('✅ App credentials format looks valid');
    console.log('');
  }

  async testAppAccessToken() {
    console.log('2️⃣ Testing App Access Token...');
    
    try {
      const response = await axios.get(`https://graph.facebook.com/oauth/access_token`, {
        params: {
          client_id: this.appId,
          client_secret: this.appSecret,
          grant_type: 'client_credentials'
        },
        timeout: 10000
      });

      this.appAccessToken = response.data.access_token;
      this.testResults.appAccessToken = {
        status: 'success',
        message: 'App access token obtained successfully',
        token: this.appAccessToken ? '***' + this.appAccessToken.slice(-10) : null
      };
      
      console.log('✅ App access token obtained successfully');
      console.log(`   Token: ***${this.appAccessToken.slice(-10)}`);
      
    } catch (error) {
      this.testResults.appAccessToken = {
        status: 'error',
        message: error.response?.data?.error?.message || error.message,
        errorCode: error.response?.data?.error?.code,
        errorType: error.response?.data?.error?.type
      };
      
      console.log('❌ Failed to get app access token');
      console.log(`   Error: ${error.response?.data?.error?.message || error.message}`);
      
      if (error.response?.data?.error?.code) {
        console.log(`   Error Code: ${error.response.data.error.code}`);
      }
    }
    console.log('');
  }

  async testAppInfo() {
    console.log('3️⃣ Testing App Information...');
    
    if (!this.appAccessToken) {
      console.log('⏭️ Skipping app info test (no access token)');
      console.log('');
      return;
    }

    try {
      const response = await axios.get(`https://graph.facebook.com/${this.graphApiVersion}/${this.appId}`, {
        params: {
          access_token: this.appAccessToken,
          fields: 'name,app_domains,privacy_policy_url,terms_of_service_url,category,company,contact_email'
        },
        timeout: 10000
      });

      this.testResults.appInfo = {
        status: 'success',
        data: response.data
      };
      
      console.log('✅ App information retrieved successfully');
      console.log(`   App Name: ${response.data.name || 'Not set'}`);
      console.log(`   Category: ${response.data.category || 'Not set'}`);
      console.log(`   Company: ${response.data.company || 'Not set'}`);
      console.log(`   App Domains: ${response.data.app_domains?.join(', ') || 'None configured'}`);
      
    } catch (error) {
      this.testResults.appInfo = {
        status: 'error',
        message: error.response?.data?.error?.message || error.message,
        errorCode: error.response?.data?.error?.code
      };
      
      console.log('❌ Failed to get app information');
      console.log(`   Error: ${error.response?.data?.error?.message || error.message}`);
    }
    console.log('');
  }

  async testPermissions() {
    console.log('4️⃣ Testing App Permissions...');
    
    if (!this.appAccessToken) {
      console.log('⏭️ Skipping permissions test (no access token)');
      console.log('');
      return;
    }

    try {
      const response = await axios.get(`https://graph.facebook.com/${this.graphApiVersion}/${this.appId}/permissions`, {
        params: {
          access_token: this.appAccessToken
        },
        timeout: 10000
      });

      this.testResults.permissions = {
        status: 'success',
        data: response.data.data
      };
      
      console.log('✅ App permissions retrieved successfully');
      if (response.data.data && response.data.data.length > 0) {
        response.data.data.forEach(permission => {
          console.log(`   ${permission.permission}: ${permission.status}`);
        });
      } else {
        console.log('   No specific permissions configured');
      }
      
    } catch (error) {
      this.testResults.permissions = {
        status: 'error',
        message: error.response?.data?.error?.message || error.message
      };
      
      console.log('⚠️ Could not retrieve app permissions');
      console.log(`   Error: ${error.response?.data?.error?.message || error.message}`);
    }
    console.log('');
  }

  async testUserCreation() {
    console.log('5️⃣ Testing Test User Creation...');
    
    if (!this.appAccessToken) {
      console.log('⏭️ Skipping test user creation (no access token)');
      console.log('');
      return;
    }

    try {
      const response = await axios.post(`https://graph.facebook.com/${this.graphApiVersion}/${this.appId}/accounts/test-users`, {
        installed: true,
        permissions: 'email,public_profile',
        access_token: this.appAccessToken
      }, {
        timeout: 10000
      });

      this.testResults.testUserCreation = {
        status: 'success',
        data: response.data
      };
      
      console.log('✅ Test user created successfully');
      console.log(`   User ID: ${response.data.id}`);
      console.log(`   Access Token: ***${response.data.access_token.slice(-10)}`);
      
      // Clean up - delete the test user
      try {
        await axios.delete(`https://graph.facebook.com/${this.graphApiVersion}/${response.data.id}`, {
          params: { access_token: this.appAccessToken }
        });
        console.log('   ✅ Test user cleaned up successfully');
      } catch (cleanupError) {
        console.log('   ⚠️ Could not clean up test user');
      }
      
    } catch (error) {
      this.testResults.testUserCreation = {
        status: 'error',
        message: error.response?.data?.error?.message || error.message,
        errorCode: error.response?.data?.error?.code
      };
      
      console.log('❌ Failed to create test user');
      console.log(`   Error: ${error.response?.data?.error?.message || error.message}`);
      
      if (error.response?.status === 403) {
        console.log('   💡 This might be normal if your app is in Live mode');
      }
    }
    console.log('');
  }

  async testTokenValidation() {
    console.log('6️⃣ Testing Token Validation Endpoint...');
    
    if (!this.appAccessToken) {
      console.log('⏭️ Skipping token validation test (no access token)');
      console.log('');
      return;
    }

    try {
      const response = await axios.get(`https://graph.facebook.com/${this.graphApiVersion}/debug_token`, {
        params: {
          input_token: this.appAccessToken,
          access_token: this.appAccessToken
        },
        timeout: 10000
      });

      this.testResults.tokenValidation = {
        status: 'success',
        data: response.data.data
      };
      
      console.log('✅ Token validation endpoint working');
      console.log(`   Token is valid: ${response.data.data.is_valid}`);
      console.log(`   App ID matches: ${response.data.data.app_id === this.appId}`);
      console.log(`   Token type: ${response.data.data.type}`);
      
    } catch (error) {
      this.testResults.tokenValidation = {
        status: 'error',
        message: error.response?.data?.error?.message || error.message
      };
      
      console.log('❌ Token validation failed');
      console.log(`   Error: ${error.response?.data?.error?.message || error.message}`);
    }
    console.log('');
  }

  generateFinalReport() {
    console.log('📊 FINAL TEST REPORT');
    console.log('====================');
    
    const tests = [
      { name: 'App Credentials', result: this.testResults.appCredentials },
      { name: 'App Access Token', result: this.testResults.appAccessToken },
      { name: 'App Information', result: this.testResults.appInfo },
      { name: 'App Permissions', result: this.testResults.permissions },
      { name: 'Test User Creation', result: this.testResults.testUserCreation },
      { name: 'Token Validation', result: this.testResults.tokenValidation }
    ];

    let passedTests = 0;
    let totalTests = 0;

    tests.forEach(test => {
      if (test.result) {
        totalTests++;
        const status = test.result.status === 'success' ? '✅' : '❌';
        console.log(`${status} ${test.name}: ${test.result.status}`);
        if (test.result.status === 'success') passedTests++;
      }
    });

    console.log('');
    console.log(`📈 Test Results: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests >= 3) {
      this.testResults.overall = 'success';
      console.log('🎉 Your Facebook app is working properly!');
      
      if (this.testResults.appInfo?.data) {
        console.log('');
        console.log('📋 CONFIGURATION RECOMMENDATIONS:');
        console.log('1. Make sure your app is in "Live" mode for production');
        console.log('2. Add "localhost" to App Domains for development');
        console.log('3. Add "http://localhost:5173" to Valid OAuth Redirect URIs');
        console.log('4. Configure proper privacy policy and terms of service URLs');
      }
    } else {
      this.testResults.overall = 'issues_detected';
      console.log('⚠️ Some issues detected with your Facebook app configuration');
      console.log('');
      console.log('🔧 TROUBLESHOOTING STEPS:');
      console.log('1. Verify your App ID and App Secret are correct');
      console.log('2. Check if your app is active in Facebook Developers Console');
      console.log('3. Make sure your app has the necessary permissions');
      console.log('4. Check if your app is in the correct mode (Development/Live)');
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new FacebookAppTester();
  tester.runAllTests()
    .then(results => {
      process.exit(results.overall === 'success' ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Testing failed:', error);
      process.exit(1);
    });
}

module.exports = FacebookAppTester;
