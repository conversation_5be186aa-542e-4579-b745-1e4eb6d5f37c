name: Continuous Integration

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  frontend-ci:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: Frontend/package-lock.json
        
    - name: Install frontend dependencies
      run: |
        cd Frontend
        npm ci --legacy-peer-deps
        
    - name: Run frontend linting
      run: |
        cd Frontend
        npm run lint
        
    - name: Run frontend tests
      run: |
        cd Frontend
        npm run test -- --run
        
    - name: Build frontend
      run: |
        cd Frontend
        npm run build
      env:
        VITE_API_URL: /api
        VITE_API_BASE_URL: http://localhost:5003
        VITE_APP_NAME: Finwise
        VITE_NODE_ENV: development
        
  backend-ci:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: testpassword
          MYSQL_DATABASE: finwise_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: Backend/package-lock.json
        
    - name: Install backend dependencies
      run: |
        cd Backend
        npm ci
        
    - name: Run backend tests
      run: |
        cd Backend
        npm test
      env:
        NODE_ENV: test
        DB_HOST: localhost
        DB_PORT: 3306
        DB_USER: root
        DB_PASSWORD: testpassword
        DB_NAME: finwise_test
        JWT_SECRET: test-jwt-secret
        SESSION_SECRET: test-session-secret
        
    - name: Run security audit
      run: |
        cd Backend
        npm audit --audit-level moderate
        
  docker-build:
    runs-on: ubuntu-latest
    needs: [frontend-ci, backend-ci]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Build Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile
        push: false
        tags: finwise-backend:test
        cache-from: type=gha
        cache-to: type=gha,mode=max
        
    - name: Test Docker image
      run: |
        # Create test environment file
        cat > Backend/.env.test << EOF
        NODE_ENV=production
        PORT=5003
        DB_HOST=localhost
        DB_PORT=3306
        DB_USER=test
        DB_PASSWORD=test
        DB_NAME=test
        JWT_SECRET=test-secret
        SESSION_SECRET=test-session
        CORS_ORIGIN=http://localhost:3000
        EOF
        
        # Run container for testing
        docker run -d --name test-container -p 5003:5003 --env-file Backend/.env.test finwise-backend:test
        
        # Wait for container to start
        sleep 10
        
        # Test health endpoint
        if curl -f http://localhost:5003/api/health; then
          echo "✅ Docker image test passed!"
        else
          echo "❌ Docker image test failed!"
          docker logs test-container
          exit 1
        fi
        
        # Cleanup
        docker stop test-container
        docker rm test-container
        
  code-quality:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: |
        cd Backend
        npm ci
        cd ../Frontend
        npm ci --legacy-peer-deps
        
    - name: Run code quality checks
      run: |
        echo "Running code quality checks..."
        
        # Check for TODO/FIXME comments
        if grep -r "TODO\|FIXME" Backend/ Frontend/ --exclude-dir=node_modules --exclude-dir=dist; then
          echo "⚠️ Found TODO/FIXME comments"
        fi
        
        # Check for console.log in production code
        if grep -r "console\.log" Backend/ --exclude-dir=node_modules --exclude="*.test.js"; then
          echo "⚠️ Found console.log statements in backend code"
        fi
        
        # Check for hardcoded secrets (basic check)
        if grep -r "password.*=.*['\"].*['\"]" Backend/ Frontend/ --exclude-dir=node_modules --exclude="*.md" --exclude="*.example" || \
           grep -r "secret.*=.*['\"].*['\"]" Backend/ Frontend/ --exclude-dir=node_modules --exclude="*.md" --exclude="*.example"; then
          echo "⚠️ Potential hardcoded secrets found"
        fi
        
        echo "✅ Code quality checks completed"
