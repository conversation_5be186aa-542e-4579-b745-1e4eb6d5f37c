const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
require('dotenv').config();

const { logger } = require('./utils/logger');
const config = require('./config/config');
const TelegramBotService = require('./services/TelegramBotService');
const healthCheck = require('./utils/healthCheck');
const { createRateLimiter } = require('./utils/rateLimiter');

class BotServer {
  constructor() {
    this.app = express();
    this.bot = null;
    this.server = null;
  }

  async initialize() {
    try {
      logger.info('🤖 Initializing Finwise Telegram Bot Service...');

      // Setup Express middleware
      this.setupMiddleware();
      
      // Setup routes
      this.setupRoutes();
      
      // Initialize bot service
      await this.initializeBotService();
      
      // Setup health checks
      this.setupHealthChecks();
      
      // Setup graceful shutdown
      this.setupGracefulShutdown();
      
      logger.info('✅ Bot service initialized successfully');
    } catch (error) {
      logger.error('❌ Failed to initialize bot service:', error);
      process.exit(1);
    }
  }

  setupMiddleware() {
    // Security middleware
    this.app.use(helmet());
    
    // CORS configuration
    this.app.use(cors({
      origin: config.security.corsOrigins,
      credentials: true
    }));
    
    // Rate limiting
    this.app.use(createRateLimiter());
    
    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
    
    // Request logging
    this.app.use((req, res, next) => {
      logger.info(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      next();
    });
  }

  setupRoutes() {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'finwise-telegram-bot',
        version: process.env.npm_package_version || '1.0.0'
      });
    });

    // Webhook endpoint for Telegram
    this.app.post('/webhook/telegram', async (req, res) => {
      try {
        if (this.bot) {
          await this.bot.handleWebhookUpdate(req.body);
        }
        res.status(200).send('OK');
      } catch (error) {
        logger.error('❌ Webhook error:', error);
        res.status(500).send('Internal Server Error');
      }
    });

    // Bot management endpoints
    this.app.use('/api/bot', require('./controllers/botController'));
    
    // Metrics endpoint
    this.app.get('/metrics', (req, res) => {
      res.json({
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        timestamp: new Date().toISOString()
      });
    });

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: 'The requested endpoint does not exist'
      });
    });

    // Error handler
    this.app.use((error, req, res, next) => {
      logger.error('❌ Unhandled error:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Something went wrong'
      });
    });
  }

  async initializeBotService() {
    try {
      this.bot = new TelegramBotService();
      await this.bot.initialize();
      logger.info('✅ Telegram bot service started');
    } catch (error) {
      logger.error('❌ Failed to initialize bot service:', error);
      throw error;
    }
  }

  setupHealthChecks() {
    // Start health check monitoring
    healthCheck.start(this.bot);
    logger.info('✅ Health check monitoring started');
  }

  setupGracefulShutdown() {
    const shutdown = async (signal) => {
      logger.info(`📴 Received ${signal}, shutting down gracefully...`);
      
      try {
        // Stop accepting new requests
        if (this.server) {
          this.server.close(() => {
            logger.info('✅ HTTP server closed');
          });
        }
        
        // Stop bot
        if (this.bot) {
          await this.bot.stop();
          logger.info('✅ Bot stopped');
        }
        
        // Stop health checks
        healthCheck.stop();
        logger.info('✅ Health checks stopped');
        
        logger.info('✅ Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('❌ Error during shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGUSR2', () => shutdown('SIGUSR2')); // nodemon restart
  }

  async start() {
    try {
      await this.initialize();
      
      const port = config.server.port;
      this.server = this.app.listen(port, () => {
        logger.info(`🚀 Finwise Telegram Bot Service running on port ${port}`);
        logger.info(`📊 Environment: ${config.server.environment}`);
        logger.info(`🔗 Backend API: ${config.api.baseUrl}`);
        
        if (config.server.environment === 'production') {
          logger.info(`🌐 Webhook URL: ${config.telegram.webhookUrl}`);
        } else {
          logger.info('🔄 Running in polling mode for development');
        }
      });
      
      // Handle server errors
      this.server.on('error', (error) => {
        logger.error('❌ Server error:', error);
        process.exit(1);
      });
      
    } catch (error) {
      logger.error('❌ Failed to start server:', error);
      process.exit(1);
    }
  }
}

// Start the bot service
if (require.main === module) {
  const botServer = new BotServer();
  botServer.start().catch((error) => {
    console.error('❌ Failed to start bot service:', error);
    process.exit(1);
  });
}

module.exports = BotServer;
