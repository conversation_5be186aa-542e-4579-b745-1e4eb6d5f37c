import { create } from 'zustand';
import { billingAPI } from '../services/billingAPI';
import toast from 'react-hot-toast';

const useBillingStore = create((set, get) => ({
  // State
  billingInfo: null,
  usageStats: null,
  paymentHistory: [],
  currentPayment: null,
  isLoading: false,
  error: null,

  // Actions
  fetchBillingInfo: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await billingAPI.getBillingInfo();
      const billingInfo = response.data.data;

      set({
        billingInfo,
        isLoading: false,
        error: null
      });

      return { success: true, data: billingInfo };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to fetch billing information';
      
      set({
        billingInfo: null,
        isLoading: false,
        error: errorMessage
      });

      return { success: false, error: errorMessage };
    }
  },

  fetchUsageStats: async () => {
    try {
      const response = await billingAPI.getUsageStats();
      const usageStats = response.data.data;

      set({ usageStats });

      return { success: true, data: usageStats };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to fetch usage statistics';
      console.error('Fetch usage stats error:', error);
      return { success: false, error: errorMessage };
    }
  },

  createPremiumPayment: async (sessionId = null) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await billingAPI.createPremiumPayment({ sessionId });
      const paymentData = response.data.data;

      console.log('Payment response from API:', response.data);
      console.log('Payment data to store:', paymentData);

      set({
        currentPayment: paymentData,
        isLoading: false,
        error: null
      });

      toast.success('Payment QR code generated successfully!');
      return { success: true, data: paymentData };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to create payment';
      
      set({
        currentPayment: null,
        isLoading: false,
        error: errorMessage
      });

      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  checkPaymentStatus: async (paymentId) => {
    try {
      const response = await billingAPI.checkPaymentStatus(paymentId);
      const statusData = response.data.data;

      // Update current payment if it matches
      const currentPayment = get().currentPayment;
      if (currentPayment && currentPayment.paymentId === paymentId) {
        set({
          currentPayment: {
            ...currentPayment,
            status: statusData.status
          }
        });
      }

      return { success: true, data: statusData };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to check payment status';
      console.error('Check payment status error:', error);
      return { success: false, error: errorMessage };
    }
  },

  cancelSubscription: async (reason = null) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await billingAPI.cancelSubscription({ reason });

      set({ isLoading: false });

      // Refresh billing info
      await get().fetchBillingInfo();

      toast.success('Subscription cancelled successfully');
      return { success: true, data: response.data };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to cancel subscription';
      
      set({
        isLoading: false,
        error: errorMessage
      });

      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  fetchPaymentHistory: async (page = 1, limit = 20) => {
    try {
      const response = await billingAPI.getPaymentHistory({ page, limit });
      const historyData = response.data.data;

      set({ paymentHistory: historyData.payments });

      return { success: true, data: historyData };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to fetch payment history';
      console.error('Fetch payment history error:', error);
      return { success: false, error: errorMessage };
    }
  },

  // Utility actions
  clearCurrentPayment: () => {
    set({ currentPayment: null });
  },

  clearError: () => {
    set({ error: null });
  },

  // Computed getters
  isPremium: () => {
    const billingInfo = get().billingInfo;
    return billingInfo?.user?.isPremium || false;
  },

  getSubscriptionExpiry: () => {
    const billingInfo = get().billingInfo;
    return billingInfo?.user?.subscription_expires_at;
  },

  getUsagePercentage: (featureType) => {
    const usageStats = get().usageStats;
    return usageStats?.stats?.[featureType]?.percentage || 0;
  },

  getRemainingUsage: (featureType) => {
    const usageStats = get().usageStats;
    return usageStats?.stats?.[featureType]?.remaining || 0;
  },

  isUsageLimitReached: (featureType) => {
    const usageStats = get().usageStats;
    const remaining = usageStats?.stats?.[featureType]?.remaining;
    return remaining === 0;
  }
}));

export default useBillingStore;
