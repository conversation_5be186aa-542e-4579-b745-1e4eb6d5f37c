#!/bin/bash

# Environment Setup Script for Finwise
# This script helps set up environment variables for different deployment scenarios

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "⚙️  Finwise - Environment Configuration Setup"
echo "============================================="

# Function to validate environment file
validate_env_file() {
    local env_file=$1
    local missing_vars=()
    
    # Required variables
    required_vars=(
        "DB_HOST"
        "DB_PORT" 
        "DB_USER"
        "DB_PASSWORD"
        "DB_NAME"
        "JWT_SECRET"
        "SESSION_SECRET"
        "NODE_ENV"
        "PORT"
        "CORS_ORIGIN"
    )
    
    print_status "Validating environment file: $env_file"
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^${var}=" "$env_file" 2>/dev/null; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -eq 0 ]; then
        print_success "All required environment variables are present"
        return 0
    else
        print_error "Missing required environment variables:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        return 1
    fi
}

# Function to update URLs in environment files
update_urls() {
    local backend_url=$1
    local frontend_url=$2
    
    print_status "Updating URLs in configuration files..."
    
    # Update backend production environment
    if [ -f "Backend/.env.production" ]; then
        sed -i.bak "s|CORS_ORIGIN=.*|CORS_ORIGIN=$frontend_url|g" Backend/.env.production
        sed -i.bak "s|FRONTEND_URL=.*|FRONTEND_URL=$frontend_url|g" Backend/.env.production
        print_success "Updated Backend/.env.production"
    fi
    
    # Update frontend production environment
    if [ -f "Frontend/.env.production" ]; then
        sed -i.bak "s|VITE_API_URL=.*|VITE_API_URL=$backend_url/api|g" Frontend/.env.production
        sed -i.bak "s|VITE_API_BASE_URL=.*|VITE_API_BASE_URL=$backend_url|g" Frontend/.env.production
        sed -i.bak "s|VITE_GOOGLE_OAUTH_URL=.*|VITE_GOOGLE_OAUTH_URL=$backend_url/api/auth/google|g" Frontend/.env.production
        sed -i.bak "s|VITE_FACEBOOK_OAUTH_URL=.*|VITE_FACEBOOK_OAUTH_URL=$backend_url/api/auth/facebook|g" Frontend/.env.production
        print_success "Updated Frontend/.env.production"
    fi
    
    # Update netlify.toml
    if [ -f "netlify.toml" ]; then
        sed -i.bak "s|to = \"https://[^\"]*\"|to = \"$backend_url\"|g" netlify.toml
        sed -i.bak "s|VITE_API_URL = \"https://[^\"]*\"|VITE_API_URL = \"$backend_url/api\"|g" netlify.toml
        sed -i.bak "s|VITE_API_BASE_URL = \"https://[^\"]*\"|VITE_API_BASE_URL = \"$backend_url\"|g" netlify.toml
        print_success "Updated netlify.toml"
    fi
    
    # Update docker-compose.yml
    if [ -f "docker-compose.yml" ]; then
        sed -i.bak "s|CORS_ORIGIN=https://[^\"]*|CORS_ORIGIN=$frontend_url|g" docker-compose.yml
        sed -i.bak "s|FRONTEND_URL=https://[^\"]*|FRONTEND_URL=$frontend_url|g" docker-compose.yml
        print_success "Updated docker-compose.yml"
    fi
    
    # Update deployment scripts
    if [ -f "update-digital-ocean.sh" ]; then
        sed -i.bak "s|-e CORS_ORIGIN=https://[^ ]*|-e CORS_ORIGIN=$frontend_url|g" update-digital-ocean.sh
        sed -i.bak "s|-e FRONTEND_URL=https://[^ ]*|-e FRONTEND_URL=$frontend_url|g" update-digital-ocean.sh
        print_success "Updated update-digital-ocean.sh"
    fi
}

# Main menu
echo ""
echo "What would you like to do?"
echo "1. Validate environment files"
echo "2. Update deployment URLs"
echo "3. Generate new secrets"
echo "4. Setup development environment"
echo "5. Setup production environment"
echo "6. Exit"
echo ""

read -p "Enter your choice (1-6): " choice

case $choice in
    1)
        print_status "Validating environment files..."
        
        # Check backend environments
        if [ -f "Backend/.env" ]; then
            validate_env_file "Backend/.env"
        else
            print_warning "Backend/.env not found"
        fi
        
        if [ -f "Backend/.env.production" ]; then
            validate_env_file "Backend/.env.production"
        else
            print_warning "Backend/.env.production not found"
        fi
        ;;
        
    2)
        read -p "Enter your backend URL (e.g., https://your-app.ondigitalocean.app): " backend_url
        read -p "Enter your frontend URL (e.g., https://your-app.netlify.app): " frontend_url
        
        if [ -n "$backend_url" ] && [ -n "$frontend_url" ]; then
            update_urls "$backend_url" "$frontend_url"
            print_success "URLs updated successfully!"
        else
            print_error "Both URLs are required"
        fi
        ;;
        
    3)
        if [ -f "scripts/generate-secrets.sh" ]; then
            chmod +x scripts/generate-secrets.sh
            ./scripts/generate-secrets.sh
        else
            print_error "generate-secrets.sh not found"
        fi
        ;;
        
    4)
        print_status "Setting up development environment..."
        
        # Copy development environment files
        if [ -f "Backend/.env.example" ]; then
            cp Backend/.env.example Backend/.env
            print_success "Created Backend/.env from example"
        elif [ ! -f "Backend/.env" ]; then
            print_warning "No Backend/.env found. Please create one manually."
        fi
        
        if [ -f "Frontend/.env.example" ]; then
            cp Frontend/.env.example Frontend/.env
            print_success "Created Frontend/.env from example"
        elif [ ! -f "Frontend/.env" ]; then
            print_warning "No Frontend/.env found. Please create one manually."
        fi
        ;;
        
    5)
        print_status "Setting up production environment..."
        
        # Copy production environment files
        if [ -f "Backend/.env.production" ]; then
            cp Backend/.env.production Backend/.env
            print_success "Copied production environment for backend"
        else
            print_error "Backend/.env.production not found"
        fi
        
        print_warning "Remember to:"
        echo "1. Update secrets in Backend/.env"
        echo "2. Verify all URLs are correct"
        echo "3. Test the configuration"
        ;;
        
    6)
        print_status "Exiting..."
        exit 0
        ;;
        
    *)
        print_error "Invalid choice"
        exit 1
        ;;
esac

print_success "Environment setup completed!"
