const { User, Subscription, Payment, UsageTracking } = require('../models');
const BakongPaymentService = require('../services/bakongPaymentService');
const { getUserUsageStats } = require('../middleware/usageLimits');

async function testBillingSystem() {
  console.log('🧪 Starting Billing System Test...\n');

  try {
    // Test 1: Create a test user
    console.log('1️⃣ Creating test user...');
    const testUser = await User.create({
      name: 'Test User',
      email: `test_${Date.now()}@example.com`,
      password_hash: 'test123',
      role: 'Freemium'
    });
    console.log(`✅ Test user created: ${testUser.id}\n`);

    // Test 2: Check initial usage stats
    console.log('2️⃣ Checking initial usage stats...');
    const initialStats = await getUserUsageStats(testUser.id);
    console.log('📊 Initial usage stats:', JSON.stringify(initialStats, null, 2));
    console.log('');

    // Test 3: Simulate usage tracking
    console.log('3️⃣ Simulating usage tracking...');
    await UsageTracking.incrementCurrentMonthUsage(testUser.id, 'ocr_scan');
    await UsageTracking.incrementCurrentMonthUsage(testUser.id, 'ocr_scan');
    await UsageTracking.incrementCurrentMonthUsage(testUser.id, 'excel_import');
    
    const updatedStats = await getUserUsageStats(testUser.id);
    console.log('📊 Updated usage stats:', JSON.stringify(updatedStats, null, 2));
    console.log('');

    // Test 4: Create premium payment
    console.log('4️⃣ Creating premium payment...');
    const bakongService = new BakongPaymentService();
    const paymentResult = await bakongService.createPremiumPayment(testUser.id, 'test-session');
    
    console.log('💳 Payment created successfully!');
    console.log('🔑 Payment ID:', paymentResult.payment.id);
    console.log('🔑 QR MD5 Hash:', paymentResult.paymentData.md5Hash);
    console.log('📋 Bill Number:', paymentResult.paymentData.billNumber);
    console.log('💰 Amount:', paymentResult.paymentData.amount, paymentResult.paymentData.currency);
    console.log('');

    // Test 5: Check payment status
    console.log('5️⃣ Checking payment status...');
    const paymentStatus = await bakongService.checkPaymentStatus(paymentResult.payment.id);
    console.log('📊 Payment status:', paymentStatus.status);
    console.log('');

    // Test 6: Simulate successful payment (for testing)
    console.log('6️⃣ Simulating successful payment...');
    await paymentResult.payment.markAsPaid('test-transaction-id');
    
    // Update subscription
    const subscription = await Subscription.findByPk(paymentResult.subscription.id);
    const now = new Date();
    const expiryDate = new Date();
    expiryDate.setMonth(expiryDate.getMonth() + 1);
    
    await subscription.update({
      status: 'active',
      started_at: now,
      expires_at: expiryDate
    });

    // Upgrade user
    await testUser.upgradeToPremium(expiryDate);
    
    console.log('✅ Payment marked as successful and user upgraded to Premium');
    console.log('📅 Subscription expires at:', expiryDate.toISOString());
    console.log('');

    // Test 7: Check premium usage stats
    console.log('7️⃣ Checking premium usage stats...');
    const premiumStats = await getUserUsageStats(testUser.id);
    console.log('📊 Premium usage stats:', JSON.stringify(premiumStats, null, 2));
    console.log('');

    // Test 8: Test payment monitoring
    console.log('8️⃣ Testing payment monitoring...');
    const monitoringStatus = bakongService.getMonitoringStatus();
    console.log('📊 Monitoring status:', JSON.stringify(monitoringStatus, null, 2));
    console.log('');

    // Test 9: Get user's payment history
    console.log('9️⃣ Getting payment history...');
    const paymentHistory = await bakongService.getUserPayments(testUser.id);
    console.log('📊 Payment history count:', paymentHistory.length);
    console.log('');

    console.log('🎉 All tests completed successfully!');
    console.log('\n📋 Test Summary:');
    console.log(`👤 Test User ID: ${testUser.id}`);
    console.log(`💳 Payment ID: ${paymentResult.payment.id}`);
    console.log(`🔑 QR MD5 Hash: ${paymentResult.paymentData.md5Hash}`);
    console.log(`📋 Bill Number: ${paymentResult.paymentData.billNumber}`);
    console.log('\n🔥 IMPORTANT FOR REAL PAYMENT TEST:');
    console.log(`💰 To test real payment, use this QR MD5 Hash: ${paymentResult.paymentData.md5Hash}`);
    console.log(`💰 Amount: $${paymentResult.paymentData.amount} ${paymentResult.paymentData.currency}`);
    console.log(`📋 Bill Number: ${paymentResult.paymentData.billNumber}`);
    console.log('\n🎯 Next Steps:');
    console.log('1. Use the QR MD5 Hash above to make a real payment');
    console.log('2. The auto-monitor will detect the payment automatically');
    console.log('3. Check the logs for payment success notifications');

    return {
      userId: testUser.id,
      paymentId: paymentResult.payment.id,
      md5Hash: paymentResult.paymentData.md5Hash,
      billNumber: paymentResult.paymentData.billNumber,
      amount: paymentResult.paymentData.amount,
      currency: paymentResult.paymentData.currency
    };

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testBillingSystem()
    .then((result) => {
      console.log('\n✅ Test completed successfully');
      console.log('🔑 Test Result:', result);
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
}

module.exports = testBillingSystem;
