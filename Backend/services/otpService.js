const crypto = require('crypto');
const emailService = require('./emailService');
const smsService = require('./smsService');

class OTPService {
  constructor() {
    // In-memory storage for OTPs (in production, use Redis or database)
    this.otpStorage = new Map();
    this.otpExpiry = 10 * 60 * 1000; // 10 minutes in milliseconds
    this.maxAttempts = 3;
    
    // Clean up expired OTPs every 5 minutes
    setInterval(() => this.cleanupExpiredOTPs(), 5 * 60 * 1000);
  }

  generateOTP(length = 6) {
    // Generate a random OTP with specified length
    const digits = '0123456789';
    let otp = '';
    
    for (let i = 0; i < length; i++) {
      otp += digits[crypto.randomInt(0, digits.length)];
    }
    
    return otp;
  }

  generateOTPKey(identifier, type) {
    // Normalize identifier for consistent key (phones canonicalized, emails lowercase)
    if (!identifier) return `${type}:`;
    let norm = identifier;
    if (this.isEmail(identifier)) {
      norm = identifier.toLowerCase();
    } else if (this.isPhoneNumber(identifier)) {
      try {
        const smsService = require('./smsService');
        const validation = smsService.validatePhoneNumber(identifier);
        if (validation && validation.formatted) norm = validation.formatted;
      } catch (e) {
        // fallback: digits only with + prefix assumption for Cambodia
        norm = '+' + identifier.replace(/\D/g, '');
      }
    }
    return `${type}:${norm}`;
  }

  async sendOTP(identifier, type = 'password_reset', method = 'auto') {
    try {
      const otp = this.generateOTP();
  const key = this.generateOTPKey(identifier, type);
      const expiresAt = Date.now() + this.otpExpiry;

      // Store OTP with metadata
      this.otpStorage.set(key, {
        otp,
        expiresAt,
        attempts: 0,
        maxAttempts: this.maxAttempts,
        type,
        identifier,
        method,
        createdAt: Date.now()
      });

      // Determine sending method
      let result;
      if (method === 'email' || (method === 'auto' && this.isEmail(identifier))) {
        result = await emailService.sendOTP(identifier, otp, type);
        result.method = 'email';
      } else if (method === 'sms' || (method === 'auto' && this.isPhoneNumber(identifier))) {
        const validation = smsService.validatePhoneNumber(identifier);
        if (!validation.isValid) {
          throw new Error('Invalid phone number format');
        }
        result = await smsService.sendOTP(validation.formatted, otp, type);
        result.method = 'sms';
      } else {
        throw new Error('Invalid identifier or method');
      }

      console.log(`✅ OTP sent successfully to ${identifier} via ${result.method}`);
      
      return {
        success: true,
        method: result.method,
        identifier,
        expiresIn: this.otpExpiry / 1000, // seconds
        messageId: result.messageId
      };
    } catch (error) {
      console.error('❌ Failed to send OTP:', error);
      throw new Error(`Failed to send OTP: ${error.message}`);
    }
  }

  async verifyOTP(identifier, otp, type = 'password_reset') {
    try {
  const key = this.generateOTPKey(identifier, type);
      const otpData = this.otpStorage.get(key);

      if (!otpData) {
        return {
          success: false,
          error: 'OTP not found or expired',
          code: 'OTP_NOT_FOUND'
        };
      }

      // Check if OTP is expired
      if (Date.now() > otpData.expiresAt) {
        this.otpStorage.delete(key);
        return {
          success: false,
          error: 'OTP has expired',
          code: 'OTP_EXPIRED'
        };
      }

      // Check if max attempts exceeded
      if (otpData.attempts >= otpData.maxAttempts) {
        this.otpStorage.delete(key);
        return {
          success: false,
          error: 'Maximum verification attempts exceeded',
          code: 'MAX_ATTEMPTS_EXCEEDED'
        };
      }

      // Increment attempts
      otpData.attempts++;

      // Verify OTP
      if (otpData.otp !== otp) {
        this.otpStorage.set(key, otpData);
        return {
          success: false,
          error: 'Invalid OTP',
          code: 'INVALID_OTP',
          attemptsRemaining: otpData.maxAttempts - otpData.attempts
        };
      }

      // OTP is valid, remove it from storage
      this.otpStorage.delete(key);
      
      console.log(`✅ OTP verified successfully for ${identifier}`);
      
      return {
        success: true,
        message: 'OTP verified successfully',
        identifier,
        type
      };
    } catch (error) {
      console.error('❌ Failed to verify OTP:', error);
      throw new Error(`Failed to verify OTP: ${error.message}`);
    }
  }

  async resendOTP(identifier, type = 'password_reset', method = 'auto') {
    try {
      const key = this.generateOTPKey(identifier, type);
      const existingOTP = this.otpStorage.get(key);

      // Check if there's a recent OTP (prevent spam)
      if (existingOTP && (Date.now() - existingOTP.createdAt) < 60000) { // 1 minute
        return {
          success: false,
          error: 'Please wait before requesting a new OTP',
          code: 'TOO_FREQUENT',
          waitTime: 60 - Math.floor((Date.now() - existingOTP.createdAt) / 1000)
        };
      }

      // Send new OTP
      return await this.sendOTP(identifier, type, method);
    } catch (error) {
      console.error('❌ Failed to resend OTP:', error);
      throw new Error(`Failed to resend OTP: ${error.message}`);
    }
  }

  isEmail(identifier) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(identifier);
  }

  isPhoneNumber(identifier) {
    // Basic phone number detection
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
    return phoneRegex.test(identifier);
  }

  cleanupExpiredOTPs() {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, otpData] of this.otpStorage.entries()) {
      if (now > otpData.expiresAt) {
        this.otpStorage.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 Cleaned up ${cleanedCount} expired OTPs`);
    }
  }

  getOTPStats() {
    const now = Date.now();
    let active = 0;
    let expired = 0;

    for (const [key, otpData] of this.otpStorage.entries()) {
      if (now > otpData.expiresAt) {
        expired++;
      } else {
        active++;
      }
    }

    return {
      active,
      expired,
      total: this.otpStorage.size
    };
  }

  // Method to invalidate all OTPs for a specific identifier
  invalidateOTPs(identifier) {
    let invalidatedCount = 0;
    
    for (const [key, otpData] of this.otpStorage.entries()) {
      if (otpData.identifier.toLowerCase() === identifier.toLowerCase()) {
        this.otpStorage.delete(key);
        invalidatedCount++;
      }
    }

    console.log(`🗑️ Invalidated ${invalidatedCount} OTPs for ${identifier}`);
    return invalidatedCount;
  }
}

module.exports = new OTPService();
