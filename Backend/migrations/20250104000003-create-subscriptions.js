'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('subscriptions', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      plan_type: {
        type: Sequelize.ENUM('Freemium', 'Premium'),
        allowNull: false,
        comment: 'Subscription plan type'
      },
      status: {
        type: Sequelize.ENUM('active', 'expired', 'cancelled', 'pending'),
        defaultValue: 'pending',
        allowNull: false,
        comment: 'Current subscription status'
      },
      started_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'When the subscription started'
      },
      expires_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'When the subscription expires'
      },
      auto_renew: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        comment: 'Whether subscription auto-renews'
      },
      price_paid: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
        comment: 'Amount paid for this subscription'
      },
      currency: {
        type: Sequelize.STRING(3),
        defaultValue: 'USD',
        allowNull: false,
        comment: 'Currency of payment'
      },
      payment_method: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: 'Payment method used (e.g., Bakong)'
      },
      payment_reference: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: 'Payment reference or transaction ID'
      },
      cancelled_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'When the subscription was cancelled'
      },
      cancellation_reason: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Reason for cancellation'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // Add indexes
    await queryInterface.addIndex('subscriptions', ['user_id']);
    await queryInterface.addIndex('subscriptions', ['status']);
    await queryInterface.addIndex('subscriptions', ['plan_type']);
    await queryInterface.addIndex('subscriptions', ['expires_at']);
    await queryInterface.addIndex('subscriptions', ['payment_reference']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('subscriptions');
  }
};
