import React, { useState, useEffect } from "react";
import { Helmet } from "react-helmet-async";
import {
  Plus,
  Pie<PERSON><PERSON>,
  Edit,
  Trash2,
  Calendar,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Filter,
} from "lucide-react";
import useBudgetStore from "../store/budgetStore";
import useCategoryStore from "../store/categoryStore";
import BudgetForm from "../components/budgets/BudgetForm";

const Budgets = () => {
  const [showForm, setShowForm] = useState(false);
  const [editingBudget, setEditingBudget] = useState(null);
  const [filterPeriod, setFilterPeriod] = useState("current");

  const {
    budgets,
    isLoading,
    error,
    fetchBudgets,
    deleteBudget,
    getBudgetProgress,
    getBudgetStatus,
    getBudgetRemaining,
    getActiveBudgets,
    clearError,
  } = useBudgetStore();

  const { categories, fetchCategories } = useCategoryStore();

  useEffect(() => {
    fetchBudgets();
    fetchCategories();
  }, []); // empty array ensures it runs only once
  

  const handleCreateBudget = () => {
    setEditingBudget(null);
    setShowForm(true);
  };

  const handleEditBudget = (budget) => {
    setEditingBudget(budget);
    setShowForm(true);
  };

  const handleDeleteBudget = async (budgetId) => {
    if (window.confirm("Are you sure you want to delete this budget?")) {
      await deleteBudget(budgetId);
    }
  };

  const handleFormClose = () => {
    setShowForm(false);
    setEditingBudget(null);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "safe":
        return "text-green-600 bg-green-100";
      case "on-track":
        return "text-blue-600 bg-blue-100";
      case "warning":
        return "text-yellow-600 bg-yellow-100";
      case "exceeded":
        return "text-red-600 bg-red-100";
      case "expired":
        return "text-gray-600 bg-gray-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "safe":
        return <CheckCircle className="w-4 h-4" />;
      case "warning":
        return <AlertTriangle className="w-4 h-4" />;
      case "exceeded":
        return <AlertTriangle className="w-4 h-4" />;
      case "expired":
        return <Clock className="w-4 h-4" />;
      default:
        return <TrendingUp className="w-4 h-4" />;
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getCategoryName = (categoryId) => {
    const category = categories.find((cat) => cat.id === categoryId);
    return category ? category.name : "Unknown Category";
  };

  const filteredBudgets =
    filterPeriod === "current" ? getActiveBudgets() : budgets;

  if (error) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="mx-auto h-12 w-12 text-red-500 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Error Loading Budgets
        </h3>
        <p className="text-gray-500 mb-4">{error}</p>
        <button
          onClick={() => {
            clearError();
            fetchBudgets();
          }}
          className="btn btn-primary"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>Budgets - Finwise</title>
        <meta name="description" content="Create and manage your budgets" />
      </Helmet>

      <div className="space-y-8 min-h-screen p-6 rounded-xl bg-gray-100 dark:bg-[#1E1E2D]">
        {/* Header */}
        <div className="flex items-center justify-between pb-4 border-b border-[#c6d2dd] dark:border-[#3A3B4E]">
          <div>
            <h1 className="text-3xl font-bold text-[#1E3A8A] dark:text-white">
              Budgets
            </h1>
            <p className="text-gray-500 dark:text-gray-400 mt-1">
              Plan and track your spending with smart budgets.
            </p>
          </div>
          <button
            onClick={handleCreateBudget}
            className="flex items-center px-4 py-2 rounded-lg bg-[#1E3A8A] dark:bg-[#8B5CF6] text-white hover:bg-[#162B5A] dark:hover:bg-[#6D28D9] transition shadow"
          >
            <Plus className="w-4 h-4 mr-2" />
            Create Budget
          </button>
        </div>

        {/* Filter */}
        {budgets.length > 0 && (
          <div className="flex items-center gap-3">
            <Filter className="w-4 h-4 text-gray-500 dark:text-gray-400" />
            <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
              Filter:
            </span>
            <select
              value={filterPeriod}
              onChange={(e) => setFilterPeriod(e.target.value)}
              className="text-sm border border-[#c6d2dd] dark:border-[#3A3B4E] bg-white dark:bg-[#252736] text-gray-500 dark:text-gray-400 rounded-md px-3 py-1 focus:ring-2 focus:ring-[#aabbcd]"
            >
              <option value="current">Active Budgets</option>
              <option value="all">All Budgets</option>
            </select>
          </div>
        )}

        {/* Budgets List */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredBudgets.map((budget) => {
            const progress = getBudgetProgress(budget);
            const status = getBudgetStatus(budget);
            const remaining = getBudgetRemaining(budget);

            return (
              <div
                key={budget.id}
                className="rounded-xl shadow hover:shadow-md transition overflow-hidden bg-white dark:bg-[#252736] border border-[#c6d2dd] dark:border-[#3A3B4E]"
              >
                {/* Card Header */}
                <div className="flex items-start justify-between p-5 border-b border-[#c6d2dd] dark:border-[#3A3B4E] bg-[#f9fafb] dark:bg-[#252736]">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-500 dark:text-gray-400">
                      {getCategoryName(budget.category_id)}
                    </h3>
                    <p className="text-sm text-[#8da5bd] dark:text-[#8da5bd] capitalize">
                      {budget.period_type} Budget
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleEditBudget(budget)}
                      className="p-1  text-gray-500 dark:text-gray-400]"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteBudget(budget.id)}
                      className="p-1 text-[#8da5bd] dark:text-[#8da5bd] hover:text-red-600"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {/* Card Body */}
                <div className="p-5 space-y-4">
                  {/* Progress */}
                  <div>
                    <div className="flex justify-between text-sm mb-1 text-gray-500 dark:text-gray-400">
                      <span>Spent</span>
                      <span>{progress.toFixed(1)}%</span>
                    </div>
                    <div className="w-full bg-[#c6d2dd] dark:bg-[#3A3B4E] rounded-full h-2 overflow-hidden">
                      <div
                        className={`h-2 transition-all duration-500 ${
                          progress >= 100
                            ? "bg-red-500"
                            : progress >= 80
                            ? "bg-yellow-500"
                            : "bg-green-500"
                        }`}
                        style={{ width: `${Math.min(progress, 100)}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Details */}
                  <div className="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex justify-between">
                      <span className="flex items-center">
                        <DollarSign className="w-4 h-4 mr-1" /> Spent
                      </span>
                      <span className="font-medium">{formatCurrency(budget.spent_amount || 0)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="flex items-center">
                        <PieChart className="w-4 h-4 mr-1" /> Budget
                      </span>
                      <span className="font-medium">{formatCurrency(budget.amount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="flex items-center">
                        <TrendingUp className="w-4 h-4 mr-1" /> Remaining
                      </span>
                      <span className={`font-medium ${remaining > 0 ? "text-green-600" : "text-red-600"}`}>
                        {formatCurrency(remaining)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1" /> Period
                      </span>
                      <span className="font-medium">
                        {formatDate(budget.period_start)} - {formatDate(budget.period_end)}
                      </span>
                    </div>
                  </div>

                  {/* Status */}
                  <div className="flex justify-between items-center pt-3 border-t border-[#c6d2dd] dark:border-[#3A3B4E]">
                    <span className="text-sm text-[#8da5bd] dark:text-[#8da5bd]">Status</span>
                    <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(status)}`}>
                      {getStatusIcon(status)}
                      {status.replace("-", " ")}
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </>
  );
};

export default Budgets;