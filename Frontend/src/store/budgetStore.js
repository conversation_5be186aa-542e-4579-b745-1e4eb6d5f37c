import { create } from 'zustand';
import { budgetAPI } from '../services/api';
import toast from 'react-hot-toast';

const useBudgetStore = create((set, get) => ({
  // State
  budgets: [],
  isLoading: false,
  error: null,
  currentBudget: null,

  // Actions
  fetchBudgets: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await budgetAPI.getBudgets();
      const budgets = response.data.data || [];

      set({
        budgets,
        isLoading: false,
        error: null
      });

      return { success: true, data: budgets };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to fetch budgets';
      
      set({
        budgets: [],
        isLoading: false,
        error: errorMessage
      });

      return { success: false, error: errorMessage };
    }
  },

  createBudget: async (budgetData) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await budgetAPI.createBudget(budgetData);
      const newBudget = response.data.data;

      set(state => ({
        budgets: [...state.budgets, newBudget],
        isLoading: false,
        error: null
      }));

      toast.success('Budget created successfully!');
      return { success: true, data: newBudget };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to create budget';
      
      set({
        isLoading: false,
        error: errorMessage
      });

      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  updateBudget: async (budgetId, budgetData) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await budgetAPI.updateBudget(budgetId, budgetData);
      const updatedBudget = response.data.data;

      set(state => ({
        budgets: state.budgets.map(budget => 
          budget.id === budgetId ? updatedBudget : budget
        ),
        isLoading: false,
        error: null,
        currentBudget: state.currentBudget?.id === budgetId ? updatedBudget : state.currentBudget
      }));

      toast.success('Budget updated successfully!');
      return { success: true, data: updatedBudget };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to update budget';
      
      set({
        isLoading: false,
        error: errorMessage
      });

      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  deleteBudget: async (budgetId) => {
    set({ isLoading: true, error: null });
    
    try {
      await budgetAPI.deleteBudget(budgetId);

      set(state => ({
        budgets: state.budgets.filter(budget => budget.id !== budgetId),
        isLoading: false,
        error: null,
        currentBudget: state.currentBudget?.id === budgetId ? null : state.currentBudget
      }));

      toast.success('Budget deleted successfully!');
      return { success: true };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to delete budget';
      
      set({
        isLoading: false,
        error: errorMessage
      });

      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  getBudget: async (budgetId) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await budgetAPI.getBudget(budgetId);
      const budget = response.data.data;

      set({
        currentBudget: budget,
        isLoading: false,
        error: null
      });

      return { success: true, data: budget };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to fetch budget';
      
      set({
        currentBudget: null,
        isLoading: false,
        error: errorMessage
      });

      return { success: false, error: errorMessage };
    }
  },

  getBudgetAnalysis: async (budgetId, period) => {
    try {
      const response = await budgetAPI.getBudgetAnalysis(budgetId, period);
      const analysis = response.data.data;

      return { success: true, data: analysis };

    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to fetch budget analysis';
      return { success: false, error: errorMessage };
    }
  },

  // Utility actions
  setCurrentBudget: (budget) => {
    set({ currentBudget: budget });
  },

  clearCurrentBudget: () => {
    set({ currentBudget: null });
  },

  clearError: () => {
    set({ error: null });
  },

  // Computed values
  getBudgetProgress: (budget) => {
    if (!budget || !budget.amount) return 0;
    return Math.min((budget.spent_amount / budget.amount) * 100, 100);
  },

  getBudgetStatus: (budget) => {
    if (!budget) return 'unknown';
    
    const progress = get().getBudgetProgress(budget);
    const now = new Date();
    const endDate = new Date(budget.period_end);
    
    if (progress >= 100) return 'exceeded';
    if (now > endDate) return 'expired';
    if (progress >= 80) return 'warning';
    if (progress >= 50) return 'on-track';
    return 'safe';
  },

  getBudgetRemaining: (budget) => {
    if (!budget) return 0;
    return Math.max(budget.amount - budget.spent_amount, 0);
  },

  getBudgetsByCategory: () => {
    const { budgets } = get();
    return budgets.reduce((acc, budget) => {
      if (!acc[budget.category_id]) {
        acc[budget.category_id] = [];
      }
      acc[budget.category_id].push(budget);
      return acc;
    }, {});
  },

  getActiveBudgets: () => {
    const { budgets } = get();
    const now = new Date();
    return budgets.filter(budget => {
      const endDate = new Date(budget.period_end);
      return endDate >= now;
    });
  },

  getTotalBudgetAmount: () => {
    const { budgets } = get();
    return budgets.reduce((total, budget) => total + budget.amount, 0);
  },

  getTotalSpentAmount: () => {
    const { budgets } = get();
    return budgets.reduce((total, budget) => total + budget.spent_amount, 0);
  }
}));

export default useBudgetStore;
