const { User, Transaction, Budget, Goal } = require('../models');
const bcrypt = require('bcryptjs');
const { Op } = require('sequelize');

class UserController {
  /**
   * Get user profile
   */
  async getProfile(req, res) {
    try {
      const userId = req.user.id;
      
      const user = await User.findByPk(userId, {
        attributes: [
          'id', 'name', 'email', 'phone', 'preferred_language', 
          'preferred_currency', 'profile_picture', 'created_at'
        ]
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          error: 'User not found'
        });
      }

      res.json({
        success: true,
        data: {
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            phone: user.phone,
            preferredLanguage: user.preferred_language || 'en',
            preferredCurrency: user.preferred_currency || 'USD',
            profilePicture: user.profile_picture,
            memberSince: user.created_at
          }
        }
      });

    } catch (error) {
      console.error('Get profile error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch user profile'
      });
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(req, res) {
    try {
      const userId = req.user.id;
      const { name, phone, preferredLanguage, preferredCurrency } = req.body;

      // Validate input
      if (!name || name.trim().length === 0) {
        return res.status(400).json({
          success: false,
          error: 'Name is required'
        });
      }

      // Update user profile
      const [updatedRows] = await User.update({
        name: name.trim(),
        phone: phone || null,
        preferred_language: preferredLanguage || 'en',
        preferred_currency: preferredCurrency || 'USD'
      }, {
        where: { id: userId }
      });

      if (updatedRows === 0) {
        return res.status(404).json({
          success: false,
          error: 'User not found'
        });
      }

      // Fetch updated user data
      const updatedUser = await User.findByPk(userId, {
        attributes: [
          'id', 'name', 'email', 'phone', 'preferred_language', 
          'preferred_currency', 'profile_picture', 'created_at'
        ]
      });

      res.json({
        success: true,
        message: 'Profile updated successfully',
        data: {
          user: {
            id: updatedUser.id,
            name: updatedUser.name,
            email: updatedUser.email,
            phone: updatedUser.phone,
            preferredLanguage: updatedUser.preferred_language,
            preferredCurrency: updatedUser.preferred_currency,
            profilePicture: updatedUser.profile_picture,
            memberSince: updatedUser.created_at
          }
        }
      });

    } catch (error) {
      console.error('Update profile error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update user profile'
      });
    }
  }

  /**
   * Update user settings
   */
  async updateSettings(req, res) {
    try {
      const userId = req.user.id;
      const { 
        preferredLanguage, 
        preferredCurrency, 
        emailNotifications, 
        pushNotifications,
        currentPassword,
        newPassword 
      } = req.body;

      const updateData = {};

      // Update language and currency preferences
      if (preferredLanguage) {
        updateData.preferred_language = preferredLanguage;
      }
      if (preferredCurrency) {
        updateData.preferred_currency = preferredCurrency;
      }

      // Handle password change
      if (currentPassword && newPassword) {
        const user = await User.findByPk(userId);
        
        // Verify current password
        const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password_hash);
        if (!isCurrentPasswordValid) {
          return res.status(400).json({
            success: false,
            error: 'Current password is incorrect'
          });
        }

        // Validate new password
        if (newPassword.length < 6) {
          return res.status(400).json({
            success: false,
            error: 'New password must be at least 6 characters long'
          });
        }

        // Hash new password
        const saltRounds = 12;
        updateData.password_hash = await bcrypt.hash(newPassword, saltRounds);
      }

      // Update user settings
      const [updatedRows] = await User.update(updateData, {
        where: { id: userId }
      });

      if (updatedRows === 0) {
        return res.status(404).json({
          success: false,
          error: 'User not found'
        });
      }

      res.json({
        success: true,
        message: 'Settings updated successfully'
      });

    } catch (error) {
      console.error('Update settings error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update user settings'
      });
    }
  }

  /**
   * Get user statistics
   */
  async getUserStats(req, res) {
    try {
      const userId = req.user.id;

      // Get user join date
      const user = await User.findByPk(userId, {
        attributes: ['created_at']
      });

      // Get transaction counts
      const transactionStats = await Transaction.findAll({
        where: { user_id: userId },
        attributes: [
          [Transaction.sequelize.fn('COUNT', Transaction.sequelize.col('id')), 'total_transactions'],
          [Transaction.sequelize.fn('SUM', Transaction.sequelize.col('money_in')), 'total_income'],
          [Transaction.sequelize.fn('SUM', Transaction.sequelize.col('money_out')), 'total_expenses']
        ],
        raw: true
      });

      // Get budget and goal counts
      const [budgetCount, goalCount] = await Promise.all([
        Budget.count({ where: { user_id: userId } }),
        Goal.count({ where: { user_id: userId } })
      ]);

      // Get recent activity (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const recentActivity = await Transaction.count({
        where: {
          user_id: userId,
          created_at: {
            [Op.gte]: thirtyDaysAgo
          }
        }
      });

      const stats = transactionStats[0] || {};

      res.json({
        success: true,
        data: {
          memberSince: user.created_at,
          totalTransactions: parseInt(stats.total_transactions) || 0,
          totalIncome: parseFloat(stats.total_income) || 0,
          totalExpenses: parseFloat(stats.total_expenses) || 0,
          totalBudgets: budgetCount,
          totalGoals: goalCount,
          recentActivity: recentActivity,
          netWorth: (parseFloat(stats.total_income) || 0) - (parseFloat(stats.total_expenses) || 0)
        }
      });

    } catch (error) {
      console.error('Get user stats error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch user statistics'
      });
    }
  }
}

module.exports = new UserController();
