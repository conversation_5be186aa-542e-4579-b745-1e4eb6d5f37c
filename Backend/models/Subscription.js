'use strict';

module.exports = (sequelize, DataTypes) => {
  const Subscription = sequelize.define('Subscription', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    plan_type: {
      type: DataTypes.ENUM('Freemium', 'Premium'),
      allowNull: false,
      comment: 'Subscription plan type'
    },
    status: {
      type: DataTypes.ENUM('active', 'expired', 'cancelled', 'pending'),
      defaultValue: 'pending',
      allowNull: false,
      comment: 'Current subscription status'
    },
    started_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When the subscription started'
    },
    expires_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When the subscription expires'
    },
    auto_renew: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Whether subscription auto-renews'
    },
    price_paid: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      comment: 'Amount paid for this subscription'
    },
    currency: {
      type: DataTypes.STRING(3),
      defaultValue: 'USD',
      allowNull: false,
      comment: 'Currency of payment'
    },
    payment_method: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: 'Payment method used (e.g., Bakong)'
    },
    payment_reference: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: 'Payment reference or transaction ID'
    },
    cancelled_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When the subscription was cancelled'
    },
    cancellation_reason: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Reason for cancellation'
    }
  }, {
    tableName: 'subscriptions',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['plan_type']
      },
      {
        fields: ['expires_at']
      },
      {
        fields: ['payment_reference']
      }
    ]
  });

  Subscription.associate = function(models) {
    Subscription.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
    Subscription.hasMany(models.Payment, {
      foreignKey: 'subscription_id',
      as: 'payments'
    });
  };

  // Instance methods
  Subscription.prototype.isActive = function() {
    return this.status === 'active' && 
           (!this.expires_at || new Date() < this.expires_at);
  };

  Subscription.prototype.isExpired = function() {
    return this.expires_at && new Date() > this.expires_at;
  };

  Subscription.prototype.daysUntilExpiry = function() {
    if (!this.expires_at) return null;
    const now = new Date();
    const expiry = new Date(this.expires_at);
    const diffTime = expiry - now;
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  // Static methods
  Subscription.getActiveSubscription = async function(userId) {
    return await this.findOne({
      where: {
        user_id: userId,
        status: 'active'
      },
      order: [['created_at', 'DESC']]
    });
  };

  Subscription.getExpiringSubscriptions = async function(daysAhead = 7) {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + daysAhead);

    return await this.findAll({
      where: {
        status: 'active',
        expires_at: {
          [sequelize.Sequelize.Op.lte]: futureDate,
          [sequelize.Sequelize.Op.gte]: new Date()
        }
      },
      include: [{
        model: sequelize.models.User,
        as: 'user'
      }]
    });
  };

  return Subscription;
};
