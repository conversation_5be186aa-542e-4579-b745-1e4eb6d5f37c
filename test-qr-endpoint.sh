#!/bin/bash

echo "🔄 Starting backend server in background..."
cd /home/<USER>/academic/Finwise2/Finwise/Backend

# Start server in background
node server.js &
SERVER_PID=$!

echo "⏰ Waiting for server to start..."
sleep 5

echo "🧪 Testing QR image endpoint..."
curl -I http://localhost:5003/qr/qr_BILL1757002702089.png

echo ""
echo "🧪 Testing with different QR file..."
curl -I http://localhost:5003/qr/qr_BILL1757002702089.png

echo ""
echo "🔍 Listing QR files..."
ls -la /home/<USER>/academic/Finwise2/Finwise/@bakong_js/temp/qr_*.png | head -5

echo ""
echo "🧪 Testing with first available QR file..."
FIRST_QR=$(ls /home/<USER>/academic/Finwise2/Finwise/@bakong_js/temp/qr_*.png | head -1 | xargs basename)
echo "Testing: /qr/$FIRST_QR"
curl -I http://localhost:5003/qr/$FIRST_QR

echo ""
echo "🛑 Stopping server..."
kill $SERVER_PID

echo "✅ Test complete!"
