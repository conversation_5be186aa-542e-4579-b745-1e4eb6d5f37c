import React, { useState, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useDropzone } from "react-dropzone";
import ABA from "../../assets/ABA.webp";
import ACLEDA from "../../assets/ACLEDA.webp";
import {
  Upload,
  FileSpreadsheet,
  AlertCircle,
  Loader2,
  CheckCircle,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { importExportAPI } from "../../services/api";

const ExcelUpload = ({ onPreviewData }) => {
  const { t } = useTranslation();
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const onDrop = useCallback(
    async (acceptedFiles) => {
      const file = acceptedFiles[0];
      if (!file) return;

      // Validate file type
      const allowedTypes = [
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-excel",
        "text/csv",
        "application/csv",
        "application/pdf",
      ];

      if (!allowedTypes.includes(file.type)) {
        toast.error(t("import.fileTypeError"));
        return;
      }

      // Validate file size (10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast.error(t("import.fileSizeError"));
        return;
      }

      setUploading(true);
      setUploadProgress(0);

      try {
        // Simulate upload progress
        const progressInterval = setInterval(() => {
          setUploadProgress((prev) => {
            if (prev >= 90) {
              clearInterval(progressInterval);
              return 90;
            }
            return prev + 10;
          });
        }, 200);

        const formData = new FormData();
        formData.append("file", file);

        const response = await importExportAPI.previewImport(formData);

        clearInterval(progressInterval);
        setUploadProgress(100);

        if (response.success) {
          toast.success(t("import.fileUploaded"));
          onPreviewData(
            {
              ...response,
              fileName: file.name,
              fileSize: file.size,
            },
            file
          );
        } else {
          toast.error(response.error || t("import.importFailed"));
        }
      } catch (error) {
        console.error("Upload error:", error);
        toast.error(error.response?.data?.error || "Failed to upload file");
      } finally {
        setUploading(false);
        setUploadProgress(0);
      }
    },
    [onPreviewData]
  );

  const { getRootProps, getInputProps, isDragActive, isDragReject } =
    useDropzone({
      onDrop,
      accept: {
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
          ".xlsx",
        ],
        "application/vnd.ms-excel": [".xls"],
        "text/csv": [".csv"],
        "application/csv": [".csv"],
        "application/pdf": [".pdf"],
      },
      maxFiles: 1,
      disabled: uploading,
    });

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
          isDragActive && !isDragReject
            ? "border-primary-400 bg-primary-50"
            : isDragReject
            ? "border-red-400 bg-red-50"
            : uploading
            ? "border-gray-300 bg-gray-50 cursor-not-allowed"
            : "border-gray-400 hover:border-primary-400 hover:bg-primary-50 dark:hover:bg-[#252736] dark:hover:border-gray-400"
        }`}
      >
        <input {...getInputProps()} />

        <div className="space-y-4">
          {uploading ? (
            <Loader2 className="w-12 h-12 text-primary-600 mx-auto animate-spin" />
          ) : (
            <FileSpreadsheet className="w-12 h-12 text-gray-400 mx-auto" />
          )}

          <div>
            {uploading ? (
              <div className="space-y-2">
                <p className="text-lg font-medium text-gray-900">
                  Processing file...
                </p>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
                <p className="text-sm text-gray-600">
                  {uploadProgress}% complete
                </p>
              </div>
            ) : isDragActive ? (
              isDragReject ? (
                <p className="text-lg font-medium text-red-600">
                  {t("import.fileTypeError")}
                </p>
              ) : (
                <p className="text-lg font-medium text-primary-600">
                  Drop your file here
                </p>
              )
            ) : (
              <div>
                <p className="text-lg font-medium text-gray-900 dark:text-gray-400">
                  {t("import.dragDropFile")}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {t("import.supportedFormats")}
                </p>
              </div>
            )}
          </div>

          {!uploading && (
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Supported formats: .xlsx, .xls (max 10MB)
            </div>
          )}
        </div>
      </div>

      {/* Instructions */}
      <div className="bg-gray-50 rounded-lg p-6 dark:bg-[#252736]">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          How to Import
        </h3>
        <div className="space-y-3">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-6 h-6 bg-primary-100 dark:text-gray-400 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium dark:text-black text-primary-600">
                1
              </span>
            </div>
            <div>
              <p className="text-sm font-medium dark:text-gray-400 text-gray-900">
                Prepare your file
              </p>
              <p className="text-sm dark:text-gray-400 text-gray-600">
                Export your bank statement from ACLEDA or ABA in Excel format
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-6 h-6 bg-primary-100 dark:text-gray-400 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium dark:text-black text-primary-600">
                2
              </span>
            </div>
            <div>
              <p className="text-sm font-medium dark:text-gray-400 text-gray-900">
                Upload and preview
              </p>
              <p className="text-sm dark:text-gray-400 text-gray-600">
                Upload your file to see a preview of the transactions that will
                be imported
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-6 h-6 bg-primary-100 rounded-full dark:text-gray-400 flex items-center justify-center">
              <span className="text-sm font-medium dark:text-black text-primary-600">
                3
              </span>
            </div>
            <div>
              <p className="text-sm font-medium dark:text-gray-400 text-gray-900">
                Review and import
              </p>
              <p className="text-sm dark:text-gray-400 text-gray-600">
                Review the preview and confirm to import the transactions to
                your account
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Supported Banks */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="border border-gray-200 dark:border-gray-400 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-lg overflow-hidden dark:bg-[#8B5CF6] ring-1 ring-gray-200 dark:ring-gray-700 flex items-center justify-center">
              <img
                src={ACLEDA}
                alt="ACLEDA Bank logo"
                className="w-full h-full object-cover scale-125"
              />
            </div>
            <div>
              <h4 className="font-medium dark:text-gray-400 text-gray-900">
                ACLEDA Bank
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Account statement format
              </p>
            </div>
          </div>
          <div className="mt-3 text-xs dark:text-gray-400 text-gray-500">
            Required columns: Date, Descriptions, Cash Out (Dr), Cash In (Cr)
          </div>
        </div>

        <div className="border border-gray-200 dark:border-gray-400 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-lg overflow-hidden dark:bg-[#8B5CF6] ring-1 ring-gray-200 dark:ring-gray-700 flex items-center justify-center">
              <img
                src={ABA}
                alt="ABA Bank logo"
                className="w-full h-full object-cover scale-125"
              />
            </div>
            <div>
              <h4 className="font-medium text-gray-900 dark:text-gray-400">
                ABA Bank
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Account activity format
              </p>
            </div>
          </div>
          <div className="mt-3 text-xs dark:text-gray-400 text-gray-500">
            Required columns: Date, Transaction Details, Money In, Money Out
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExcelUpload;
