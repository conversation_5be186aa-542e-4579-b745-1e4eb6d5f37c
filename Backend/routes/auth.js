const express = require("express");
const passport = require("passport");
const router = express.Router();

// Import controllers (will be created next)
const authController = require("../controllers/authController");
const { authenticateRefreshToken, authenticateToken } = require("../middleware/auth");

// Import validators (will be created next)
const {
  validateRegister,
  validateLogin,
  validateForgotPassword,
  validateVerifyOTP,
  validateResetPassword,
} = require("../validators/authValidators");

// @route   POST /api/auth/register
// @desc    Register a new user
// @access  Public
router.post("/register", validateRegister, authController.register);

// @route   POST /api/auth/login
// @desc    Login user
// @access  Public
router.post("/login", validateLogin, authController.login);

// @route   POST /api/auth/refresh
// @desc    Refresh access token
// @access  Public (but requires refresh token)
router.post("/refresh", authenticateRefreshToken, authController.refreshToken);

// @route   POST /api/auth/logout
// @desc    Logout user (revoke refresh token)
// @access  Public
router.post("/logout", authController.logout);

// @route   POST /api/auth/forgot-password
// @desc    Send password reset OTP
// @access  Public
router.post(
  "/forgot-password",
  validateForgotPassword,
  authController.forgotPassword
);

// @route   POST /api/auth/verify-password-reset-otp
// @desc    Verify OTP and get reset token
// @access  Public
router.post(
  "/verify-password-reset-otp",
  validateVerifyOTP,
  authController.verifyPasswordResetOTP
);

// @route   POST /api/auth/reset-password
// @desc    Reset password with verified token
// @access  Public
router.post(
  "/reset-password",
  validateResetPassword,
  authController.resetPassword
);

// @route   POST /api/auth/verify-email
// @desc    Verify email address
// @access  Public
router.post("/verify-email", authController.verifyEmail);

// @route   POST /api/auth/resend-verification
// @desc    Resend email verification
// @access  Public
router.post("/resend-verification", authController.resendVerificationEmail);

// @route   GET /api/auth/profile
// @desc    Get user profile (alias for /api/users/profile)
// @access  Private
router.get("/profile", authenticateToken, authController.getProfile);

// @route   PUT /api/auth/profile
// @desc    Update user profile (alias for /api/users/profile)
// @access  Private
router.put("/profile", authenticateToken, authController.updateProfile);

// OAuth routes
// @route   GET /api/auth/google
// @desc    Google OAuth login
// @access  Public
router.get(
  "/google",
  passport.authenticate("google", {
    scope: ["profile", "email"],
  })
);

// @route   GET /api/auth/google/callback
// @desc    Google OAuth callback
// @access  Public
router.get(
  "/google/callback",
  passport.authenticate("google", { failureRedirect: "/login" }),
  authController.googleCallback
);

// @route   GET /api/auth/facebook
// @desc    Facebook OAuth login
// @access  Public
router.get("/facebook", authController.facebookAuth);

// @route   GET /api/auth/facebook/callback
// @desc    Facebook OAuth callback
// @access  Public
router.get("/facebook/callback", authController.facebookCallback);

module.exports = router;
