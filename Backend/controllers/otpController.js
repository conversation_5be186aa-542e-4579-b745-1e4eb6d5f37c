const { User } = require('../models');
const { Op } = require('sequelize');
const otpService = require('../services/otpService');
const { validationResult } = require('express-validator');

// Send OTP for password reset
const sendPasswordResetOTP = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { identifier, method = 'auto' } = req.body;

    // Check if user exists
    let user = await User.findOne({ where: { email: identifier } });
    if (!user) {
      user = await User.findOne({ where: { phone: identifier } });
    }

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found with this email or phone number'
      });
    }

    // Send OTP
    const result = await otpService.sendOTP(identifier, 'password_reset', method);

    res.json({
      success: true,
      message: `OTP sent successfully via ${result.method}`,
      data: {
        method: result.method,
        identifier: result.identifier,
        expiresIn: result.expiresIn
      }
    });
  } catch (error) {
    console.error('Send password reset OTP error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to send OTP'
    });
  }
};

// Verify OTP for password reset
const verifyPasswordResetOTP = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { identifier, otp } = req.body;

    // Verify OTP
    const result = await otpService.verifyOTP(identifier, otp, 'password_reset');

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.error,
        code: result.code,
        attemptsRemaining: result.attemptsRemaining
      });
    }

    // Generate a temporary token for password reset
    const jwt = require('jsonwebtoken');
    const resetToken = jwt.sign(
      { 
        identifier,
        type: 'password_reset',
        timestamp: Date.now()
      },
      process.env.JWT_SECRET,
      { expiresIn: '15m' } // 15 minutes to reset password
    );

    res.json({
      success: true,
      message: 'OTP verified successfully',
      data: {
        resetToken,
        expiresIn: 900 // 15 minutes in seconds
      }
    });
  } catch (error) {
    console.error('Verify password reset OTP error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to verify OTP'
    });
  }
};

// Resend OTP
const resendOTP = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { identifier, type = 'password_reset', method = 'auto' } = req.body;

    // For password reset, check if user exists
    if (type === 'password_reset') {
      let user = await User.findOne({ where: { email: identifier } });
      if (!user) {
        user = await User.findOne({ where: { phone: identifier } });
      }

      if (!user) {
        return res.status(404).json({
          success: false,
          error: 'User not found with this email or phone number'
        });
      }
    }

    // Resend OTP
    const result = await otpService.resendOTP(identifier, type, method);

    if (!result.success) {
      return res.status(429).json({
        success: false,
        error: result.error,
        code: result.code,
        waitTime: result.waitTime
      });
    }

    res.json({
      success: true,
      message: `OTP resent successfully via ${result.method}`,
      data: {
        method: result.method,
        identifier: result.identifier,
        expiresIn: result.expiresIn
      }
    });
  } catch (error) {
    console.error('Resend OTP error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to resend OTP'
    });
  }
};

// Send OTP for email verification
const sendEmailVerificationOTP = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { email } = req.body;

    // Send OTP
    const result = await otpService.sendOTP(email, 'email_verification', 'email');

    res.json({
      success: true,
      message: 'Email verification OTP sent successfully',
      data: {
        method: result.method,
        identifier: result.identifier,
        expiresIn: result.expiresIn
      }
    });
  } catch (error) {
    console.error('Send email verification OTP error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to send email verification OTP'
    });
  }
};

// Verify email verification OTP
const verifyEmailVerificationOTP = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { email, otp } = req.body;

    // Verify OTP
    const result = await otpService.verifyOTP(email, otp, 'email_verification');

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.error,
        code: result.code,
        attemptsRemaining: result.attemptsRemaining
      });
    }

    res.json({
      success: true,
      message: 'Email verified successfully',
      data: {
        email,
        verified: true
      }
    });
  } catch (error) {
    console.error('Verify email verification OTP error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to verify email'
    });
  }
};

// Get OTP service statistics (for admin/debugging)
const getOTPStats = async (req, res) => {
  try {
    const stats = otpService.getOTPStats();
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get OTP stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get OTP statistics'
    });
  }
};

module.exports = {
  sendPasswordResetOTP,
  verifyPasswordResetOTP,
  resendOTP,
  sendEmailVerificationOTP,
  verifyEmailVerificationOTP,
  getOTPStats
};
