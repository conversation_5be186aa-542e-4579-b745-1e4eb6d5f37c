import React, { useState, useEffect } from "react";
import { Outlet } from "react-router-dom";
import Sidebar from "./Sidebar";
import Header from "./Header";
import FeedbackPopup from "../feedback/FeedbackPopup";
import useFeedbackStore from "../../store/feedbackStore";

const Layout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(window.innerWidth >= 1024);
  const [isDesktop, setIsDesktop] = useState(window.innerWidth >= 1024);

  const { checkFeedbackEligibility } = useFeedbackStore();

  useEffect(() => {
    const handleResize = () => {
      const desktop = window.innerWidth >= 1024;
      setIsDesktop(desktop);
      if (desktop) setSidebarOpen(true);
      else setSidebarOpen(false);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Check feedback eligibility on layout mount
  useEffect(() => {
    checkFeedbackEligibility();
  }, []);

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-900 overflow-hidden">
      {/* Sidebar */}
      <Sidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        isDesktop={isDesktop}
      />

      {/* Main content */}
      <div
        className={`flex flex-col flex-1 transition-all duration-300`}
        style={{ marginLeft: isDesktop && sidebarOpen ? 256 : 0 }} // 256px = sidebar width
      >
        {/* Header */}
        <Header
          onMenuClick={() => setSidebarOpen(!sidebarOpen)}
          isSidebarOpen={sidebarOpen}
        />

        {/* Page content */}
        <main className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-800">
          <div className="px-6 pb-8 pt-[100px]">
            <Outlet />
          </div>
        </main>
      </div>

      {/* Feedback Popup */}
      <FeedbackPopup />
    </div>
  );
};

export default Layout;
