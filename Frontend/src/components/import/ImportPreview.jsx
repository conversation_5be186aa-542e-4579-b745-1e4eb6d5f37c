import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  ArrowLeft,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Download,
  Upload,
  FileText,
  DollarSign,
  Calendar,
  Loader2,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { importExportAPI } from "../../services/api";

const ImportPreview = ({ data, originalFile, onImportComplete, onBack }) => {
  const { t } = useTranslation();
  const [importing, setImporting] = useState(false);
  const [activeTab, setActiveTab] = useState("summary");

  const handleImport = async () => {
    if (!originalFile) {
      toast.error(t("import.noFileError"));
      return;
    }

    setImporting(true);

    try {
      // Create FormData with the original file
      const formData = new FormData();
      formData.append("file", originalFile);

      const response = await importExportAPI.executeImport(formData, {
        skipDuplicates: true,
        importMode: "safe",
      });

      if (response.success) {
        toast.success(
          `Successfully imported ${response.importedTransactions} transactions!`
        );
        onImportComplete();
      } else {
        toast.error(response.error || t("import.importFailed"));
      }
    } catch (error) {
      console.error("Import error:", error);
      toast.error(error.response?.data?.error || t("import.importFailed"));
    } finally {
      setImporting(false);
    }
  };

  const formatCurrency = (amount, currency = "USD") => {
    if (!amount) return "-";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (dateString) => {
    if (!dateString) return "-";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const tabs = [
    { id: "summary", name: "Summary", icon: FileText },
    { id: "transactions", name: "Transactions", icon: DollarSign },
    { id: "duplicates", name: "Duplicates", icon: AlertTriangle },
    { id: "errors", name: "Errors", icon: XCircle },
  ];

  return (
    <div className="bg-white shadow rounded-lg">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={onBack}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Import Preview
              </h2>
              <p className="text-sm text-gray-600">
                {data.fileName} • {data.format} Format •{" "}
                {(data.fileSize / 1024).toFixed(1)} KB
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={onBack}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleImport}
              disabled={importing || data.summary?.uniqueTransactions === 0}
              className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {importing ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Importing...</span>
                </>
              ) : (
                <>
                  <Upload className="w-4 h-4" />
                  <span>
                    Import {data.summary?.uniqueTransactions || 0} Transactions
                  </span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg p-4 border">
            <div className="flex items-center">
              <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
              <div>
                <p className="text-sm font-medium text-gray-900">Valid</p>
                <p className="text-lg font-bold text-green-600">
                  {data.summary?.uniqueTransactions || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-4 border">
            <div className="flex items-center">
              <AlertTriangle className="w-5 h-5 text-yellow-500 mr-2" />
              <div>
                <p className="text-sm font-medium text-gray-900">Duplicates</p>
                <p className="text-lg font-bold text-yellow-600">
                  {data.summary?.duplicates || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-4 border">
            <div className="flex items-center">
              <XCircle className="w-5 h-5 text-red-500 mr-2" />
              <div>
                <p className="text-sm font-medium text-gray-900">Errors</p>
                <p className="text-lg font-bold text-red-600">
                  {data.summary?.errors || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-4 border">
            <div className="flex items-center">
              <FileText className="w-5 h-5 text-blue-500 mr-2" />
              <div>
                <p className="text-sm font-medium text-gray-900">Total Rows</p>
                <p className="text-lg font-bold text-blue-600">
                  {data.summary?.totalRows || 0}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 px-6">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                activeTab === tab.id
                  ? "border-primary-500 text-primary-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.name}</span>
              {tab.id === "duplicates" && data.summary?.duplicates > 0 && (
                <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                  {data.summary.duplicates}
                </span>
              )}
              {tab.id === "errors" && data.summary?.errors > 0 && (
                <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                  {data.summary.errors}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {activeTab === "summary" && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">
                  Import Details
                </h3>
                <dl className="space-y-2">
                  <div className="flex justify-between">
                    <dt className="text-sm text-gray-600">Format Detected:</dt>
                    <dd className="text-sm font-medium text-gray-900">
                      {data.format}
                    </dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-sm text-gray-600">File Size:</dt>
                    <dd className="text-sm font-medium text-gray-900">
                      {(data.fileSize / 1024).toFixed(1)} KB
                    </dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-sm text-gray-600">Processing Time:</dt>
                    <dd className="text-sm font-medium text-gray-900">
                      {data.metadata?.importedAt
                        ? new Date(
                            data.metadata.importedAt
                          ).toLocaleTimeString()
                        : "Just now"}
                    </dd>
                  </div>
                </dl>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">
                  Column Mapping
                </h3>
                <div className="space-y-2">
                  {data.metadata?.columnMappings &&
                    Object.entries(data.metadata.columnMappings).map(
                      ([field, index]) => (
                        <div
                          key={field}
                          className="flex justify-between text-sm"
                        >
                          <span className="text-gray-600 capitalize">
                            {field.replace("_", " ")}:
                          </span>
                          <span className="font-medium text-gray-900">
                            Column {index + 1}
                          </span>
                        </div>
                      )
                    )}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === "transactions" && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">
              Valid Transactions ({data.sampleTransactions?.length || 0} shown)
            </h3>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Description
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Currency
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {data.sampleTransactions?.map((transaction, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(transaction.date)}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900">
                        <div
                          className="max-w-xs truncate"
                          title={transaction.details}
                        >
                          {transaction.details || "-"}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        {transaction.money_in ? (
                          <span className="text-green-600 font-medium">
                            +
                            {formatCurrency(
                              transaction.money_in,
                              transaction.currency
                            )}
                          </span>
                        ) : (
                          <span className="text-red-600 font-medium">
                            -
                            {formatCurrency(
                              transaction.money_out,
                              transaction.currency
                            )}
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {transaction.currency || "USD"}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeTab === "duplicates" && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">
              Duplicate Transactions ({data.duplicateTransactions?.length || 0})
            </h3>
            {data.duplicateTransactions?.length > 0 ? (
              <div className="space-y-3">
                {data.duplicateTransactions.map((transaction, index) => (
                  <div
                    key={index}
                    className="bg-yellow-50 border border-yellow-200 rounded-lg p-4"
                  >
                    <div className="flex items-start justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {formatDate(transaction.date)} - {transaction.details}
                        </p>
                        <p className="text-sm text-gray-600">
                          Amount: {transaction.money_in ? "+" : "-"}
                          {formatCurrency(
                            transaction.money_in || transaction.money_out,
                            transaction.currency
                          )}
                        </p>
                      </div>
                      <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                        {transaction.duplicateReason}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-8">
                No duplicate transactions found.
              </p>
            )}
          </div>
        )}

        {activeTab === "errors" && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">
              Error Transactions ({data.errorTransactions?.length || 0})
            </h3>
            {data.errorTransactions?.length > 0 ? (
              <div className="space-y-3">
                {data.errorTransactions.map((transaction, index) => (
                  <div
                    key={index}
                    className="bg-red-50 border border-red-200 rounded-lg p-4"
                  >
                    <div className="flex items-start justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          Row {transaction.rowNumber}
                        </p>
                        <p className="text-sm text-red-600 mt-1">
                          {transaction.error}
                        </p>
                        <p className="text-xs text-gray-500 mt-2">
                          Raw data: {JSON.stringify(transaction.rawData)}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-8">No errors found.</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ImportPreview;
