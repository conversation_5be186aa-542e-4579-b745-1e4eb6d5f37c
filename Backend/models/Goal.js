'use strict';

module.exports = (sequelize, DataTypes) => {
  const Goal = sequelize.define('Goal', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 100]
      }
    },
    name_km: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: 'Khmer translation of goal name'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    description_km: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Khmer translation of description'
    },
    target_amount: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    current_amount: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      defaultValue: 0.00,
      validate: {
        min: 0
      }
    },
    currency: {
      type: DataTypes.STRING(3),
      allowNull: false,
      defaultValue: 'USD',
      validate: {
        len: [3, 3],
        isUppercase: true
      }
    },
    goal_type: {
      type: DataTypes.ENUM('saving', 'debt_payoff', 'investment', 'emergency_fund', 'other'),
      allowNull: false,
      defaultValue: 'saving'
    },
    target_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      validate: {
        isDate: true,
        isAfter: new Date().toISOString().split('T')[0]
      }
    },
    start_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      validate: {
        isDate: true
      }
    },
    monthly_contribution: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
      validate: {
        min: 0
      },
      comment: 'Suggested monthly contribution to reach goal'
    },
    priority: {
      type: DataTypes.ENUM('low', 'medium', 'high'),
      defaultValue: 'medium'
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    is_completed: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    completed_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    icon: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: 'Icon identifier for UI'
    },
    color: {
      type: DataTypes.STRING(7),
      allowNull: true,
      validate: {
        is: /^#[0-9A-F]{6}$/i
      },
      comment: 'Hex color code for UI'
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    milestones: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Array of milestone objects with amount and description'
    },
    alert_settings: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Notification preferences for this goal'
    }
  }, {
    tableName: 'goals',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['goal_type']
      },
      {
        fields: ['target_date']
      },
      {
        fields: ['is_active']
      },
      {
        fields: ['is_completed']
      },
      {
        fields: ['priority']
      }
    ],
    hooks: {
      beforeUpdate: (goal) => {
        if (goal.current_amount >= goal.target_amount && !goal.is_completed) {
          goal.is_completed = true;
          goal.completed_at = new Date();
        }
      }
    }
  });

  Goal.associate = function(models) {
    Goal.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
  };

  // Instance methods
  Goal.prototype.getProgressPercentage = function() {
    return this.target_amount > 0 ? (this.current_amount / this.target_amount) * 100 : 0;
  };

  Goal.prototype.getRemainingAmount = function() {
    return Math.max(0, this.target_amount - this.current_amount);
  };

  Goal.prototype.getDaysRemaining = function() {
    if (!this.target_date) return null;
    const today = new Date();
    const targetDate = new Date(this.target_date);
    const diffTime = targetDate - today;
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  Goal.prototype.getEstimatedCompletionDate = function() {
    if (!this.monthly_contribution || this.monthly_contribution <= 0) return null;
    const remaining = this.getRemainingAmount();
    const monthsNeeded = Math.ceil(remaining / this.monthly_contribution);
    const completionDate = new Date();
    completionDate.setMonth(completionDate.getMonth() + monthsNeeded);
    return completionDate;
  };

  return Goal;
};
