'use strict';

module.exports = (sequelize, DataTypes) => {
  const UsageTracking = sequelize.define('UsageTracking', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    feature_type: {
      type: DataTypes.ENUM('ocr_scan', 'excel_import'),
      allowNull: false,
      comment: 'Type of feature being tracked'
    },
    usage_month: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1,
        max: 12
      },
      comment: 'Month of usage (1-12)'
    },
    usage_year: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 2020
      },
      comment: 'Year of usage'
    },
    usage_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      allowNull: false,
      validate: {
        min: 0
      },
      comment: 'Number of times feature was used this month'
    },
    last_used_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Last time this feature was used'
    }
  }, {
    tableName: 'usage_tracking',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['user_id', 'feature_type', 'usage_month', 'usage_year'],
        name: 'unique_user_feature_month_year'
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['feature_type']
      },
      {
        fields: ['usage_month', 'usage_year']
      }
    ]
  });

  UsageTracking.associate = function(models) {
    UsageTracking.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
  };

  // Instance methods
  UsageTracking.prototype.incrementUsage = async function() {
    this.usage_count += 1;
    this.last_used_at = new Date();
    return await this.save();
  };

  // Static methods
  UsageTracking.getCurrentMonthUsage = async function(userId, featureType) {
    const now = new Date();
    const month = now.getMonth() + 1;
    const year = now.getFullYear();

    const usage = await this.findOne({
      where: {
        user_id: userId,
        feature_type: featureType,
        usage_month: month,
        usage_year: year
      }
    });

    return usage ? usage.usage_count : 0;
  };

  UsageTracking.incrementCurrentMonthUsage = async function(userId, featureType) {
    const now = new Date();
    const month = now.getMonth() + 1;
    const year = now.getFullYear();

    const [usage, created] = await this.findOrCreate({
      where: {
        user_id: userId,
        feature_type: featureType,
        usage_month: month,
        usage_year: year
      },
      defaults: {
        usage_count: 1,
        last_used_at: now
      }
    });

    if (!created) {
      await usage.incrementUsage();
    }

    return usage;
  };

  return UsageTracking;
};
