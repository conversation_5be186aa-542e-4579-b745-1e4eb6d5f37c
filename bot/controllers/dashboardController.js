/**
 * Dashboard Controller for Telegram Bot
 */

const { Markup } = require('telegraf');
const BackendAPI = require('../services/BackendAPI');
const { logger, logUserInteraction } = require('../utils/logger');

class DashboardController {
  // Show balance and overview
  async handleBalance(ctx) {
    try {
      const telegramId = ctx.from.id;
      logUserInteraction(telegramId, 'balance_command');

      const stats = await BackendAPI.getDashboardStats(ctx.user.id);

      if (!stats.success) {
        await ctx.reply('❌ Unable to fetch balance information. Please try again later.');
        return;
      }

      const data = stats.data;
      const totalBalance = data.totalBalance || 0;
      const monthlyIncome = data.monthlyIncome || 0;
      const monthlyExpenses = data.monthlyExpenses || 0;
      const balanceEmoji = totalBalance >= 0 ? '💰' : '⚠️';

      const message = `${balanceEmoji} **Your Financial Overview**\n\n` +
        `💵 **Total Balance:** $${totalBalance.toFixed(2)}\n` +
        `📈 **This Month's Income:** $${monthlyIncome.toFixed(2)}\n` +
        `📉 **This Month's Expenses:** $${monthlyExpenses.toFixed(2)}\n` +
        `📊 **Net This Month:** $${(monthlyIncome - monthlyExpenses).toFixed(2)}\n\n` +
        `📅 **Last Updated:** ${new Date().toLocaleDateString()}`;

      await ctx.reply(message, {
        parse_mode: 'Markdown',
        ...Markup.inlineKeyboard([
          [Markup.button.callback('📊 Detailed Stats', 'detailed_stats')],
          [Markup.button.callback('📱 Recent Transactions', 'recent_transactions')],
          [Markup.button.callback('📈 Monthly Report', 'monthly_report')]
        ])
      });
    } catch (error) {
      logger.error('Error in balance command', { error: error.message, telegramId: ctx.from.id });
      await ctx.reply('❌ Sorry, something went wrong. Please try again later.');
    }
  }

  // Show detailed statistics
  async handleDetailedStats(ctx) {
    try {
      const telegramId = ctx.from.id;
      logUserInteraction(telegramId, 'detailed_stats');

      const stats = await BackendAPI.getDashboardStats(ctx.user.id);

      if (!stats.success) {
        await ctx.editMessageText('❌ Unable to fetch statistics. Please try again later.');
        return;
      }

      const data = stats.data;
      
      let message = `📊 **Detailed Statistics**\n\n`;
      
      // Balance information
      message += `💰 **Balance Overview:**\n`;
      message += `• Total Balance: $${(data.totalBalance || 0).toFixed(2)}\n`;
      message += `• Available: $${(data.availableBalance || data.totalBalance || 0).toFixed(2)}\n\n`;

      // Monthly stats
      message += `📅 **This Month:**\n`;
      message += `• Income: $${(data.monthlyIncome || 0).toFixed(2)}\n`;
      message += `• Expenses: $${(data.monthlyExpenses || 0).toFixed(2)}\n`;
      message += `• Net: $${((data.monthlyIncome || 0) - (data.monthlyExpenses || 0)).toFixed(2)}\n\n`;

      // Transaction counts
      message += `📈 **Activity:**\n`;
      message += `• Total Transactions: ${data.totalTransactions || 0}\n`;
      message += `• This Month: ${data.monthlyTransactions || 0}\n`;
      message += `• This Week: ${data.weeklyTransactions || 0}\n\n`;

      // Top categories
      if (data.topCategories && data.topCategories.length > 0) {
        message += `🏷️ **Top Categories:**\n`;
        data.topCategories.slice(0, 3).forEach((cat, index) => {
          message += `${index + 1}. ${cat.name}: $${cat.amount.toFixed(2)}\n`;
        });
      }

      await ctx.editMessageText(message, {
        parse_mode: 'Markdown',
        ...Markup.inlineKeyboard([
          [Markup.button.callback('📈 Monthly Report', 'monthly_report')],
          [Markup.button.callback('🎯 Goals & Budgets', 'goals_budgets')],
          [Markup.button.callback('⬅️ Back', 'back_to_balance')]
        ])
      });
    } catch (error) {
      logger.error('Error in detailed stats', { error: error.message, telegramId: ctx.from.id });
      await ctx.reply('❌ Sorry, something went wrong. Please try again later.');
    }
  }

  // Show monthly report
  async handleMonthlyReport(ctx) {
    try {
      const telegramId = ctx.from.id;
      logUserInteraction(telegramId, 'monthly_report');

      const now = new Date();
      const month = now.getMonth() + 1;
      const year = now.getFullYear();

      const report = await BackendAPI.getMonthlyReport(ctx.user.id, month, year);

      if (!report.success) {
        await ctx.editMessageText('❌ Unable to fetch monthly report. Please try again later.');
        return;
      }

      const data = report.data;
      const monthName = now.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });

      let message = `📈 **Monthly Report - ${monthName}**\n\n`;

      // Summary
      message += `💰 **Summary:**\n`;
      message += `• Income: $${(data.totalIncome || 0).toFixed(2)}\n`;
      message += `• Expenses: $${(data.totalExpenses || 0).toFixed(2)}\n`;
      message += `• Net: $${((data.totalIncome || 0) - (data.totalExpenses || 0)).toFixed(2)}\n`;
      message += `• Transactions: ${data.transactionCount || 0}\n\n`;

      // Daily average
      const daysInMonth = new Date(year, month, 0).getDate();
      const dailyAvgExpense = (data.totalExpenses || 0) / daysInMonth;
      message += `📊 **Averages:**\n`;
      message += `• Daily Expenses: $${dailyAvgExpense.toFixed(2)}\n`;
      message += `• Per Transaction: $${data.transactionCount > 0 ? ((data.totalExpenses || 0) / data.transactionCount).toFixed(2) : '0.00'}\n\n`;

      // Top expense categories
      if (data.expensesByCategory && data.expensesByCategory.length > 0) {
        message += `🏷️ **Top Expense Categories:**\n`;
        data.expensesByCategory.slice(0, 5).forEach((cat, index) => {
          const percentage = data.totalExpenses > 0 ? ((cat.amount / data.totalExpenses) * 100).toFixed(1) : '0.0';
          message += `${index + 1}. ${cat.category}: $${cat.amount.toFixed(2)} (${percentage}%)\n`;
        });
      }

      await ctx.editMessageText(message, {
        parse_mode: 'Markdown',
        ...Markup.inlineKeyboard([
          [Markup.button.callback('📊 Previous Month', 'prev_month_report')],
          [Markup.button.callback('📈 Year Overview', 'year_overview')],
          [Markup.button.callback('⬅️ Back', 'back_to_balance')]
        ])
      });
    } catch (error) {
      logger.error('Error in monthly report', { error: error.message, telegramId: ctx.from.id });
      await ctx.reply('❌ Sorry, something went wrong. Please try again later.');
    }
  }

  // Show goals and budgets
  async handleGoalsBudgets(ctx) {
    try {
      const telegramId = ctx.from.id;
      logUserInteraction(telegramId, 'goals_budgets');

      const [goalsResponse, budgetsResponse] = await Promise.all([
        BackendAPI.getUserGoals(ctx.user.id),
        BackendAPI.getUserBudgets(ctx.user.id)
      ]);

      let message = `🎯 **Goals & Budgets**\n\n`;

      // Goals section
      if (goalsResponse.success && goalsResponse.data && goalsResponse.data.length > 0) {
        message += `🎯 **Active Goals:**\n`;
        goalsResponse.data.slice(0, 3).forEach((goal, index) => {
          const progress = goal.current_amount && goal.target_amount 
            ? ((goal.current_amount / goal.target_amount) * 100).toFixed(1)
            : '0.0';
          message += `${index + 1}. ${goal.name}\n`;
          message += `   Progress: $${(goal.current_amount || 0).toFixed(2)} / $${(goal.target_amount || 0).toFixed(2)} (${progress}%)\n`;
        });
        message += '\n';
      } else {
        message += `🎯 **Goals:** No active goals\n\n`;
      }

      // Budgets section
      if (budgetsResponse.success && budgetsResponse.data && budgetsResponse.data.length > 0) {
        message += `📋 **Active Budgets:**\n`;
        budgetsResponse.data.slice(0, 3).forEach((budget, index) => {
          const spent = budget.spent || 0;
          const limit = budget.amount || 0;
          const remaining = limit - spent;
          const percentage = limit > 0 ? ((spent / limit) * 100).toFixed(1) : '0.0';
          const status = percentage > 100 ? '🔴' : percentage > 80 ? '🟡' : '🟢';
          
          message += `${index + 1}. ${budget.category} ${status}\n`;
          message += `   Spent: $${spent.toFixed(2)} / $${limit.toFixed(2)} (${percentage}%)\n`;
          message += `   Remaining: $${remaining.toFixed(2)}\n`;
        });
      } else {
        message += `📋 **Budgets:** No active budgets\n`;
      }

      await ctx.editMessageText(message, {
        parse_mode: 'Markdown',
        ...Markup.inlineKeyboard([
          [Markup.button.callback('➕ Create Goal', 'create_goal')],
          [Markup.button.callback('📋 Create Budget', 'create_budget')],
          [Markup.button.callback('⬅️ Back', 'back_to_balance')]
        ])
      });
    } catch (error) {
      logger.error('Error in goals budgets', { error: error.message, telegramId: ctx.from.id });
      await ctx.reply('❌ Sorry, something went wrong. Please try again later.');
    }
  }

  // Handle help command
  async handleHelp(ctx) {
    try {
      const telegramId = ctx.from.id;
      logUserInteraction(telegramId, 'help_command');

      const helpMessage = `❓ **Finwise Bot Help**\n\n` +
        `🔗 **Account Management:**\n` +
        `/start - Welcome and setup\n` +
        `/link - Link your Finwise account\n` +
        `/unlink - Unlink your account\n\n` +
        
        `💰 **Financial Overview:**\n` +
        `/balance - View balance and overview\n` +
        `/stats - Detailed statistics\n` +
        `/report - Monthly report\n\n` +
        
        `📱 **Transactions:**\n` +
        `/add - Add new transaction\n` +
        `/recent - View recent transactions\n` +
        `Quick format: \`expense 25.50 Coffee food\`\n\n` +
        
        `🎯 **Goals & Budgets:**\n` +
        `/goals - View and manage goals\n` +
        `/budgets - View and manage budgets\n\n` +
        
        `⚙️ **Settings:**\n` +
        `/settings - Bot preferences\n` +
        `/help - Show this help message\n\n` +
        
        `💡 **Tips:**\n` +
        `• Use quick transaction format for fast entry\n` +
        `• Set up budgets to track spending\n` +
        `• Create goals to save for specific targets\n` +
        `• Check your monthly report for insights`;

      await ctx.reply(helpMessage, {
        parse_mode: 'Markdown',
        ...Markup.keyboard([
          ['💰 Balance', '📊 Stats'],
          ['➕ Add Transaction', '📱 Recent'],
          ['🎯 Goals', '📋 Budgets'],
          ['⚙️ Settings', '❓ Help']
        ]).resize()
      });
    } catch (error) {
      logger.error('Error in help command', { error: error.message, telegramId: ctx.from.id });
      await ctx.reply('❌ Sorry, something went wrong. Please try again later.');
    }
  }
}

module.exports = new DashboardController();
