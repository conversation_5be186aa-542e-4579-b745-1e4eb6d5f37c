import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import i18n from '../i18n';

const useLanguageStore = create(
  persist(
    (set, get) => ({
      // State
      language: 'en', // 'en', 'km'
      isRTL: false,

      // Actions
      setLanguage: (language) => {
        set({ language });
        i18n.changeLanguage(language);
        get().applyLanguage(language);
      },

      applyLanguage: (language) => {
        const root = window.document.documentElement;
        
        // Set language attribute
        root.setAttribute('lang', language);
        
        // Set direction (Khmer is LTR like English)
        root.setAttribute('dir', 'ltr');
        set({ isRTL: false });
        
        // Apply font family based on language
        if (language === 'km') {
          root.style.setProperty('--font-family', 'Noto Sans Khmer, system-ui, sans-serif');
        } else {
          root.style.setProperty('--font-family', 'Inter, system-ui, sans-serif');
        }
      },

      initializeLanguage: () => {
        const { language, applyLanguage } = get();
        applyLanguage(language);
        i18n.changeLanguage(language);
      },

      toggleLanguage: () => {
        const { language, setLanguage } = get();
        setLanguage(language === 'en' ? 'km' : 'en');
      },

      getLanguageLabel: (lang) => {
        const labels = {
          en: 'English',
          km: 'ខ្មែរ'
        };
        return labels[lang] || lang;
      }
    }),
    {
      name: 'finwise-language',
      partialize: (state) => ({ language: state.language }),
    }
  )
);

export default useLanguageStore;
