import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import useAuthStore from '../../store/authStore';
import toast from 'react-hot-toast';

const TelegramIntegration = () => {
  const { t } = useTranslation();
  const { user, updateUser } = useAuthStore();
  const [isLinked, setIsLinked] = useState(false);
  const [linkingCode, setLinkingCode] = useState('');

  useEffect(() => {
    setIsLinked(user?.telegram_id ? true : false);
  }, [user]);

  const generateLinkingCode = () => {
    // Generate a simple linking code (in production, this would be more secure)
    const code = user?.id || 'USER_ID_NOT_FOUND';
    setLinkingCode(code);
  };

  const unlinkTelegram = async () => {
    try {
      // In a real implementation, you'd call an API to unlink
      toast.success('Telegram account unlinked successfully!');
      updateUser({ ...user, telegram_id: null, telegram_username: null });
      setIsLinked(false);
    } catch (error) {
      toast.error('Failed to unlink Telegram account');
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          🤖 Telegram Integration
        </h3>
        <div className={`px-3 py-1 rounded-full text-sm font-medium ${
          isLinked 
            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
            : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
        }`}>
          {isLinked ? 'Connected' : 'Not Connected'}
        </div>
      </div>

      {isLinked ? (
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
              <span className="text-blue-600 dark:text-blue-400 text-lg">📱</span>
            </div>
            <div>
              <p className="font-medium text-gray-900 dark:text-white">
                @{user?.telegram_username || 'Unknown'}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Telegram ID: {user?.telegram_id}
              </p>
            </div>
          </div>

          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">
              ✅ Connected Successfully!
            </h4>
            <p className="text-sm text-green-700 dark:text-green-300">
              You'll receive notifications and can use bot commands in Telegram.
            </p>
          </div>

          <button
            onClick={unlinkTelegram}
            className="w-full px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200"
          >
            Unlink Telegram Account
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
              🔗 Link Your Telegram Account
            </h4>
            <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
              Connect your Telegram to receive notifications and use bot features.
            </p>
            
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Your User ID (for bot linking):
                </label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={user?.id || ''}
                    readOnly
                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                  />
                  <button
                    onClick={() => {
                      navigator.clipboard.writeText(user?.id || '');
                      toast.success('User ID copied to clipboard!');
                    }}
                    className="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors duration-200"
                  >
                    Copy
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">
              📱 How to Link:
            </h4>
            <ol className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
              <li>1. Open Telegram and search for the Finwise bot</li>
              <li>2. Send <code className="bg-gray-200 dark:bg-gray-600 px-1 rounded">/start</code> to begin</li>
              <li>3. Send <code className="bg-gray-200 dark:bg-gray-600 px-1 rounded">/link</code> to start linking</li>
              <li>4. Choose one of these easy methods:</li>
              <li className="ml-4">• Send your email: <strong>{user?.email}</strong></li>
              <li className="ml-4">• Send your User ID (copied above)</li>
              <li className="ml-4">• Use auto-link if you have the same username</li>
              <li>5. The bot will link automatically!</li>
            </ol>
          </div>

          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">
              💡 Pro Tip
            </h4>
            <p className="text-sm text-yellow-700 dark:text-yellow-300">
              The easiest way is to just send your email address to the bot - it will link automatically!
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default TelegramIntegration;
