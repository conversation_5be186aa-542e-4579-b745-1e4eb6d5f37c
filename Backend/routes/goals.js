const express = require("express");
const router = express.Router();
const { body } = require("express-validator");
const goalController = require("../controllers/goalController");
const { authenticateToken } = require("../middleware/auth");

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Validation rules
const goalValidation = [
  body("name").notEmpty().withMessage("Goal name is required"),
  body("target_amount")
    .isFloat({ min: 0.01 })
    .withMessage("Target amount must be greater than 0"),
  body("current_amount")
    .optional()
    .isFloat({ min: 0 })
    .withMessage("Current amount must be 0 or greater"),
  body("goal_type")
    .optional()
    .isIn(["saving", "debt_payoff", "investment", "emergency_fund", "other"])
    .withMessage("Invalid goal type"),
  body("target_date")
    .optional()
    .isDate()
    .withMessage("Valid target date is required"),
  body("start_date")
    .optional()
    .isDate()
    .withMessage("Valid start date is required"),
  body("monthly_contribution")
    .optional()
    .isFloat({ min: 0 })
    .withMessage("Monthly contribution must be 0 or greater"),
  body("priority")
    .optional()
    .isIn(["low", "medium", "high"])
    .withMessage("Invalid priority level"),
  body("currency")
    .optional()
    .isLength({ min: 3, max: 3 })
    .withMessage("Currency must be 3 characters"),
];

const goalUpdateValidation = [
  body("name").optional().notEmpty().withMessage("Goal name cannot be empty"),
  body("target_amount")
    .optional()
    .isFloat({ min: 0.01 })
    .withMessage("Target amount must be greater than 0"),
  body("current_amount")
    .optional()
    .isFloat({ min: 0 })
    .withMessage("Current amount must be 0 or greater"),
  body("goal_type")
    .optional()
    .isIn(["saving", "debt_payoff", "investment", "emergency_fund", "other"])
    .withMessage("Invalid goal type"),
  body("target_date")
    .optional()
    .isDate()
    .withMessage("Valid target date is required"),
  body("start_date")
    .optional()
    .isDate()
    .withMessage("Valid start date is required"),
  body("monthly_contribution")
    .optional()
    .isFloat({ min: 0 })
    .withMessage("Monthly contribution must be 0 or greater"),
  body("priority")
    .optional()
    .isIn(["low", "medium", "high"])
    .withMessage("Invalid priority level"),
  body("currency")
    .optional()
    .isLength({ min: 3, max: 3 })
    .withMessage("Currency must be 3 characters"),
];

const progressValidation = [
  body("amount")
    .isFloat({ min: 0.01 })
    .withMessage("Amount must be greater than 0"),
  body("operation")
    .isIn(["add", "subtract"])
    .withMessage('Operation must be "add" or "subtract"'),
  body("notes").optional().isString().withMessage("Notes must be a string"),
];

// Routes
router.get("/", goalController.getGoals);
router.get("/:id", goalController.getGoal);
router.post("/", goalValidation, goalController.createGoal);
router.put("/:id", goalUpdateValidation, goalController.updateGoal);
router.patch(
  "/:id/progress",
  progressValidation,
  goalController.updateGoalProgress
);
router.delete("/:id", goalController.deleteGoal);

module.exports = router;
