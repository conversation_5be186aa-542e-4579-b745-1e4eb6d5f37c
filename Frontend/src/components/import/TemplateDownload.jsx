import React, { useState } from 'react';
import { Download, FileSpreadsheet, CheckCircle, Info } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { importExportAPI } from '../../services/api';

const TemplateDownload = () => {
  const [downloading, setDownloading] = useState({});

  const handleDownload = async (format) => {
    setDownloading(prev => ({ ...prev, [format]: true }));
    
    try {
      const response = await importExportAPI.downloadTemplate(format);
      
      // Create blob and download
      const blob = new Blob([response], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
      
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${format.toUpperCase()}_Sample_Template.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      toast.success(`${format.toUpperCase()} template downloaded successfully!`);
    } catch (error) {
      console.error('Download error:', error);
      toast.error(`Failed to download ${format.toUpperCase()} template`);
    } finally {
      setDownloading(prev => ({ ...prev, [format]: false }));
    }
  };

  const templates = [
    {
      id: 'acleda',
      name: 'ACLEDA Bank',
      description: 'Account statement format with CASH IN/OUT columns',
      icon: '🏦',
      color: 'blue',
      columns: [
        'DATE',
        'DESCRIPTIONS', 
        'CASH OUT (Dr)',
        'CASH IN (Cr)',
        'BALANCE'
      ],
      sampleData: [
        {
          date: 'Jul 01, 2025',
          description: 'Paid To MERCHANT NAME',
          cashOut: '1,000.00',
          cashIn: '',
          balance: '7,592.00 KHR'
        },
        {
          date: 'Jul 02, 2025', 
          description: 'Received From SENDER NAME',
          cashOut: '',
          cashIn: '50,000.00',
          balance: '57,592.00 KHR'
        }
      ]
    },
    {
      id: 'aba',
      name: 'ABA Bank',
      description: 'Account activity format with Money In/Out columns',
      icon: '🏛️',
      color: 'green',
      columns: [
        'Date',
        'Transaction Details',
        'Money In',
        'Money Out', 
        'Balance',
        'Ccy'
      ],
      sampleData: [
        {
          date: 'Jul 01, 2025',
          description: 'FUNDS TRANSFERRED TO MERCHANT',
          moneyIn: '',
          moneyOut: '3.50',
          balance: '102.05',
          currency: 'USD'
        },
        {
          date: 'Jul 02, 2025',
          description: 'FUNDS RECEIVED FROM SENDER', 
          moneyIn: '50.00',
          moneyOut: '',
          balance: '150.80',
          currency: 'USD'
        }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900">Download Templates</h2>
        <p className="mt-2 text-gray-600">
          Download sample Excel templates to format your bank statements correctly
        </p>
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {templates.map((template) => (
          <div key={template.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden">
            {/* Header */}
            <div className={`bg-${template.color}-50 border-b border-${template.color}-100 px-6 py-4`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{template.icon}</span>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{template.name}</h3>
                    <p className="text-sm text-gray-600">{template.description}</p>
                  </div>
                </div>
                <button
                  onClick={() => handleDownload(template.id)}
                  disabled={downloading[template.id]}
                  className={`px-4 py-2 bg-${template.color}-600 text-white rounded-lg hover:bg-${template.color}-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 transition-colors`}
                >
                  {downloading[template.id] ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Downloading...</span>
                    </>
                  ) : (
                    <>
                      <Download className="w-4 h-4" />
                      <span>Download</span>
                    </>
                  )}
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6 space-y-4">
              {/* Required Columns */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">Required Columns</h4>
                <div className="flex flex-wrap gap-2">
                  {template.columns.map((column, index) => (
                    <span
                      key={index}
                      className={`px-2 py-1 bg-${template.color}-100 text-${template.color}-800 text-xs rounded-md font-medium`}
                    >
                      {column}
                    </span>
                  ))}
                </div>
              </div>

              {/* Sample Data Preview */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">Sample Data</h4>
                <div className="bg-gray-50 rounded-lg p-3 overflow-x-auto">
                  <table className="min-w-full text-xs">
                    <thead>
                      <tr className="border-b border-gray-200">
                        {template.columns.map((column, index) => (
                          <th key={index} className="text-left py-1 px-2 font-medium text-gray-700">
                            {column}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {template.sampleData.map((row, rowIndex) => (
                        <tr key={rowIndex} className="border-b border-gray-100">
                          {template.id === 'acleda' ? (
                            <>
                              <td className="py-1 px-2 text-gray-600">{row.date}</td>
                              <td className="py-1 px-2 text-gray-600">{row.description}</td>
                              <td className="py-1 px-2 text-gray-600">{row.cashOut}</td>
                              <td className="py-1 px-2 text-gray-600">{row.cashIn}</td>
                              <td className="py-1 px-2 text-gray-600">{row.balance}</td>
                            </>
                          ) : (
                            <>
                              <td className="py-1 px-2 text-gray-600">{row.date}</td>
                              <td className="py-1 px-2 text-gray-600">{row.description}</td>
                              <td className="py-1 px-2 text-gray-600">{row.moneyIn}</td>
                              <td className="py-1 px-2 text-gray-600">{row.moneyOut}</td>
                              <td className="py-1 px-2 text-gray-600">{row.balance}</td>
                              <td className="py-1 px-2 text-gray-600">{row.currency}</td>
                            </>
                          )}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Tips */}
              <div className={`bg-${template.color}-50 border border-${template.color}-200 rounded-lg p-3`}>
                <div className="flex items-start space-x-2">
                  <Info className={`w-4 h-4 text-${template.color}-600 mt-0.5 flex-shrink-0`} />
                  <div className="text-sm">
                    <p className={`font-medium text-${template.color}-900 mb-1`}>Tips for {template.name}</p>
                    <ul className={`text-${template.color}-700 space-y-1 text-xs`}>
                      <li>• Export your statement in Excel format (.xlsx or .xls)</li>
                      <li>• Ensure column headers match the required format</li>
                      <li>• Remove any summary rows or metadata before uploading</li>
                      <li>• Date format should be consistent (MM/DD/YYYY or DD/MM/YYYY)</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* General Instructions */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">General Instructions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Before Uploading</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li className="flex items-start">
                <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                Download the appropriate template for your bank
              </li>
              <li className="flex items-start">
                <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                Copy your transaction data to match the template format
              </li>
              <li className="flex items-start">
                <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                Remove any header information or summary rows
              </li>
              <li className="flex items-start">
                <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                Ensure dates are in a consistent format
              </li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Supported Features</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li className="flex items-start">
                <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                Automatic duplicate detection
              </li>
              <li className="flex items-start">
                <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                Multi-currency support (USD, KHR, EUR, etc.)
              </li>
              <li className="flex items-start">
                <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                Error validation and reporting
              </li>
              <li className="flex items-start">
                <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                Preview before final import
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TemplateDownload;
