import api from './api';

export const billingAPI = {
  // Get billing information
  getBillingInfo: () => api.get('/billing/info'),

  // Create premium payment
  createPremiumPayment: (data) => api.post('/billing/premium', data),

  // Check payment status
  checkPaymentStatus: (paymentId) => api.get(`/billing/payment/${paymentId}/status`),

  // Get usage statistics
  getUsageStats: () => api.get('/billing/usage'),

  // Cancel subscription
  cancelSubscription: (data) => api.post('/billing/cancel', data),

  // Get payment history
  getPaymentHistory: (params) => api.get('/billing/payments', { params }),

  // Get monitoring status
  getMonitoringStatus: () => api.get('/billing/monitoring')
};

export const feedbackAPI = {
  // Check if user can provide feedback
  canProvideFeedback: () => api.get('/feedback/can-provide'),

  // Submit feedback
  submitFeedback: (data) => api.post('/feedback/submit', data),

  // Get feedback history
  getFeedbackHistory: (params) => api.get('/feedback/history', { params }),

  // Skip feedback for this month
  skipFeedback: () => api.post('/feedback/skip'),

  // Get feedback statistics
  getFeedbackStats: (params) => api.get('/feedback/stats', { params })
};
