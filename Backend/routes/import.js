const express = require('express');
const router = express.Router();
const {
  upload,
  previewImport,
  executeImport,
  downloadTemplate,
  getImportHistory,
  getFormats
} = require('../controllers/importController');
const { authenticateToken: auth } = require('../middleware/auth');
const { checkUsageLimit, trackUsage } = require('../middleware/usageLimits');
const rateLimit = require('express-rate-limit');
const multer = require('multer');

// Rate limiting for import endpoints
const importRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 import requests per windowMs
  message: {
    success: false,
    error: 'Too many import requests, please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Rate limiting for template downloads
const templateRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 20, // Limit each IP to 20 template downloads per windowMs
  message: {
    success: false,
    error: 'Too many template download requests, please try again later'
  }
});

/**
 * @route   POST /api/import/preview
 * @desc    Preview Excel file before import
 * @access  Private
 * @body    file: Excel file (multipart/form-data)
 */
router.post('/preview', auth, upload.single('file'), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({
      success: false,
      error: 'No file uploaded. Please select an Excel file.'
    });
  }

  try {
    await previewImport(req, res);
  } catch (error) {
    console.error('Preview import error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to preview import file'
    });
  }
});

/**
 * @route   POST /api/import/execute
 * @desc    Execute Excel import after preview
 * @access  Private
 * @body    file: Excel file (multipart/form-data)
 * @body    skipDuplicates: boolean (optional, default: true)
 * @body    importMode: string (optional, default: 'safe')
 */
router.post('/execute', auth, checkUsageLimit('excel_import'), trackUsage, upload.single('file'), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({
      success: false,
      error: 'No file uploaded. Please select an Excel file.'
    });
  }

  try {
    await executeImport(req, res);
  } catch (error) {
    console.error('Execute import error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to execute import'
    });
  }
});

/**
 * @route   GET /api/import/template/:format
 * @desc    Download sample template for ACLEDA or ABA format
 * @access  Private
 * @params  format: 'acleda' or 'aba'
 */
router.get('/template/:format', auth, async (req, res) => {
  try {
    await downloadTemplate(req, res);
  } catch (error) {
    console.error('Download template error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate template'
    });
  }
});

/**
 * @route   GET /api/import/history
 * @desc    Get import history for user
 * @access  Private
 * @query   page: number (optional, default: 1)
 * @query   limit: number (optional, default: 10)
 */
router.get('/history', auth, async (req, res) => {
  try {
    await getImportHistory(req, res);
  } catch (error) {
    console.error('Get import history error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch import history'
    });
  }
});

/**
 * @route   GET /api/import/formats
 * @desc    Get supported import formats and specifications
 * @access  Private
 */
router.get('/formats', auth, async (req, res) => {
  try {
    await getFormats(req, res);
  } catch (error) {
    console.error('Get formats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch format information'
    });
  }
});

// Legacy route for backward compatibility
router.post('/excel', (req, res) => {
  res.status(301).json({
    success: false,
    message: 'This endpoint has been moved. Please use /api/import/preview or /api/import/execute',
    newEndpoints: {
      preview: '/api/import/preview',
      execute: '/api/import/execute'
    }
  });
});

module.exports = router;
