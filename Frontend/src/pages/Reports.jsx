import React, { useEffect, useState } from "react";
import { Helmet } from "react-helmet-async";
import { Filter, Download, DollarSign, FileText, FileSpreadsheet, Calendar } from "lucide-react";
import { toast } from "react-hot-toast";
import { BarChart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, LineChart, Line, AreaChart, Area, CartesianGrid, Legend } from "recharts";
import { useTranslation } from "react-i18next";

import useTransactionStore from "../store/transactionStore";
import useCategoryStore from "../store/categoryStore";

import { importExportAPI, downloadFile } from "../services/api";
import api from "../services/api";

const Reports = () => {
  const [showFilters, setShowFilters] = useState(false);
  const [showExportDropdown, setShowExportDropdown] = useState(false);
  const [filterCategory, setFilterCategory] = useState("");
  const [filterType, setFilterType] = useState("");
  const [filterDateFrom, setFilterDateFrom] = useState("");
  const [filterDateTo, setFilterDateTo] = useState("");
  const [isReady, setIsReady] = useState(false);
  const [period, setPeriod] = useState('month');
  const [isExporting, setIsExporting] = useState(false);
  const [financialInsights, setFinancialInsights] = useState(null);
  const [isLoadingInsights, setIsLoadingInsights] = useState(false);
  const { t } = useTranslation();

  const { transactions, summary, fetchTransactions } = useTransactionStore();
  const { categories, fetchCategories } = useCategoryStore();

  useEffect(() => {
    const loadAll = async () => {
      await Promise.all([fetchTransactions(), fetchCategories()]);
      await fetchFinancialInsights();
      setIsReady(true);
    };
    loadAll();
  }, []);

  const fetchFinancialInsights = async () => {
    setIsLoadingInsights(true);
    try {
      const params = new URLSearchParams({
        period: period,
        ...(filterDateFrom && { startDate: filterDateFrom }),
        ...(filterDateTo && { endDate: filterDateTo })
      });

      const response = await api.get(`/export/reports/financial-insights?${params}`);
      setFinancialInsights(response.data.data);
    } catch (error) {
      console.error('Error fetching financial insights:', error);
      toast.error('Failed to load financial insights');
    } finally {
      setIsLoadingInsights(false);
    }
  };

  // Refresh insights when filters change
  useEffect(() => {
    if (isReady) {
      fetchFinancialInsights();
    }
  }, [period, filterDateFrom, filterDateTo]);

  if (!isReady)
    return <p className="text-center p-12 text-gray-500">Loading reports...</p>;

  // Filter transactions
  const filteredTransactions = transactions.filter((t) => {
    const matchesCategory = filterCategory
      ? t.category_id === filterCategory
      : true;
    const matchesType =
      filterType === "income"
        ? t.money_in > 0
        : filterType === "expense"
        ? t.money_out > 0
        : true;
    const matchesDateFrom = filterDateFrom
      ? new Date(t.date) >= new Date(filterDateFrom)
      : true;
    const matchesDateTo = filterDateTo
      ? new Date(t.date) <= new Date(filterDateTo)
      : true;

    return matchesCategory && matchesType && matchesDateFrom && matchesDateTo;
  });

  // Data for charts
  const categoryData = categories
    .map((cat) => {
      const totalSpent = filteredTransactions
        .filter((t) => t.category_id === cat.id && t.money_out > 0)
        .reduce((sum, t) => sum + t.money_out, 0);
      return { name: cat.name, spent: totalSpent };
    })
    .filter((d) => d.spent > 0);

  // Monthly trend
  const monthlyData = {};
  filteredTransactions.forEach((t) => {
    const date = new Date(t.date);
    const month = `${date.getFullYear()}-${(date.getMonth() + 1)
      .toString()
      .padStart(2, "0")}`;
    if (!monthlyData[month])
      monthlyData[month] = { month, income: 0, expense: 0 };
    monthlyData[month].income += t.money_in || 0;
    monthlyData[month].expense += t.money_out || 0;
  });
  const monthlyTrend = Object.values(monthlyData).sort((a, b) =>
    a.month.localeCompare(b.month)
  );

  const handleExport = async (type) => {
    try {
      const params = {
        category_id: filterCategory,
        type: filterType,
        start_date: filterDateFrom,
        end_date: filterDateTo,
      };
      let response, filename;

      if (type === "csv") response = await importExportAPI.exportCSV(params);
      else if (type === "excel")
        response = await importExportAPI.exportExcel(params);
      else if (type === "pdf")
        response = await importExportAPI.exportPDF(params);

      filename = `report_${new Date().toISOString().split("T")[0]}.${
        type === "excel" ? "xlsx" : type
      }`;
      downloadFile(response.data, filename);
      toast.success(`Report exported to ${type.toUpperCase()} successfully!`);
    } catch (error) {
      console.error(error);
      toast.error(`Failed to export report to ${type.toUpperCase()}`);
    }
  };

  // Enhanced export functions
  const handleEnhancedExport = async (format) => {
    setIsExporting(true);
    try {
      const exportData = {
        startDate: filterDateFrom || undefined,
        endDate: filterDateTo || undefined,
        period: period,
        category: filterCategory || undefined,
        type: filterType || undefined
      };

      let response;
      if (format === 'excel') {
        response = await api.post('/export/excel/enhanced', exportData, {
          responseType: 'blob'
        });
      } else if (format === 'pdf') {
        response = await api.post('/export/pdf/enhanced', exportData, {
          responseType: 'blob'
        });
      }

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;

      const today = new Date().toISOString().split('T')[0];
      const filename = `finwise-report-${today}.${format === 'excel' ? 'xlsx' : 'pdf'}`;
      link.setAttribute('download', filename);

      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      toast.success(t('messages.reportExported'));
    } catch (error) {
      console.error('Enhanced export error:', error);
      toast.error(`Failed to export ${format.toUpperCase()} report`);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <>
      <Helmet>
        <title>Reports - Finwise</title>
        <meta name="description" content="View financial reports" />
      </Helmet>

      <div className="space-y-6 p-4 md:p-6 bg-gray-100 dark:bg-[#1E1E2D] rounded-xl">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{t('reports.title')}</h1>
            <p className="text-gray-600 dark:text-gray-400">
              Analyze your financial data with detailed reports.
            </p>
          </div>

          {/* Enhanced Export Controls */}
          <div className="flex items-center gap-4">
            {/* Period Selector */}
            <select
              value={period}
              onChange={(e) => setPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="week">{t('reports.week')}</option>
              <option value="month">{t('reports.month')}</option>
              <option value="year">{t('reports.year')}</option>
            </select>

            {/* Export Dropdown */}
            <div className="relative">
              <button
                onClick={() => setShowExportDropdown(!showExportDropdown)}
                disabled={isExporting}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 disabled:opacity-50"
              >
                <Download className="w-4 h-4" />
                {isExporting ? 'Exporting...' : t('reports.exportReport')}
              </button>

              {showExportDropdown && (
                <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-10">
                  <div className="py-1">
                    <button
                      onClick={() => {
                        handleEnhancedExport('excel');
                        setShowExportDropdown(false);
                      }}
                      className="w-full text-left px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
                    >
                      <FileSpreadsheet className="w-4 h-4" />
                      {t('reports.exportAs')} Excel
                    </button>
                    <button
                      onClick={() => {
                        handleEnhancedExport('pdf');
                        setShowExportDropdown(false);
                      }}
                      className="w-full text-left px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
                    >
                      <FileText className="w-4 h-4" />
                      {t('reports.exportAs')} PDF
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Financial Insights Summary */}
        {financialInsights && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Financial Insights</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-blue-600 dark:text-blue-400">Health Score</h3>
                <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                  {financialInsights.summary.healthScore}/100
                </p>
              </div>

              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-green-600 dark:text-green-400">Savings Rate</h3>
                <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                  {financialInsights.summary.savingsRate}%
                </p>
              </div>

              <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-purple-600 dark:text-purple-400">Net Income</h3>
                <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                  ${financialInsights.summary.netIncome.toFixed(2)}
                </p>
              </div>

              <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-orange-600 dark:text-orange-400">Daily Avg Expenses</h3>
                <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                  ${financialInsights.summary.dailyAverageExpenses.toFixed(2)}
                </p>
              </div>
            </div>

            {financialInsights.topSpendingCategories.length > 0 && (
              <div>
                <h3 className="text-md font-medium text-gray-900 dark:text-white mb-3">Top Spending Categories</h3>
                <div className="space-y-2">
                  {financialInsights.topSpendingCategories.slice(0, 3).map((category, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-gray-700 dark:text-gray-300">{category.category}</span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        ${category.expenses.toFixed(2)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Summary Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
          {[
            {
              label: "Total Income",
              value: summary.totalIncome,
              color: "text-green-600",
              bg: "bg-green-100 dark:bg-green-900",
            },
            {
              label: "Total Expenses",
              value: summary.totalExpense,
              color: "text-red-600",
              bg: "bg-red-100 dark:bg-red-900",
            },
            {
              label: "Net Balance",
              value: summary.balance,
              color: summary.balance >= 0 ? "text-green-600" : "text-red-600",
              bg: "bg-gray-200 dark:bg-gray-700",
            },
            {
              label: "Total Transactions",
              value: summary.totalTransactions,
              color: "text-gray-800 dark:text-gray-200",
              bg: "bg-gray-100 dark:bg-gray-800",
            },
          ].map((card, idx) => (
            <div
              key={idx}
              className="flex justify-between items-center p-4 bg-white dark:bg-[#252736] rounded-lg shadow"
            >
              <div>
                <p className="text-sm text-gray-400 dark:text-gray-400">
                  {card.label}
                </p>
                <p className={`text-xl font-semibold ${card.color}`}>
                  ${card.value.toFixed(2)}
                </p>
              </div>
              <div
                className={`h-10 w-10 flex items-center justify-center rounded-lg ${card.bg}`}
              >
                <DollarSign className={`w-5 h-5 ${card.color}`} />
              </div>
            </div>
          ))}
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Expenses by Category */}
          <div className="bg-white dark:bg-[#252736] p-4 rounded-xl shadow">
            <h2 className="text-lg font-medium mb-4 text-gray-800 dark:text-white">
              Expenses by Category
            </h2>
            {categoryData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={categoryData}>
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="spent" fill="#f87171" />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <p className="text-gray-500 dark:text-gray-400 text-center">
                No expenses found for selected filters.
              </p>
            )}
          </div>

          {/* Income vs Expense by Month */}
          <div className="bg-white dark:bg-[#252736] p-4 rounded-xl shadow">
            <h2 className="text-lg font-medium mb-4 text-gray-800 dark:text-white">
              Income vs Expenses (Monthly)
            </h2>
            {monthlyTrend.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={monthlyTrend}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="income" stroke="#22c55e" />
                  <Line type="monotone" dataKey="expense" stroke="#f87171" />
                </LineChart>
              </ResponsiveContainer>
            ) : (
              <p className="text-gray-500 dark:text-gray-400 text-center">
                No data for selected filters.
              </p>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default Reports;
