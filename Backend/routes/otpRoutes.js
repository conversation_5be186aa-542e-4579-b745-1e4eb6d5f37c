const express = require('express');
const { body } = require('express-validator');
const {
  sendPasswordResetOTP,
  verifyPasswordResetOTP,
  resendOTP,
  sendEmailVerificationOTP,
  verifyEmailVerificationOTP,
  getOTPStats
} = require('../controllers/otpController');

const router = express.Router();

// Validation middleware
const validateIdentifier = [
  body('identifier')
    .notEmpty()
    .withMessage('Email or phone number is required')
    .custom((value) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
      
      if (!emailRegex.test(value) && !phoneRegex.test(value)) {
        throw new Error('Please provide a valid email or phone number');
      }
      return true;
    })
];

const validateEmail = [
  body('email')
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail()
];

const validateOTP = [
  body('otp')
    .isLength({ min: 4, max: 8 })
    .withMessage('OTP must be between 4 and 8 digits')
    .isNumeric()
    .withMessage('OTP must contain only numbers')
];

const validateMethod = [
  body('method')
    .optional()
    .isIn(['auto', 'email', 'sms'])
    .withMessage('Method must be auto, email, or sms')
];

const validateType = [
  body('type')
    .optional()
    .isIn(['password_reset', 'email_verification', 'login_verification'])
    .withMessage('Invalid OTP type')
];

// Routes

/**
 * @route   POST /api/otp/send-password-reset
 * @desc    Send OTP for password reset
 * @access  Public
 */
router.post('/send-password-reset', [
  ...validateIdentifier,
  ...validateMethod
], sendPasswordResetOTP);

/**
 * @route   POST /api/otp/verify-password-reset
 * @desc    Verify OTP for password reset
 * @access  Public
 */
router.post('/verify-password-reset', [
  ...validateIdentifier,
  ...validateOTP
], verifyPasswordResetOTP);

/**
 * @route   POST /api/otp/send-email-verification
 * @desc    Send OTP for email verification
 * @access  Public
 */
router.post('/send-email-verification', [
  ...validateEmail
], sendEmailVerificationOTP);

/**
 * @route   POST /api/otp/verify-email-verification
 * @desc    Verify OTP for email verification
 * @access  Public
 */
router.post('/verify-email-verification', [
  ...validateEmail,
  ...validateOTP
], verifyEmailVerificationOTP);

/**
 * @route   POST /api/otp/resend
 * @desc    Resend OTP
 * @access  Public
 */
router.post('/resend', [
  ...validateIdentifier,
  ...validateType,
  ...validateMethod
], resendOTP);

/**
 * @route   GET /api/otp/stats
 * @desc    Get OTP service statistics
 * @access  Public (should be protected in production)
 */
router.get('/stats', getOTPStats);

module.exports = router;
