# Finwise Deployment Guide

## 🚀 Deployed Application URLs

- **Frontend (Netlify)**: https://spiffy-madeleine-3ff7c6.netlify.app
- **Backend (Digital Ocean)**: https://seal-app-mvbaj.ondigitalocean.app
- **API Health Check**: https://seal-app-mvbaj.ondigitalocean.app/api/health

## Overview

This guide will help you deploy the Finwise application with the frontend on Netlify and backend on Digital Ocean using Docker.

## Prerequisites

- GitHub repository with your Finwise code
- Netlify account
- Digital Ocean account
- Docker installed (for local testing)

## Backend Deployment (Digital Ocean)

### Step 1: Prepare Digital Ocean Droplet

1. Create a new Digital Ocean droplet (Ubuntu 20.04 or later recommended)
2. SSH into your droplet
3. Install Docker:
   ```bash
   curl -fsSL https://get.docker.com -o get-docker.sh
   sudo sh get-docker.sh
   sudo usermod -aG docker $USER
   ```

### Step 2: Clone Repository and Build

```bash
# Clone your repository
git clone https://github.com/RiRith69/Finwise.git
cd Finwise

# Build Docker image
docker build -t finwise-backend .
```

### Step 3: Set Environment Variables

Create a `.env` file on your Digital Ocean droplet:

```bash
# Database Configuration
DB_HOST=your_database_host
DB_PORT=your_database_port
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_NAME=your_database_name
DB_DIALECT=mysql

# Security
JWT_SECRET=your_super_secure_jwt_secret_here
SESSION_SECRET=your_super_secure_session_secret_here

# Server Configuration
NODE_ENV=production
PORT=5003

# CORS (Replace with your actual Netlify URL)
CORS_ORIGIN=https://your-app-name.netlify.app
FRONTEND_URL=https://your-app-name.netlify.app

# OAuth Configuration
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### Step 4: Run Docker Container

```bash
# Run the container
docker run -d \
  --name finwise-backend \
  -p 5003:5003 \
  --env-file .env \
  --restart unless-stopped \
  -v $(pwd)/Backend/uploads:/app/uploads \
  finwise-backend:latest
```

### Step 5: Set up Nginx (Optional but recommended)

Create nginx configuration for reverse proxy:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:5003;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## Frontend Deployment (Netlify)

### Step 1: Update Configuration Files

1. **Update `netlify.toml`**: Replace `YOUR_DIGITAL_OCEAN_BACKEND_URL` with your actual Digital Ocean backend URL
2. **Update `Frontend/.env.production`**: Replace `YOUR_DIGITAL_OCEAN_BACKEND_URL` with your actual Digital Ocean backend URL

### Step 2: Deploy to Netlify

1. Connect your GitHub repository to Netlify
2. Set the following build settings in Netlify:

   - **Base directory**: `Frontend`
   - **Build command**: `npm run build`
   - **Publish directory**: `Frontend/dist`

3. Add environment variables in Netlify dashboard:
   ```
   VITE_API_URL=https://your-digital-ocean-backend-url/api
   VITE_API_BASE_URL=https://your-digital-ocean-backend-url
   VITE_NODE_ENV=production
   ```

### Step 3: Update Backend CORS

After getting your Netlify URL, update the backend CORS configuration:

1. SSH into your Digital Ocean droplet
2. Update the `.env` file with your Netlify URL:
   ```bash
   CORS_ORIGIN=https://your-app-name.netlify.app
   FRONTEND_URL=https://your-app-name.netlify.app
   ```
3. Restart the Docker container:
   ```bash
   docker restart finwise-backend
   ```

## Configuration Updates Needed

### When you get your Digital Ocean URL:

1. **In `netlify.toml`**: Replace all instances of `YOUR_DIGITAL_OCEAN_BACKEND_URL` with your actual URL
2. **In `Frontend/.env.production`**: Replace all instances of `YOUR_DIGITAL_OCEAN_BACKEND_URL` with your actual URL
3. **In backend**: Update CORS_ORIGIN environment variable

### When you get your Netlify URL:

1. **In backend `.env`**: Set `CORS_ORIGIN` and `FRONTEND_URL` to your Netlify URL
2. **In `Backend/server.js`**: Uncomment and update the Netlify domain lines in the CORS configuration

## Health Checks

- Backend health check: `GET /api/health`
- Container health check: `GET /health`

## Monitoring

Monitor your application with:

```bash
# Check container status
docker ps

# View logs
docker logs finwise-backend

# Follow logs
docker logs -f finwise-backend
```

## SSL Certificate (Recommended)

For production, set up SSL using Let's Encrypt:

```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

## Troubleshooting

### Common Issues:

1. **CORS errors**: Make sure CORS_ORIGIN is set correctly in backend
2. **Database connection**: Verify database credentials and network access
3. **Environment variables**: Double-check all required environment variables are set
4. **Docker issues**: Check Docker logs for detailed error messages

### Useful Commands:

```bash
# Restart backend
docker restart finwise-backend

# View backend logs
docker logs finwise-backend

# Execute commands in container
docker exec -it finwise-backend sh

# Update and redeploy
git pull
docker build -t finwise-backend .
docker stop finwise-backend
docker rm finwise-backend
# Then run the docker run command again
```
