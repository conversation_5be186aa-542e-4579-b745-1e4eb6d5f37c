'use strict';

module.exports = (sequelize, DataTypes) => {
  const Payment = sequelize.define('Payment', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    subscription_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'subscriptions',
        key: 'id'
      }
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0
      },
      comment: 'Payment amount'
    },
    currency: {
      type: DataTypes.STRING(3),
      defaultValue: 'USD',
      allowNull: false,
      comment: 'Payment currency'
    },
    status: {
      type: DataTypes.ENUM('pending', 'completed', 'failed', 'cancelled', 'refunded'),
      defaultValue: 'pending',
      allowNull: false,
      comment: 'Payment status'
    },
    payment_method: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: 'Payment method (e.g., Bakong)'
    },
    payment_reference: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: 'External payment reference'
    },
    transaction_id: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: 'Payment provider transaction ID'
    },
    qr_code: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'QR code string for payment'
    },
    qr_md5_hash: {
      type: DataTypes.STRING(32),
      allowNull: true,
      comment: 'MD5 hash of QR code for tracking'
    },
    bill_number: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: 'Bill number for payment tracking'
    },
    paid_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When payment was completed'
    },
    failed_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When payment failed'
    },
    failure_reason: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Reason for payment failure'
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Additional payment metadata'
    }
  }, {
    tableName: 'payments',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['subscription_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['payment_reference']
      },
      {
        fields: ['qr_md5_hash']
      },
      {
        fields: ['bill_number']
      },
      {
        fields: ['paid_at']
      }
    ]
  });

  Payment.associate = function(models) {
    Payment.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
    Payment.belongsTo(models.Subscription, {
      foreignKey: 'subscription_id',
      as: 'subscription'
    });
  };

  // Instance methods
  Payment.prototype.markAsPaid = async function(transactionId = null, metadata = null) {
    this.status = 'completed';
    this.paid_at = new Date();
    if (transactionId) this.transaction_id = transactionId;
    if (metadata) this.metadata = metadata;
    return await this.save();
  };

  Payment.prototype.markAsFailed = async function(reason = null) {
    this.status = 'failed';
    this.failed_at = new Date();
    if (reason) this.failure_reason = reason;
    return await this.save();
  };

  // Static methods
  Payment.findByQRHash = async function(qrMd5Hash) {
    return await this.findOne({
      where: {
        qr_md5_hash: qrMd5Hash
      },
      include: [{
        model: sequelize.models.User,
        as: 'user'
      }, {
        model: sequelize.models.Subscription,
        as: 'subscription'
      }]
    });
  };

  Payment.getPendingPayments = async function() {
    return await this.findAll({
      where: {
        status: 'pending'
      },
      include: [{
        model: sequelize.models.User,
        as: 'user'
      }]
    });
  };

  // Enhanced payment history methods
  Payment.getPaymentHistory = async function(userId, options = {}) {
    const { limit = 10, offset = 0, status, startDate, endDate } = options;

    const whereClause = { user_id: userId };

    if (status) {
      whereClause.status = status;
    }

    if (startDate && endDate) {
      whereClause.created_at = {
        [sequelize.Sequelize.Op.between]: [startDate, endDate]
      };
    }

    return await this.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: sequelize.models.User,
          as: 'user',
          attributes: ['id', 'name', 'email']
        },
        {
          model: sequelize.models.Subscription,
          as: 'subscription',
          attributes: ['id', 'plan_type', 'status']
        }
      ],
      order: [['created_at', 'DESC']],
      limit,
      offset
    });
  };

  Payment.getPaymentStats = async function(userId, period = 'month') {
    const now = new Date();
    let startDate;

    switch (period) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    }

    const payments = await this.findAll({
      where: {
        user_id: userId,
        created_at: {
          [sequelize.Sequelize.Op.gte]: startDate
        }
      }
    });

    const stats = {
      totalAmount: 0,
      totalPayments: payments.length,
      successfulPayments: 0,
      failedPayments: 0,
      pendingPayments: 0,
      refundedPayments: 0,
      averageAmount: 0,
      lastPaymentDate: null,
      paymentMethods: {},
      monthlyBreakdown: {}
    };

    payments.forEach(payment => {
      const amount = parseFloat(payment.amount);
      stats.totalAmount += amount;

      switch (payment.status) {
        case 'completed':
          stats.successfulPayments++;
          break;
        case 'failed':
          stats.failedPayments++;
          break;
        case 'pending':
          stats.pendingPayments++;
          break;
        case 'refunded':
          stats.refundedPayments++;
          break;
      }

      // Payment methods breakdown
      if (!stats.paymentMethods[payment.payment_method]) {
        stats.paymentMethods[payment.payment_method] = 0;
      }
      stats.paymentMethods[payment.payment_method]++;

      // Monthly breakdown
      const monthKey = payment.created_at.toISOString().substring(0, 7); // YYYY-MM
      if (!stats.monthlyBreakdown[monthKey]) {
        stats.monthlyBreakdown[monthKey] = { amount: 0, count: 0 };
      }
      stats.monthlyBreakdown[monthKey].amount += amount;
      stats.monthlyBreakdown[monthKey].count++;

      // Last payment date
      if (!stats.lastPaymentDate || payment.created_at > stats.lastPaymentDate) {
        stats.lastPaymentDate = payment.created_at;
      }
    });

    stats.averageAmount = stats.totalPayments > 0 ? stats.totalAmount / stats.totalPayments : 0;
    stats.successRate = stats.totalPayments > 0 ? (stats.successfulPayments / stats.totalPayments) * 100 : 0;

    return stats;
  };

  Payment.getRecentPayments = async function(userId, limit = 5) {
    return await this.findAll({
      where: { user_id: userId },
      include: [
        {
          model: sequelize.models.Subscription,
          as: 'subscription',
          attributes: ['plan_type']
        }
      ],
      order: [['created_at', 'DESC']],
      limit
    });
  };

  return Payment;
};
