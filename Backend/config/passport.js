const passport = require("passport");
const GoogleStrategy = require("passport-google-oauth20").Strategy;
const { User } = require("../models");

// Serialize user for session
passport.serializeUser((user, done) => {
  done(null, user.id);
});

// Deserialize user from session
passport.deserializeUser(async (id, done) => {
  try {
    const user = await User.findByPk(id);
    done(null, user);
  } catch (error) {
    done(error, null);
  }
});

// Google OAuth Strategy
passport.use(
  new GoogleStrategy(
    {
      clientID: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      callbackURL: process.env.GOOGLE_OAUTH_CALLBACK_URL || "/api/auth/google/callback",
    },
    async (accessToken, refreshToken, profile, done) => {
      try {
        // Check if user already exists with this Google ID
        let user = await User.findOne({
          where: {
            provider: "google",
            provider_id: profile.id,
          },
        });

        if (user) {
          // User exists, return the user
          return done(null, user);
        }

        // Check if user exists with same email
        user = await User.findOne({
          where: { email: profile.emails[0].value },
        });

        if (user) {
          // User exists with same email, link Google account
          user.provider = "google";
          user.provider_id = profile.id;
          user.email_verified = true; // Google emails are verified
          await user.save();
          return done(null, user);
        }

        // Create new user
        user = await User.create({
          provider: "google",
          provider_id: profile.id,
          name: profile.displayName,
          email: profile.emails[0].value,
          email_verified: true,
          profile_picture: profile.photos[0]?.value || null,
          preferred_language: "en",
          preferred_currency: "USD",
          // Generate a random password (user won't use it for Google login)
          password_hash: require("crypto").randomBytes(32).toString("hex"),
        });

        return done(null, user);
      } catch (error) {
        console.error("Google OAuth error:", error);
        return done(error, null);
      }
    }
  )
);

module.exports = passport;
