const { UsageTracking, User } = require('../models');

// Usage limits configuration
const USAGE_LIMITS = {
  Freemium: {
    ocr_scan: 10,
    excel_import: 10
  },
  Premium: {
    ocr_scan: -1, // -1 means unlimited
    excel_import: -1
  }
};

/**
 * Middleware to check usage limits for features
 * @param {string} featureType - 'ocr_scan' or 'excel_import'
 */
const checkUsageLimit = (featureType) => {
  return async (req, res, next) => {
    try {
      const userId = req.user.id;
      
      // Get user with current role and subscription status
      const user = await User.findByPk(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          error: 'User not found'
        });
      }

      // Determine user's current role (considering subscription expiration)
      const userRole = user.isPremium() ? 'Premium' : 'Freemium';
      const limit = USAGE_LIMITS[userRole][featureType];

      // If unlimited (Premium users), allow access
      if (limit === -1) {
        req.userRole = userRole;
        req.usageLimit = 'unlimited';
        return next();
      }

      // Check current month usage
      const currentUsage = await UsageTracking.getCurrentMonthUsage(userId, featureType);
      
      // Check if user has exceeded limit
      if (currentUsage >= limit) {
        return res.status(429).json({
          success: false,
          error: `Monthly ${featureType.replace('_', ' ')} limit exceeded`,
          details: {
            currentUsage,
            limit,
            userRole,
            featureType,
            message: `You have used ${currentUsage}/${limit} ${featureType.replace('_', ' ')}s this month. Upgrade to Premium for unlimited access.`
          }
        });
      }

      // Add usage info to request for tracking
      req.userRole = userRole;
      req.usageLimit = limit;
      req.currentUsage = currentUsage;
      req.featureType = featureType;

      next();
    } catch (error) {
      console.error('Usage limit check error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to check usage limits'
      });
    }
  };
};

/**
 * Middleware to track feature usage after successful operation
 */
const trackUsage = async (req, res, next) => {
  // Store original res.json to intercept successful responses
  const originalJson = res.json;
  
  res.json = function(data) {
    // Only track usage if the response was successful and we have tracking info
    if (data.success && req.featureType && req.user) {
      // Track usage asynchronously (don't wait for it)
      UsageTracking.incrementCurrentMonthUsage(req.user.id, req.featureType)
        .then(() => {
          console.log(`✅ Usage tracked: ${req.user.id} - ${req.featureType}`);
        })
        .catch(error => {
          console.error('Failed to track usage:', error);
        });
    }
    
    // Call original res.json
    return originalJson.call(this, data);
  };
  
  next();
};

/**
 * Get usage statistics for a user
 */
const getUserUsageStats = async (userId) => {
  try {
    const user = await User.findByPk(userId);
    if (!user) {
      throw new Error('User not found');
    }

    const userRole = user.isPremium() ? 'Premium' : 'Freemium';
    const limits = USAGE_LIMITS[userRole];

    const stats = {};
    
    for (const featureType of Object.keys(limits)) {
      const currentUsage = await UsageTracking.getCurrentMonthUsage(userId, featureType);
      const limit = limits[featureType];
      
      stats[featureType] = {
        currentUsage,
        limit: limit === -1 ? 'unlimited' : limit,
        remaining: limit === -1 ? 'unlimited' : Math.max(0, limit - currentUsage),
        percentage: limit === -1 ? 0 : Math.min(100, (currentUsage / limit) * 100)
      };
    }

    return {
      userRole,
      subscriptionExpires: user.subscription_expires_at,
      isPremium: user.isPremium(),
      stats
    };
  } catch (error) {
    console.error('Error getting usage stats:', error);
    throw error;
  }
};

/**
 * Check if user can use a specific feature
 */
const canUseFeature = async (userId, featureType) => {
  try {
    const user = await User.findByPk(userId);
    if (!user) return false;

    const userRole = user.isPremium() ? 'Premium' : 'Freemium';
    const limit = USAGE_LIMITS[userRole][featureType];

    if (limit === -1) return true; // Unlimited

    const currentUsage = await UsageTracking.getCurrentMonthUsage(userId, featureType);
    return currentUsage < limit;
  } catch (error) {
    console.error('Error checking feature access:', error);
    return false;
  }
};

module.exports = {
  checkUsageLimit,
  trackUsage,
  getUserUsageStats,
  canUseFeature,
  USAGE_LIMITS
};
